@page "/equipment-component-management"
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Models.Common
@using CoreHub.Shared.Services
@using CoreHub.Shared.Components.Common
@using CoreHub.Shared.Components
@using CoreHub.Shared.Extensions
@using MudBlazor
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.Extensions.Logging
@using System.Security.Claims
@inject IEquipmentComponentService ComponentService
@inject IDialogService DialogService
@inject ISnackbar Snackbar
@inject AuthenticationStateProvider AuthStateProvider
@inject ILogger<EquipmentComponentList> Logger

<PageTitle>设备部件管理</PageTitle>

<PermissionView RequiredPermission="EquipmentComponentManagement.View">
    <ChildContent>
        <MudContainer MaxWidth="MaxWidth.False" Class="pa-4">
            <MudText Typo="Typo.h4" Class="mb-4">设备部件管理</MudText>
    
    <!-- 简化的搜索区域 -->
    <MudPaper Class="pa-4 mb-4" Elevation="2">
        <MudGrid AlignItems="Center">
            <MudItem xs="12" sm="6" md="4">
                <MudTextField @bind-Value="_filter.Keyword"
                             Label="搜索关键词"
                             Placeholder="搜索部件名称、型号、规格..."
                             Variant="Variant.Outlined"
                             Adornment="Adornment.End"
                             AdornmentIcon="Icons.Material.Filled.Search"
                             OnAdornmentClick="SearchComponents"
                             Clearable="true" />
            </MudItem>
            <MudItem xs="12" sm="6" md="2">
                <MudButton StartIcon="Icons.Material.Filled.Search"
                          OnClick="SearchComponents"
                          Color="Color.Primary"
                          Variant="Variant.Filled">
                    搜索
                </MudButton>
            </MudItem>
            <MudItem xs="12" sm="6" md="2">
                <MudButton StartIcon="Icons.Material.Filled.Clear"
                          OnClick="ClearFilters"
                          Color="Color.Secondary"
                          Variant="Variant.Outlined">
                    清除
                </MudButton>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- 操作按钮区域 -->
    <MudPaper Class="pa-4 mb-4" Elevation="1">
        <MudGrid AlignItems="Center">
            <MudItem xs="12" sm="6">
                <MudButtonGroup Variant="Variant.Filled">
                    <MudButton StartIcon="Icons.Material.Filled.Add"
                              Color="Color.Primary"
                              OnClick="OpenCreateDialog">
                        新增部件
                    </MudButton>
                    <MudButton StartIcon="Icons.Material.Filled.FileUpload"
                              Color="Color.Secondary"
                              OnClick="ImportComponents">
                        批量导入
                    </MudButton>
                    <MudButton StartIcon="Icons.Material.Filled.FileDownload"
                              Color="Color.Info"
                              OnClick="ExportComponents">
                        导出数据
                    </MudButton>
                </MudButtonGroup>
            </MudItem>
            <MudItem xs="12" sm="6" Class="d-flex justify-end">
                <MudChip T="string" Icon="Icons.Material.Filled.Inventory"
                        Color="Color.Success"
                        Size="Size.Medium">
                    总库存价值: @_totalStockValue.ToCurrencyString()
                </MudChip>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- 数据表格 -->
    @if (_isLoading)
    {
        <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
        <MudText>正在加载...</MudText>
    }
    else if (_hasError)
    {
        <MudAlert Severity="Severity.Error">
            @_errorMessage
            <MudButton OnClick="LoadComponents" Color="Color.Primary" Variant="Variant.Text">重试</MudButton>
        </MudAlert>
    }
    else if (_pagedResult?.Items?.Any() != true)
    {
        <MudPaper Class="pa-8 text-center">
            <MudText Typo="Typo.h6">暂无设备部件</MudText>
            <MudText>点击下方"新增部件"按钮添加第一个设备部件</MudText>
            <MudButton OnClick="OpenCreateDialog" Color="Color.Primary" Variant="Variant.Filled" Class="mt-4">
                新增部件
            </MudButton>
        </MudPaper>
    }
    else
    {
        <MudTable Items="@_pagedResult?.Items"
                  Hover="true"
                  Striped="true"
                  Dense="true"
                  FixedHeader="true"
                  Height="600px">
            <HeaderContent>
                <MudTh>部件编码</MudTh>
                <MudTh>部件名称</MudTh>
                <MudTh>型号规格</MudTh>
                <MudTh>类型</MudTh>
                <MudTh>供应商</MudTh>
                <MudTh>库存数量</MudTh>
                <MudTh>单价</MudTh>
                <MudTh>库存状态</MudTh>
                <MudTh>更新时间</MudTh>
                <MudTh>操作</MudTh>
            </HeaderContent>
            <RowTemplate>
                <MudTd DataLabel="部件编码">
                    <MudText Typo="Typo.body2" Style="font-family: monospace;">@context.Code</MudText>
                </MudTd>
                <MudTd DataLabel="部件名称">
                    <div class="d-flex align-center">
                        @if (context.IsCritical)
                        {
                            <MudIcon Icon="Icons.Material.Filled.Star" Color="Color.Warning" Size="Size.Small" Class="mr-1" />
                        }
                        <MudText Typo="Typo.body2">@context.Name</MudText>
                    </div>
                </MudTd>
                <MudTd DataLabel="型号规格">
                    <MudText Typo="Typo.body2">@context.Model</MudText>
                    @if (!string.IsNullOrEmpty(context.Specifications))
                    {
                        <MudText Typo="Typo.caption" Color="Color.Secondary">@context.Specifications.Truncate(30)</MudText>
                    }
                </MudTd>
                <MudTd DataLabel="类型">
                    <MudChip T="string" Size="Size.Small" Color="Color.Default">@GetComponentTypeName(context.ComponentType)</MudChip>
                </MudTd>
                <MudTd DataLabel="供应商">@context.Supplier</MudTd>
                <MudTd DataLabel="库存数量">
                    <div class="d-flex align-center">
                        <MudText Typo="Typo.body2">@context.StockQuantity.ToFriendlyString()</MudText>
                        <MudText Typo="Typo.caption" Color="Color.Secondary" Class="ml-1">@context.Unit</MudText>
                    </div>
                </MudTd>
                <MudTd DataLabel="单价">@context.UnitPrice.ToCurrencyString()</MudTd>
                <MudTd DataLabel="库存状态">
                    <MudChip T="string" Size="Size.Small" Color="@GetStockStatusColor(context.StockStatus)">
                        @GetStockStatusName(context.StockStatus)
                    </MudChip>
                </MudTd>
                <MudTd DataLabel="更新时间">@context.UpdatedAt.ToFriendlyString()</MudTd>
                <MudTd DataLabel="操作">
                    <MudButtonGroup Size="Size.Small" Variant="Variant.Text">
                        <MudIconButton Icon="Icons.Material.Filled.Edit"
                                      Color="Color.Primary"
                                      Size="Size.Small"
                                      OnClick="() => OpenEditDialog(context)" />
                        <MudIconButton Icon="Icons.Material.Filled.Visibility"
                                      Color="Color.Info"
                                      Size="Size.Small"
                                      OnClick="() => ViewComponentDetails(context)" />
                        <MudIconButton Icon="Icons.Material.Filled.Delete"
                                      Color="Color.Error"
                                      Size="Size.Small"
                                      OnClick="() => DeleteComponent(context)" />
                    </MudButtonGroup>
                </MudTd>
            </RowTemplate>
        </MudTable>
    }

    <!-- 分页组件 -->
    @if (_pagedResult != null && _pagedResult.Items?.Any() == true)
    {
        <MudPaper Class="pa-4 mt-4">
            <MudGrid AlignItems="Center" Justify="Justify.SpaceBetween">
                <MudItem>
                    <MudText>共 @_pagedResult.TotalCount 条记录，第 @_pagedResult.PageIndex 页，共 @_pagedResult.TotalPages 页</MudText>
                </MudItem>
                <MudItem>
                    <MudButtonGroup>
                        <MudButton Disabled="@(_pagedResult.PageIndex <= 1)" OnClick="() => OnPageChanged(_pagedResult.PageIndex - 1)">上一页</MudButton>
                        <MudButton Disabled="@(_pagedResult.PageIndex >= _pagedResult.TotalPages)" OnClick="() => OnPageChanged(_pagedResult.PageIndex + 1)">下一页</MudButton>
                    </MudButtonGroup>
                </MudItem>
            </MudGrid>
        </MudPaper>
    }
</MudContainer>

@code {
    private EquipmentComponentFilter _filter = new();
    private PagedResult<EquipmentComponent>? _pagedResult;
    private bool _isLoading = true;
    private bool _hasError = false;
    private string? _errorMessage;
    private decimal _totalStockValue = 0;

    // 调试信息
    private string _currentUserName = "";
    private string _currentUserRole = "";
    private List<string> _currentUserPermissions = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadCurrentUserInfo();
        await LoadComponents();
        await LoadStatistics();
    }

    private async Task LoadCurrentUserInfo()
    {
        try
        {
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                _currentUserName = authState.User.FindFirst(ClaimTypes.Name)?.Value ?? "未知用户";

                // 调试：打印所有Claims
                Logger.LogInformation("=== 所有Claims ===");
                foreach (var claim in authState.User.Claims)
                {
                    Logger.LogInformation("Claim Type: {Type}, Value: {Value}", claim.Type, claim.Value);
                }
                Logger.LogInformation("=== Claims结束 ===");

                _currentUserRole = authState.User.FindFirst("RoleName")?.Value ??
                                  authState.User.FindFirst("Role")?.Value ?? "未知角色";
                _currentUserPermissions = authState.User.Claims
                    .Where(c => c.Type == "Permission")
                    .Select(c => c.Value)
                    .ToList();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取用户信息失败: {ex.Message}");
        }
    }

    private async Task LoadComponents()
    {
        try
        {
            _isLoading = true;
            _hasError = false;
            StateHasChanged();

            _pagedResult = await ComponentService.SearchComponentsAsync(_filter);
        }
        catch (Exception ex)
        {
            _hasError = true;
            _errorMessage = ex.Message;
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadStatistics()
    {
        try
        {
            var statistics = await ComponentService.GetComponentStatisticsAsync();
            _totalStockValue = statistics.TotalStockValue;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            // 统计数据加载失败不影响主要功能
            Console.WriteLine($"加载统计数据失败: {ex.Message}");
        }
    }

    private async Task SearchComponents()
    {
        _filter.PageIndex = 1; // 重置到第一页
        await LoadComponents();
    }

    private async Task ClearFilters()
    {
        _filter.Keyword = null;
        _filter.PageIndex = 1;
        await LoadComponents();
    }

    private async Task OnPageChanged(int pageIndex)
    {
        _filter.PageIndex = pageIndex;
        await LoadComponents();
    }

    private async Task OnPageSizeChanged(int pageSize)
    {
        _filter.PageSize = pageSize;
        _filter.PageIndex = 1;
        await LoadComponents();
    }

    private async Task OpenCreateDialog()
    {
        var parameters = new DialogParameters { ["IsEdit"] = false };
        var options = new DialogOptions { CloseButton = true, MaxWidth = MaxWidth.Medium, FullWidth = true };
        
        var dialog = await DialogService.ShowAsync<EquipmentComponentDialog>("新增设备部件", parameters, options);
        var result = await dialog.Result;
        
        if (!result.Canceled)
        {
            await LoadComponents();
            await LoadStatistics();
            Snackbar.Add("设备部件创建成功", Severity.Success);
        }
    }

    private async Task OpenEditDialog(EquipmentComponent component)
    {
        var parameters = new DialogParameters 
        { 
            ["IsEdit"] = true,
            ["ComponentId"] = component.Id
        };
        var options = new DialogOptions { CloseButton = true, MaxWidth = MaxWidth.Medium, FullWidth = true };
        
        var dialog = await DialogService.ShowAsync<EquipmentComponentDialog>("编辑设备部件", parameters, options);
        var result = await dialog.Result;
        
        if (!result.Canceled)
        {
            await LoadComponents();
            await LoadStatistics();
            Snackbar.Add("设备部件更新成功", Severity.Success);
        }
    }

    private async Task ViewComponentDetails(EquipmentComponent component)
    {
        var parameters = new DialogParameters { ["ComponentId"] = component.Id };
        var options = new DialogOptions { CloseButton = true, MaxWidth = MaxWidth.Large, FullWidth = true };
        
        await DialogService.ShowAsync<EquipmentComponentDetailsDialog>("设备部件详情", parameters, options);
    }

    private async Task DeleteComponent(EquipmentComponent component)
    {
        var confirmed = await DialogService.ShowDeleteConfirmAsync(
            component.Name, 
            "删除后将无法恢复，且相关的保养计划和记录也会受到影响。");
        
        if (confirmed)
        {
            try
            {
                await ComponentService.DeleteComponentAsync(component.Id);
                await LoadComponents();
                await LoadStatistics();
                Snackbar.Add("设备部件删除成功", Severity.Success);
            }
            catch (Exception ex)
            {
                Snackbar.Add($"删除失败: {ex.Message}", Severity.Error);
            }
        }
    }

    private async Task ImportComponents()
    {
        // TODO: 实现批量导入功能
        Snackbar.Add("批量导入功能开发中...", Severity.Info);
    }

    private async Task ExportComponents()
    {
        try
        {
            var data = await ComponentService.ExportComponentsAsync();
            // TODO: 实现文件下载
            Snackbar.Add("数据导出成功", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"导出失败: {ex.Message}", Severity.Error);
        }
    }

    private string GetComponentTypeName(int type)
    {
        return type switch
        {
            1 => "机械部件",
            2 => "电气部件", 
            3 => "液压部件",
            4 => "气动部件",
            5 => "传感器",
            6 => "其他",
            _ => "未知"
        };
    }

    private Color GetStockStatusColor(string status)
    {
        return status switch
        {
            "正常" => Color.Success,
            "不足" => Color.Warning,
            "缺货" => Color.Error,
            _ => Color.Default
        };
    }

    private string GetStockStatusName(string status)
    {
        return status ?? "未知";
    }
}
    </ChildContent>
    <NotAuthorized>
        <MudContainer MaxWidth="MaxWidth.False" Class="pa-4">
            <MudAlert Severity="Severity.Warning" Icon="@Icons.Material.Filled.Warning" Class="mb-4">
                <MudText Typo="Typo.h6">权限不足</MudText>
                <MudText>您没有访问设备部件管理页面的权限，请联系系统管理员。</MudText>
                <MudText Class="mt-2">
                    <strong>所需权限：</strong>EquipmentComponentManagement.View
                </MudText>
            </MudAlert>

            @* 调试信息 *@
            <MudExpansionPanels>
                <MudExpansionPanel Text="调试信息">
                    <div>
                        <h6>当前用户信息：</h6>
                        <p><strong>用户名：</strong>@_currentUserName</p>
                        <p><strong>角色：</strong>@_currentUserRole</p>
                        <p><strong>权限数量：</strong>@_currentUserPermissions.Count</p>

                        <h6>用户权限列表：</h6>
                        <ul>
                            @foreach (var permission in _currentUserPermissions)
                            {
                                <li>@permission</li>
                            }
                        </ul>
                    </div>
                </MudExpansionPanel>
            </MudExpansionPanels>
        </MudContainer>
    </NotAuthorized>
</PermissionView>
