@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Models.Common
@using CoreHub.Shared.Services
@using MudBlazor
@inject IMaintenancePlanService PlanService
@inject IEquipmentComponentService ComponentService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudForm @ref="_form" @bind-IsValid="_isFormValid" Model="_plan">
            <MudGrid>
                <MudItem xs="12">
                    <MudTextField @bind-Value="_plan.PlanName"
                                 Label="计划名称"
                                 Variant="Variant.Outlined"
                                 Required="true"
                                 RequiredError="请输入计划名称"
                                 MaxLength="100" />
                </MudItem>
                
                <MudItem xs="12" sm="6">
                    <MudSelect @bind-Value="_plan.ComponentId"
                              Label="设备部件"
                              Variant="Variant.Outlined"
                              Required="true"
                              RequiredError="请选择设备部件"
                              ToStringFunc="@(c => GetComponentDisplayName(c))"
                              SearchBox="true"
                              SearchFunc="@SearchComponents">
                        @foreach (var component in _components)
                        {
                            <MudSelectItem Value="@component.Id">@GetComponentDisplayName(component.Id)</MudSelectItem>
                        }
                    </MudSelect>
                </MudItem>
                
                <MudItem xs="12" sm="6">
                    <MudSelect @bind-Value="_plan.MaintenanceType"
                              Label="保养类型"
                              Variant="Variant.Outlined"
                              Required="true"
                              RequiredError="请选择保养类型">
                        <MudSelectItem Value="1">预防性保养</MudSelectItem>
                        <MudSelectItem Value="2">纠正性保养</MudSelectItem>
                        <MudSelectItem Value="3">预测性保养</MudSelectItem>
                        <MudSelectItem Value="4">紧急维修</MudSelectItem>
                    </MudSelect>
                </MudItem>
                
                <MudItem xs="12" sm="4">
                    <MudSelect @bind-Value="_plan.CycleType"
                              Label="周期类型"
                              Variant="Variant.Outlined"
                              Required="true"
                              RequiredError="请选择周期类型">
                        <MudSelectItem Value="1">天数</MudSelectItem>
                        <MudSelectItem Value="2">周数</MudSelectItem>
                        <MudSelectItem Value="3">月数</MudSelectItem>
                        <MudSelectItem Value="4">年数</MudSelectItem>
                        <MudSelectItem Value="5">使用次数</MudSelectItem>
                        <MudSelectItem Value="6">运行时间</MudSelectItem>
                        <MudSelectItem Value="7">生产数量</MudSelectItem>
                    </MudSelect>
                </MudItem>
                
                <MudItem xs="12" sm="4">
                    <MudNumericField @bind-Value="_plan.CycleValue"
                                    Label="周期数值"
                                    Variant="Variant.Outlined"
                                    Min="1"
                                    Required="true"
                                    RequiredError="请输入周期数值" />
                </MudItem>
                
                <MudItem xs="12" sm="4">
                    <MudSelect @bind-Value="_plan.Priority"
                              Label="优先级"
                              Variant="Variant.Outlined"
                              Required="true"
                              RequiredError="请选择优先级">
                        <MudSelectItem Value="1">低</MudSelectItem>
                        <MudSelectItem Value="2">中</MudSelectItem>
                        <MudSelectItem Value="3">高</MudSelectItem>
                        <MudSelectItem Value="4">紧急</MudSelectItem>
                    </MudSelect>
                </MudItem>
                
                <MudItem xs="12" sm="6">
                    <MudNumericField @bind-Value="_plan.EstimatedDuration"
                                    Label="预计耗时(分钟)"
                                    Variant="Variant.Outlined"
                                    Min="1" />
                </MudItem>
                
                <MudItem xs="12" sm="6">
                    <MudNumericField @bind-Value="_plan.EstimatedCost"
                                    Label="预计费用"
                                    Variant="Variant.Outlined"
                                    Min="0"
                                    Format="F2"
                                    Adornment="Adornment.Start"
                                    AdornmentText="¥" />
                </MudItem>
                
                <MudItem xs="12" sm="6">
                    <MudDatePicker @bind-Date="_plan.StartDate"
                                  Label="开始日期"
                                  Variant="Variant.Outlined"
                                  DateFormat="yyyy-MM-dd" />
                </MudItem>
                
                <MudItem xs="12" sm="6">
                    <MudDatePicker @bind-Date="_plan.NextMaintenanceDate"
                                  Label="下次保养日期"
                                  Variant="Variant.Outlined"
                                  DateFormat="yyyy-MM-dd" />
                </MudItem>
                
                <MudItem xs="12">
                    <MudTextField @bind-Value="_plan.MaintenanceContent"
                                 Label="保养内容"
                                 Variant="Variant.Outlined"
                                 Lines="3"
                                 MaxLength="1000" />
                </MudItem>
                
                <MudItem xs="12">
                    <MudTextField @bind-Value="_plan.RequiredTools"
                                 Label="所需工具"
                                 Variant="Variant.Outlined"
                                 Lines="2"
                                 MaxLength="500" />
                </MudItem>
                
                <MudItem xs="12">
                    <MudTextField @bind-Value="_plan.SafetyRequirements"
                                 Label="安全要求"
                                 Variant="Variant.Outlined"
                                 Lines="2"
                                 MaxLength="500" />
                </MudItem>
                
                <MudItem xs="12">
                    <MudGrid>
                        <MudItem xs="12" sm="4">
                            <MudSwitch T="bool" @bind-Checked="_plan.IsActive"
                                      Label="启用状态"
                                      Color="Color.Success" />
                        </MudItem>
                        <MudItem xs="12" sm="4">
                            <MudSwitch T="bool" @bind-Checked="_plan.AutoCreateRecord"
                                      Label="自动创建记录"
                                      Color="Color.Info" />
                        </MudItem>
                        <MudItem xs="12" sm="4">
                            <MudSwitch T="bool" @bind-Checked="_plan.SendReminder"
                                      Label="发送提醒"
                                      Color="Color.Warning" />
                        </MudItem>
                    </MudGrid>
                </MudItem>
                
                <MudItem xs="12">
                    <MudTextField @bind-Value="_plan.Description"
                                 Label="备注说明"
                                 Variant="Variant.Outlined"
                                 Lines="3"
                                 MaxLength="500" />
                </MudItem>
            </MudGrid>
        </MudForm>
    </DialogContent>
    
    <DialogActions>
        <MudButton OnClick="Cancel" Variant="Variant.Text">取消</MudButton>
        <MudButton OnClick="Save" 
                  Variant="Variant.Filled" 
                  Color="Color.Primary"
                  Disabled="!_isFormValid || _isSaving"
                  StartIcon="@(IsEdit ? Icons.Material.Filled.Save : Icons.Material.Filled.Add)">
            @if (_isSaving)
            {
                <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                <span class="ml-2">保存中...</span>
            }
            else
            {
                @(IsEdit ? "保存" : "创建")
            }
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public bool IsEdit { get; set; }
    [Parameter] public int PlanId { get; set; }

    private MudForm _form = null!;
    private bool _isFormValid;
    private bool _isSaving;
    private MaintenancePlan _plan = new();
    private List<EquipmentComponent> _components = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadComponents();
        
        if (IsEdit && PlanId > 0)
        {
            try
            {
                var plan = await PlanService.GetPlanByIdAsync(PlanId);
                if (plan != null)
                {
                    _plan = plan;
                }
                else
                {
                    Snackbar.Add("保养计划不存在", Severity.Error);
                    MudDialog.Cancel();
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"加载保养计划失败: {ex.Message}", Severity.Error);
                MudDialog.Cancel();
            }
        }
        else
        {
            // 新建时设置默认值
            _plan.IsActive = true;
            _plan.MaintenanceType = 1;
            _plan.CycleType = 1;
            _plan.CycleValue = 30;
            _plan.Priority = 2;
            _plan.StartDate = DateTime.Today;
            _plan.AutoCreateRecord = true;
            _plan.SendReminder = true;
        }
    }

    private async Task LoadComponents()
    {
        try
        {
            var filter = new EquipmentComponentFilter { PageSize = 1000, IsEnabled = true };
            var result = await ComponentService.SearchComponentsAsync(filter);
            _components = result.Items;
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载设备部件失败: {ex.Message}", Severity.Error);
        }
    }

    private string GetComponentDisplayName(int componentId)
    {
        var component = _components.FirstOrDefault(c => c.Id == componentId);
        return component != null ? $"{component.Name} ({component.Code})" : "";
    }

    private async Task<IEnumerable<int>> SearchComponents(string value)
    {
        if (string.IsNullOrEmpty(value))
            return _components.Select(c => c.Id);

        return _components
            .Where(c => c.Name.Contains(value, StringComparison.OrdinalIgnoreCase) ||
                       c.Code.Contains(value, StringComparison.OrdinalIgnoreCase))
            .Select(c => c.Id);
    }

    private void Cancel() => MudDialog.Cancel();

    private async Task Save()
    {
        if (!_isFormValid)
        {
            Snackbar.Add("请检查表单输入", Severity.Warning);
            return;
        }

        try
        {
            _isSaving = true;
            StateHasChanged();

            if (IsEdit)
            {
                await PlanService.UpdatePlanAsync(_plan);
            }
            else
            {
                await PlanService.CreatePlanAsync(_plan);
            }

            MudDialog.Close(DialogResult.Ok(true));
        }
        catch (Exception ex)
        {
            Snackbar.Add($"保存失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isSaving = false;
            StateHasChanged();
        }
    }
}
