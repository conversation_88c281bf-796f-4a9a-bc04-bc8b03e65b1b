@page "/debug-claims"
@using System.Security.Claims
@inject AuthenticationStateProvider AuthStateProvider

<PageTitle>调试 Claims</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large">
    <MudPaper Elevation="2" Class="pa-4">
        <MudText Typo="Typo.h4" Color="Color.Primary" Class="mb-4">当前用户 Claims 调试信息</MudText>
        
        @if (isLoading)
        {
            <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
        }
        else if (authState?.User?.Identity?.IsAuthenticated == true)
        {
            <MudStack Spacing="3">
                <MudAlert Severity="Severity.Info">
                    <MudText><strong>用户名:</strong> @authState.User.FindFirst(ClaimTypes.Name)?.Value</MudText>
                    <MudText><strong>显示名:</strong> @authState.User.FindFirst("DisplayName")?.Value</MudText>
                    <MudText><strong>主要角色:</strong> @authState.User.FindFirst("Role")?.Value</MudText>
                    <MudText><strong>角色名称:</strong> @authState.User.FindFirst("RoleName")?.Value</MudText>
                </MudAlert>

                <MudPaper Elevation="1" Class="pa-3">
                    <MudText Typo="Typo.h6" Class="mb-2">所有 Claims:</MudText>
                    <MudTable Items="@claims" Dense="true" Hover="true" Striped="true">
                        <HeaderContent>
                            <MudTh>类型</MudTh>
                            <MudTh>值</MudTh>
                        </HeaderContent>
                        <RowTemplate>
                            <MudTd DataLabel="类型">@context.Type</MudTd>
                            <MudTd DataLabel="值">@context.Value</MudTd>
                        </RowTemplate>
                    </MudTable>
                </MudPaper>

                @{
                    var roleClaims = authState.User.Claims.Where(c => c.Type == "Role").ToList();
                }

                <MudPaper Elevation="1" Class="pa-3">
                    <MudText Typo="Typo.h6" Class="mb-2">角色 Claims:</MudText>
                    @if (roleClaims.Any())
                    {
                        <MudStack>
                            @foreach (var roleClaim in roleClaims)
                            {
                                <MudText>• @roleClaim.Value</MudText>
                            }
                        </MudStack>
                    }
                    else
                    {
                        <MudAlert Severity="Severity.Warning">没有找到角色 Claims</MudAlert>
                    }
                </MudPaper>

                <MudPaper Elevation="1" Class="pa-3">
                    <MudText Typo="Typo.h6" Class="mb-2">权限检查测试:</MudText>
                    @{
                        var hasAdminRole = roleClaims.Any(c => c.Value.Equals("Administrator", StringComparison.OrdinalIgnoreCase));
                    }
                    <MudAlert Severity="@(hasAdminRole ? Severity.Success : Severity.Error)">
                        <MudText><strong>是否有 Administrator 角色:</strong> @(hasAdminRole ? "是" : "否")</MudText>
                    </MudAlert>
                </MudPaper>
            </MudStack>
        }
        else
        {
            <MudAlert Severity="Severity.Warning">用户未登录</MudAlert>
        }
    </MudPaper>
</MudContainer>

@code {
    private bool isLoading = true;
    private AuthenticationState? authState;
    private List<ClaimInfo> claims = new();

    protected override async Task OnInitializedAsync()
    {
        try
        {
            authState = await AuthStateProvider.GetAuthenticationStateAsync();
            
            if (authState?.User?.Identity?.IsAuthenticated == true)
            {
                claims = authState.User.Claims
                    .Select(c => new ClaimInfo { Type = c.Type, Value = c.Value })
                    .OrderBy(c => c.Type)
                    .ToList();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取认证状态失败: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private class ClaimInfo
    {
        public string Type { get; set; } = "";
        public string Value { get; set; } = "";
    }
}
