USE CoreHub;

INSERT INTO EquipmentComponents (Code, Name, Model, Specifications, Supplier, Category, StockQuantity, MinStockLevel, Unit, UnitPrice, StorageLocation, Status, IsCritical, ExpectedLifeDays, WarrantyDays, Description, IsEnabled, CreatedAt, CreatedBy) VALUES 
('COMP002', '传动皮带', 'GATES-5M-15', '长度1500mm，宽度15mm', '盖茨传动', '传动部件', 25, 10, '条', 45.00, 'A区-02架', 1, 0, 180, 90, '传动系统皮带', 1, GETDATE(), 1),
('COMP003', '电机', 'ABB-M3BP90L', '功率2.2KW，转速1450rpm', 'ABB电机', '电气部件', 3, 2, '台', 1200.00, 'B区-01架', 1, 1, 1825, 365, '主驱动电机', 1, GETDATE(), 1),
('COMP004', '密封圈', 'NOK-O-50', '内径50mm橡胶密封圈', '日本NOK', '密封部件', 50, 20, '个', 8.50, 'C区-01架', 1, 0, 90, 30, '设备密封圈', 1, GETDATE(), 1),
('COMP005', '滤芯', 'MANN-C25114', '空气滤芯，过滤精度5μm', '曼牌滤清器', '过滤部件', 15, 8, '个', 65.00, 'C区-02架', 1, 0, 120, 60, '空气过滤器滤芯', 1, GETDATE(), 1);

-- 插入保养计划数据
INSERT INTO MaintenancePlans (Code, Name, ComponentId, MaintenanceType, CycleType, CycleValue, ReminderDays, EstimatedDuration, MaintenanceContent, MaintenanceStandard, RequiredTools, RequiredMaterials, ResponsibleDepartmentId, ResponsiblePersonId, Priority, Status, NextMaintenanceDate, TotalMaintenanceCount, IsEnabled, CreatedAt, CreatedBy) VALUES 
('PLAN001', '主轴承定期保养', 1, 1, 1, 90, 7, 120, '检查轴承润滑情况，清洁轴承，添加润滑脂', '润滑脂充足，无异响，温度正常', '扳手，润滑枪，清洁布', '润滑脂，清洁剂', 2, 1, 3, 1, DATEADD(DAY, 30, GETDATE()), 0, 1, GETDATE(), 1),
('PLAN002', '传动皮带检查', 2, 2, 1, 30, 3, 30, '检查皮带张紧度，磨损情况，对齐度', '张紧度适中，无裂纹，对齐良好', '张力计，直尺', '', 2, 1, 2, 1, DATEADD(DAY, 15, GETDATE()), 0, 1, GETDATE(), 1),
('PLAN003', '电机深度保养', 3, 3, 1, 180, 14, 240, '拆解电机，清洁绕组，检查轴承，测试绝缘', '绝缘电阻>1MΩ，轴承无异响，绕组清洁', '万用表，绝缘测试仪，清洁工具', '绝缘漆，轴承润滑脂', 2, 1, 3, 1, DATEADD(DAY, 60, GETDATE()), 0, 1, GETDATE(), 1);
