@page "/database-update"
@using CoreHub.Shared.Data
@inject DatabaseContext DatabaseContext
@inject ISnackbar Snackbar

<PageTitle>数据库更新</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-4">
    <MudPaper Class="pa-4">
        <MudText Typo="Typo.h4" Class="mb-4">数据库更新</MudText>
        
        <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="UpdateDatabase" Disabled="@isUpdating">
            @if (isUpdating)
            {
                <MudProgressCircular Class="mr-2" Size="Size.Small" Indeterminate="true" />
                <text>更新中...</text>
            }
            else
            {
                <text>添加设备部位管理菜单</text>
            }
        </MudButton>
        
        @if (!string.IsNullOrEmpty(result))
        {
            <MudAlert Severity="@(isSuccess ? Severity.Success : Severity.Error)" Class="mt-4">
                @result
            </MudAlert>
        }
    </MudPaper>
</MudContainer>

@code {
    private bool isUpdating = false;
    private string result = "";
    private bool isSuccess = false;

    private async Task UpdateDatabase()
    {
        isUpdating = true;
        result = "";
        
        try
        {
            // 添加权限
            var sql1 = @"
                INSERT INTO [dbo].[Permissions] ([Code], [Name], [Description], [Module], [Action], [Level], [SortOrder], [IsSystem], [RouteUrl])
                SELECT 'EquipmentPartManagement.View', '设备部位管理查看', '查看设备部位管理页面', 'EquipmentPartManagement', 'View', 2, 50, 1, 'equipment-part-management'
                WHERE NOT EXISTS (SELECT 1 FROM Permissions WHERE Code = 'EquipmentPartManagement.View')";
            
            var sql2 = @"
                INSERT INTO [dbo].[Permissions] ([Code], [Name], [Description], [Module], [Action], [Level], [SortOrder], [IsSystem], [RouteUrl])
                SELECT 'EquipmentPartManagement.Create', '设备部位管理新增', '新增设备部位', 'EquipmentPartManagement', 'Create', 3, 51, 1, NULL
                WHERE NOT EXISTS (SELECT 1 FROM Permissions WHERE Code = 'EquipmentPartManagement.Create')";
            
            var sql3 = @"
                INSERT INTO [dbo].[Permissions] ([Code], [Name], [Description], [Module], [Action], [Level], [SortOrder], [IsSystem], [RouteUrl])
                SELECT 'EquipmentPartManagement.Edit', '设备部位管理编辑', '编辑设备部位', 'EquipmentPartManagement', 'Edit', 3, 52, 1, NULL
                WHERE NOT EXISTS (SELECT 1 FROM Permissions WHERE Code = 'EquipmentPartManagement.Edit')";
            
            var sql4 = @"
                INSERT INTO [dbo].[Permissions] ([Code], [Name], [Description], [Module], [Action], [Level], [SortOrder], [IsSystem], [RouteUrl])
                SELECT 'EquipmentPartManagement.Delete', '设备部位管理删除', '删除设备部位', 'EquipmentPartManagement', 'Delete', 3, 53, 1, NULL
                WHERE NOT EXISTS (SELECT 1 FROM Permissions WHERE Code = 'EquipmentPartManagement.Delete')";
            
            // 添加菜单
            var sql5 = @"
                DECLARE @EquipmentManagementId INT;
                SELECT @EquipmentManagementId = Id FROM MenuItems WHERE Code = 'EquipmentManagementGroup';
                
                INSERT INTO [dbo].[MenuItems] ([Code], [Name], [Description], [RouteUrl], [Icon], [ParentId], [Level], [SortOrder], [PermissionCode], [IsEnabled], [IsPublic], [IsSystem], [MenuType])
                SELECT 'EquipmentPartManagement', '设备部位管理', '设备部位管理页面', 'equipment-part-management', 'Icons.Material.Filled.Build', @EquipmentManagementId, 2, 64, 'EquipmentPartManagement.View', 1, 0, 1, 1
                WHERE NOT EXISTS (SELECT 1 FROM MenuItems WHERE Code = 'EquipmentPartManagement')";
            
            // 添加角色权限
            var sql6 = @"
                INSERT INTO [dbo].[RolePermissions] ([RoleId], [PermissionId])
                SELECT r.Id, p.Id
                FROM Roles r, Permissions p
                WHERE r.Code = 'Operator'
                AND p.Code IN ('EquipmentPartManagement.View', 'EquipmentPartManagement.Create', 'EquipmentPartManagement.Edit')
                AND NOT EXISTS (
                    SELECT 1 FROM RolePermissions rp 
                    WHERE rp.RoleId = r.Id AND rp.PermissionId = p.Id
                )";
            
            await DatabaseContext.Db.Ado.ExecuteCommandAsync(sql1);
            await DatabaseContext.Db.Ado.ExecuteCommandAsync(sql2);
            await DatabaseContext.Db.Ado.ExecuteCommandAsync(sql3);
            await DatabaseContext.Db.Ado.ExecuteCommandAsync(sql4);
            await DatabaseContext.Db.Ado.ExecuteCommandAsync(sql5);
            await DatabaseContext.Db.Ado.ExecuteCommandAsync(sql6);
            
            result = "数据库更新成功！设备部位管理菜单和权限已添加。";
            isSuccess = true;
            Snackbar.Add("数据库更新成功", Severity.Success);
        }
        catch (Exception ex)
        {
            result = $"数据库更新失败：{ex.Message}";
            isSuccess = false;
            Snackbar.Add("数据库更新失败", Severity.Error);
        }
        finally
        {
            isUpdating = false;
        }
    }
}
