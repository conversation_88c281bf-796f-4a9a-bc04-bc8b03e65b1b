using CoreHub.Shared.Models.Database;
using CoreHub.Shared.Models.Common;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 设备部件管理服务接口
    /// </summary>
    public interface IEquipmentComponentService
    {
        #region 基础CRUD操作

        /// <summary>
        /// 获取所有设备部件
        /// </summary>
        /// <returns>设备部件列表</returns>
        Task<List<EquipmentComponent>> GetAllComponentsAsync();

        /// <summary>
        /// 根据ID获取设备部件
        /// </summary>
        /// <param name="id">部件ID</param>
        /// <returns>设备部件</returns>
        Task<EquipmentComponent?> GetComponentByIdAsync(int id);

        /// <summary>
        /// 根据编码获取设备部件
        /// </summary>
        /// <param name="code">部件编码</param>
        /// <returns>设备部件</returns>
        Task<EquipmentComponent?> GetComponentByCodeAsync(string code);

        /// <summary>
        /// 创建设备部件
        /// </summary>
        /// <param name="component">设备部件</param>
        /// <returns>创建结果</returns>
        Task<(bool IsSuccess, string ErrorMessage, int? Id)> CreateComponentAsync(EquipmentComponent component);

        /// <summary>
        /// 更新设备部件
        /// </summary>
        /// <param name="component">设备部件</param>
        /// <returns>更新结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateComponentAsync(EquipmentComponent component);

        /// <summary>
        /// 删除设备部件
        /// </summary>
        /// <param name="id">部件ID</param>
        /// <returns>删除结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> DeleteComponentAsync(int id);

        /// <summary>
        /// 切换部件启用状态
        /// </summary>
        /// <param name="id">部件ID</param>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> ToggleStatusAsync(int id);

        #endregion

        #region 搜索和筛选

        /// <summary>
        /// 搜索设备部件
        /// </summary>
        /// <param name="filter">搜索过滤条件</param>
        /// <returns>分页搜索结果</returns>
        Task<PagedResult<EquipmentComponent>> SearchComponentsAsync(EquipmentComponentFilter filter);

        /// <summary>
        /// 搜索设备部件（简单版本）
        /// </summary>
        /// <param name="keyword">关键词</param>
        /// <param name="category">分类</param>
        /// <param name="status">状态</param>
        /// <param name="isLowStock">是否库存不足</param>
        /// <returns>搜索结果</returns>
        Task<List<EquipmentComponent>> SearchComponentsAsync(string? keyword = null, string? category = null, int? status = null, bool? isLowStock = null);

        /// <summary>
        /// 获取部件分类列表
        /// </summary>
        /// <returns>分类列表</returns>
        Task<List<string>> GetCategoriesAsync();

        /// <summary>
        /// 获取供应商列表
        /// </summary>
        /// <returns>供应商列表</returns>
        Task<List<string>> GetSuppliersAsync();

        #endregion

        #region 库存管理

        /// <summary>
        /// 更新库存数量
        /// </summary>
        /// <param name="componentId">部件ID</param>
        /// <param name="quantity">数量变化（正数为入库，负数为出库）</param>
        /// <param name="reason">变更原因</param>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateStockQuantityAsync(int componentId, int quantity, string reason);

        /// <summary>
        /// 获取库存不足的部件
        /// </summary>
        /// <returns>库存不足的部件列表</returns>
        Task<List<EquipmentComponent>> GetLowStockComponentsAsync();

        /// <summary>
        /// 获取缺货的部件
        /// </summary>
        /// <returns>缺货的部件列表</returns>
        Task<List<EquipmentComponent>> GetOutOfStockComponentsAsync();

        /// <summary>
        /// 批量更新库存
        /// </summary>
        /// <param name="stockUpdates">库存更新列表</param>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> BatchUpdateStockAsync(List<(int ComponentId, int Quantity, string Reason)> stockUpdates);

        #endregion

        #region 设备关联管理

        /// <summary>
        /// 获取设备的所有部件
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns>设备部件关联列表</returns>
        Task<List<EquipmentComponentMapping>> GetEquipmentComponentsAsync(int equipmentId);

        /// <summary>
        /// 获取部件关联的所有设备
        /// </summary>
        /// <param name="componentId">部件ID</param>
        /// <returns>设备部件关联列表</returns>
        Task<List<EquipmentComponentMapping>> GetComponentEquipmentsAsync(int componentId);

        /// <summary>
        /// 添加设备部件关联
        /// </summary>
        /// <param name="mapping">关联关系</param>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage, int? Id)> AddEquipmentComponentMappingAsync(EquipmentComponentMapping mapping);

        /// <summary>
        /// 更新设备部件关联
        /// </summary>
        /// <param name="mapping">关联关系</param>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateEquipmentComponentMappingAsync(EquipmentComponentMapping mapping);

        /// <summary>
        /// 删除设备部件关联
        /// </summary>
        /// <param name="id">关联ID</param>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> DeleteEquipmentComponentMappingAsync(int id);

        /// <summary>
        /// 获取需要更换的部件
        /// </summary>
        /// <param name="equipmentId">设备ID（可选）</param>
        /// <returns>需要更换的部件关联列表</returns>
        Task<List<EquipmentComponentMapping>> GetComponentsNeedingReplacementAsync(int? equipmentId = null);

        #endregion

        #region 统计分析

        /// <summary>
        /// 获取部件统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        Task<ComponentStatistics> GetComponentStatisticsAsync();

        /// <summary>
        /// 获取部件价值统计
        /// </summary>
        /// <param name="category">分类（可选）</param>
        /// <returns>价值统计</returns>
        Task<decimal> GetComponentsTotalValueAsync(string? category = null);

        /// <summary>
        /// 获取部件使用频率统计
        /// </summary>
        /// <param name="days">统计天数</param>
        /// <returns>使用频率统计</returns>
        Task<List<ComponentUsageStatistics>> GetComponentUsageFrequencyStatisticsAsync(int days = 30);

        #endregion

        #region 导入导出

        /// <summary>
        /// 批量导入部件
        /// </summary>
        /// <param name="components">部件列表</param>
        /// <returns>导入结果</returns>
        Task<(bool IsSuccess, string ErrorMessage, int SuccessCount, int FailCount)> BatchImportComponentsAsync(List<EquipmentComponent> components);

        /// <summary>
        /// 导出部件数据
        /// </summary>
        /// <param name="category">分类（可选）</param>
        /// <param name="status">状态（可选）</param>
        /// <returns>部件数据</returns>
        Task<List<EquipmentComponent>> ExportComponentsAsync(string? category = null, int? status = null);

        /// <summary>
        /// 获取部件使用统计信息
        /// </summary>
        /// <param name="componentId">部件ID</param>
        /// <returns>使用统计信息</returns>
        Task<Models.ComponentUsageStatistics> GetComponentUsageStatisticsAsync(int componentId);

        #endregion
    }

    /// <summary>
    /// 部件统计信息
    /// </summary>
    public class ComponentStatistics
    {
        /// <summary>
        /// 总部件数量
        /// </summary>
        public int TotalComponents { get; set; }

        /// <summary>
        /// 正常部件数量
        /// </summary>
        public int ActiveComponents { get; set; }

        /// <summary>
        /// 停用部件数量
        /// </summary>
        public int InactiveComponents { get; set; }

        /// <summary>
        /// 库存不足部件数量
        /// </summary>
        public int LowStockComponents { get; set; }

        /// <summary>
        /// 缺货部件数量
        /// </summary>
        public int OutOfStockComponents { get; set; }

        /// <summary>
        /// 关键部件数量
        /// </summary>
        public int CriticalComponents { get; set; }

        /// <summary>
        /// 总库存价值
        /// </summary>
        public decimal TotalStockValue { get; set; }

        /// <summary>
        /// 分类统计
        /// </summary>
        public Dictionary<string, int> CategoryStatistics { get; set; } = new Dictionary<string, int>();
    }

    /// <summary>
    /// 部件使用频率统计
    /// </summary>
    public class ComponentUsageStatistics
    {
        /// <summary>
        /// 部件ID
        /// </summary>
        public int ComponentId { get; set; }

        /// <summary>
        /// 部件名称
        /// </summary>
        public string ComponentName { get; set; } = string.Empty;

        /// <summary>
        /// 部件编码
        /// </summary>
        public string ComponentCode { get; set; } = string.Empty;

        /// <summary>
        /// 使用次数
        /// </summary>
        public int UsageCount { get; set; }

        /// <summary>
        /// 更换次数
        /// </summary>
        public int ReplacementCount { get; set; }

        /// <summary>
        /// 关联设备数量
        /// </summary>
        public int EquipmentCount { get; set; }
    }
}
