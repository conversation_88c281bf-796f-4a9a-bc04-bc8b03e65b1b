@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Models.Common
@using CoreHub.Shared.Services
@using MudBlazor
@inject IMaintenanceRecordService RecordService
@inject IMaintenancePlanService PlanService
@inject IEquipmentComponentService ComponentService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudForm @ref="_form" @bind-IsValid="_isFormValid" Model="_record">
            <MudTabs Elevation="2" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-4">
                <!-- 基本信息 -->
                <MudTabPanel Text="基本信息" Icon="Icons.Material.Filled.Info">
                    <MudGrid>
                        <MudItem xs="12" sm="6">
                            <MudTextField @bind-Value="_record.RecordNumber"
                                         Label="记录编号"
                                         Variant="Variant.Outlined"
                                         Required="true"
                                         RequiredError="请输入记录编号"
                                         MaxLength="50" />
                        </MudItem>
                        
                        <MudItem xs="12" sm="6">
                            <MudSelect @bind-Value="_record.ComponentId"
                                      Label="设备部件"
                                      Variant="Variant.Outlined"
                                      Required="true"
                                      RequiredError="请选择设备部件"
                                      ToStringFunc="@(c => GetComponentDisplayName(c))"
                                      SearchBox="true"
                                      SearchFunc="@SearchComponents">
                                @foreach (var component in _components)
                                {
                                    <MudSelectItem Value="@component.Id">@GetComponentDisplayName(component.Id)</MudSelectItem>
                                }
                            </MudSelect>
                        </MudItem>
                        
                        <MudItem xs="12" sm="6">
                            <MudSelect @bind-Value="_record.PlanId"
                                      Label="关联计划"
                                      Variant="Variant.Outlined"
                                      Clearable="true"
                                      ToStringFunc="@(p => GetPlanDisplayName(p))"
                                      SearchBox="true"
                                      SearchFunc="@SearchPlans">
                                @foreach (var plan in _plans)
                                {
                                    <MudSelectItem Value="@plan.Id">@GetPlanDisplayName(plan.Id)</MudSelectItem>
                                }
                            </MudSelect>
                        </MudItem>
                        
                        <MudItem xs="12" sm="6">
                            <MudSelect @bind-Value="_record.MaintenanceType"
                                      Label="保养类型"
                                      Variant="Variant.Outlined"
                                      Required="true"
                                      RequiredError="请选择保养类型">
                                <MudSelectItem Value="1">预防性保养</MudSelectItem>
                                <MudSelectItem Value="2">纠正性保养</MudSelectItem>
                                <MudSelectItem Value="3">预测性保养</MudSelectItem>
                                <MudSelectItem Value="4">紧急维修</MudSelectItem>
                            </MudSelect>
                        </MudItem>
                        
                        <MudItem xs="12" sm="6">
                            <MudDatePicker Date="_record.MaintenanceDate"
                                          DateChanged="@((DateTime? date) => _record.MaintenanceDate = date ?? DateTime.Now)"
                                          Label="保养日期"
                                          Variant="Variant.Outlined"
                                          Required="true"
                                          RequiredError="请选择保养日期"
                                          DateFormat="yyyy-MM-dd" />
                        </MudItem>
                        
                        <MudItem xs="12" sm="6">
                            <MudTextField @bind-Value="_record.MaintenancePersonName"
                                         Label="保养人员"
                                         Variant="Variant.Outlined"
                                         Required="true"
                                         RequiredError="请输入保养人员"
                                         MaxLength="50" />
                        </MudItem>
                        
                        <MudItem xs="12" sm="6">
                            <MudNumericField @bind-Value="_record.ActualDuration"
                                            Label="实际耗时(分钟)"
                                            Variant="Variant.Outlined"
                                            Min="1"
                                            Required="true"
                                            RequiredError="请输入实际耗时" />
                        </MudItem>
                        
                        <MudItem xs="12" sm="6">
                            <MudNumericField @bind-Value="_record.ActualCost"
                                            Label="实际费用"
                                            Variant="Variant.Outlined"
                                            Min="0"
                                            Format="F2"
                                            Adornment="Adornment.Start"
                                            AdornmentText="¥" />
                        </MudItem>
                        
                        <MudItem xs="12" sm="6">
                            <MudSelect @bind-Value="_record.MaintenanceResult"
                                      Label="保养结果"
                                      Variant="Variant.Outlined"
                                      Required="true"
                                      RequiredError="请选择保养结果">
                                <MudSelectItem Value="1">正常</MudSelectItem>
                                <MudSelectItem Value="2">需要关注</MudSelectItem>
                                <MudSelectItem Value="3">需要维修</MudSelectItem>
                            </MudSelect>
                        </MudItem>
                        
                        <MudItem xs="12" sm="6">
                            <MudSelect @bind-Value="_record.ReviewStatus"
                                      Label="审核状态"
                                      Variant="Variant.Outlined"
                                      Required="true"
                                      RequiredError="请选择审核状态">
                                <MudSelectItem Value="1">待审核</MudSelectItem>
                                <MudSelectItem Value="2">已通过</MudSelectItem>
                                <MudSelectItem Value="3">已拒绝</MudSelectItem>
                            </MudSelect>
                        </MudItem>
                    </MudGrid>
                </MudTabPanel>
                
                <!-- 保养内容 -->
                <MudTabPanel Text="保养内容" Icon="Icons.Material.Filled.Build">
                    <MudGrid>
                        <MudItem xs="12">
                            <MudTextField @bind-Value="_record.MaintenanceContent"
                                         Label="保养内容"
                                         Variant="Variant.Outlined"
                                         Lines="4"
                                         MaxLength="2000"
                                         Required="true"
                                         RequiredError="请输入保养内容" />
                        </MudItem>
                        
                        <MudItem xs="12">
                            <MudTextField @bind-Value="_record.UsedTools"
                                         Label="使用工具"
                                         Variant="Variant.Outlined"
                                         Lines="3"
                                         MaxLength="1000" />
                        </MudItem>
                        
                        <MudItem xs="12">
                            <MudTextField @bind-Value="_record.UsedMaterials"
                                         Label="使用材料"
                                         Variant="Variant.Outlined"
                                         Lines="3"
                                         MaxLength="1000" />
                        </MudItem>
                        
                        <MudItem xs="12">
                            <MudTextField @bind-Value="_record.Problems"
                                         Label="发现问题"
                                         Variant="Variant.Outlined"
                                         Lines="3"
                                         MaxLength="1000" />
                        </MudItem>
                        
                        <MudItem xs="12">
                            <MudTextField @bind-Value="_record.Solutions"
                                         Label="解决方案"
                                         Variant="Variant.Outlined"
                                         Lines="3"
                                         MaxLength="1000" />
                        </MudItem>
                        
                        <MudItem xs="12">
                            <MudTextField @bind-Value="_record.Recommendations"
                                         Label="改进建议"
                                         Variant="Variant.Outlined"
                                         Lines="3"
                                         MaxLength="1000" />
                        </MudItem>
                    </MudGrid>
                </MudTabPanel>
                
                <!-- 审核信息 -->
                <MudTabPanel Text="审核信息" Icon="Icons.Material.Filled.CheckCircle">
                    <MudGrid>
                        <MudItem xs="12" sm="6">
                            <MudTextField @bind-Value="_record.ReviewerName"
                                         Label="审核人员"
                                         Variant="Variant.Outlined"
                                         MaxLength="50" />
                        </MudItem>
                        
                        <MudItem xs="12" sm="6">
                            <MudDatePicker Date="_record.ReviewDate"
                                          DateChanged="@((DateTime? date) => _record.ReviewDate = date)"
                                          Label="审核日期"
                                          Variant="Variant.Outlined"
                                          DateFormat="yyyy-MM-dd" />
                        </MudItem>
                        
                        <MudItem xs="12">
                            <MudTextField @bind-Value="_record.ReviewComments"
                                         Label="审核意见"
                                         Variant="Variant.Outlined"
                                         Lines="3"
                                         MaxLength="500" />
                        </MudItem>
                        
                        <MudItem xs="12">
                            <MudTextField @bind-Value="_record.Notes"
                                         Label="备注说明"
                                         Variant="Variant.Outlined"
                                         Lines="3"
                                         MaxLength="500" />
                        </MudItem>
                    </MudGrid>
                </MudTabPanel>
            </MudTabs>
        </MudForm>
    </DialogContent>
    
    <DialogActions>
        <MudButton OnClick="Cancel" Variant="Variant.Text">取消</MudButton>
        <MudButton OnClick="Save" 
                  Variant="Variant.Filled" 
                  Color="Color.Primary"
                  Disabled="!_isFormValid || _isSaving"
                  StartIcon="@(IsEdit ? Icons.Material.Filled.Save : Icons.Material.Filled.Add)">
            @if (_isSaving)
            {
                <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                <span class="ml-2">保存中...</span>
            }
            else
            {
                @(IsEdit ? "保存" : "创建")
            }
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public bool IsEdit { get; set; }
    [Parameter] public int RecordId { get; set; }

    private MudForm _form = null!;
    private bool _isFormValid;
    private bool _isSaving;
    private MaintenanceRecord _record = new();
    private List<EquipmentComponent> _components = new();
    private List<MaintenancePlan> _plans = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadComponents();
        await LoadPlans();
        
        if (IsEdit && RecordId > 0)
        {
            try
            {
                var record = await RecordService.GetRecordByIdAsync(RecordId);
                if (record != null)
                {
                    _record = record;
                }
                else
                {
                    Snackbar.Add("保养记录不存在", Severity.Error);
                    MudDialog.Cancel();
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"加载保养记录失败: {ex.Message}", Severity.Error);
                MudDialog.Cancel();
            }
        }
        else
        {
            // 新建时设置默认值
            _record.RecordNumber = GenerateRecordNumber();
            _record.MaintenanceDate = DateTime.Today;
            _record.MaintenanceType = 1;
            _record.MaintenanceResult = 1;
            _record.ReviewStatus = 1;
        }
    }

    private async Task LoadComponents()
    {
        try
        {
            var filter = new EquipmentComponentFilter { PageSize = 1000, IsEnabled = true };
            var result = await ComponentService.SearchComponentsAsync(filter);
            _components = result.Items;
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载设备部件失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadPlans()
    {
        try
        {
            var filter = new MaintenancePlanFilter { PageSize = 1000, IsEnabled = true };
            var result = await PlanService.SearchPlansAsync(filter);
            _plans = result.Items;
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载保养计划失败: {ex.Message}", Severity.Error);
        }
    }

    private string GetComponentDisplayName(int componentId)
    {
        var component = _components.FirstOrDefault(c => c.Id == componentId);
        return component != null ? $"{component.Name} ({component.Code})" : "";
    }

    private string GetPlanDisplayName(int? planId)
    {
        if (!planId.HasValue) return "";
        var plan = _plans.FirstOrDefault(p => p.Id == planId.Value);
        return plan != null ? $"{plan.PlanName}" : "";
    }

    private async Task<IEnumerable<int>> SearchComponents(string value)
    {
        if (string.IsNullOrEmpty(value))
            return _components.Select(c => c.Id);

        return _components
            .Where(c => c.Name.Contains(value, StringComparison.OrdinalIgnoreCase) ||
                       c.Code.Contains(value, StringComparison.OrdinalIgnoreCase))
            .Select(c => c.Id);
    }

    private async Task<IEnumerable<int>> SearchPlans(string value)
    {
        if (string.IsNullOrEmpty(value))
            return _plans.Select(p => p.Id);

        return _plans
            .Where(p => p.PlanName.Contains(value, StringComparison.OrdinalIgnoreCase))
            .Select(p => p.Id);
    }

    private string GenerateRecordNumber()
    {
        return $"MR{DateTime.Now:yyyyMMddHHmmss}";
    }

    private void Cancel() => MudDialog.Cancel();

    private async Task Save()
    {
        if (!_isFormValid)
        {
            Snackbar.Add("请检查表单输入", Severity.Warning);
            return;
        }

        try
        {
            _isSaving = true;
            StateHasChanged();

            if (IsEdit)
            {
                await RecordService.UpdateRecordAsync(_record);
            }
            else
            {
                await RecordService.CreateRecordAsync(_record);
            }

            MudDialog.Close(DialogResult.Ok(true));
        }
        catch (Exception ex)
        {
            Snackbar.Add($"保存失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isSaving = false;
            StateHasChanged();
        }
    }
}
