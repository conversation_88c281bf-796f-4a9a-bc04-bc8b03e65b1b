using System;

namespace CoreHub.Shared.Extensions
{
    /// <summary>
    /// DateTime扩展方法
    /// </summary>
    public static class DateTimeExtensions
    {
        /// <summary>
        /// 格式化为友好的时间显示
        /// </summary>
        public static string ToFriendlyString(this DateTime dateTime)
        {
            var now = DateTime.Now;
            var diff = now - dateTime;

            if (diff.TotalMinutes < 1)
                return "刚刚";
            if (diff.TotalMinutes < 60)
                return $"{(int)diff.TotalMinutes}分钟前";
            if (diff.TotalHours < 24)
                return $"{(int)diff.TotalHours}小时前";
            if (diff.TotalDays < 7)
                return $"{(int)diff.TotalDays}天前";
            if (diff.TotalDays < 30)
                return $"{(int)(diff.TotalDays / 7)}周前";
            if (diff.TotalDays < 365)
                return $"{(int)(diff.TotalDays / 30)}个月前";

            return $"{(int)(diff.TotalDays / 365)}年前";
        }

        /// <summary>
        /// 格式化为友好的时间显示（可空类型）
        /// </summary>
        public static string ToFriendlyString(this DateTime? dateTime)
        {
            return dateTime?.ToFriendlyString() ?? "未设置";
        }

        /// <summary>
        /// 获取月份的第一天
        /// </summary>
        public static DateTime GetFirstDayOfMonth(this DateTime dateTime)
        {
            return new DateTime(dateTime.Year, dateTime.Month, 1);
        }

        /// <summary>
        /// 获取月份的最后一天
        /// </summary>
        public static DateTime GetLastDayOfMonth(this DateTime dateTime)
        {
            return new DateTime(dateTime.Year, dateTime.Month, DateTime.DaysInMonth(dateTime.Year, dateTime.Month));
        }

        /// <summary>
        /// 获取周的第一天（周一）
        /// </summary>
        public static DateTime GetFirstDayOfWeek(this DateTime dateTime)
        {
            var diff = (7 + (dateTime.DayOfWeek - DayOfWeek.Monday)) % 7;
            return dateTime.AddDays(-1 * diff).Date;
        }

        /// <summary>
        /// 获取周的最后一天（周日）
        /// </summary>
        public static DateTime GetLastDayOfWeek(this DateTime dateTime)
        {
            return dateTime.GetFirstDayOfWeek().AddDays(6);
        }

        /// <summary>
        /// 判断是否为今天
        /// </summary>
        public static bool IsToday(this DateTime dateTime)
        {
            return dateTime.Date == DateTime.Today;
        }

        /// <summary>
        /// 判断是否为本周
        /// </summary>
        public static bool IsThisWeek(this DateTime dateTime)
        {
            var today = DateTime.Today;
            var startOfWeek = today.GetFirstDayOfWeek();
            var endOfWeek = today.GetLastDayOfWeek();
            return dateTime.Date >= startOfWeek && dateTime.Date <= endOfWeek;
        }

        /// <summary>
        /// 判断是否为本月
        /// </summary>
        public static bool IsThisMonth(this DateTime dateTime)
        {
            var today = DateTime.Today;
            return dateTime.Year == today.Year && dateTime.Month == today.Month;
        }

        /// <summary>
        /// 格式化为标准日期时间字符串
        /// </summary>
        public static string ToStandardString(this DateTime dateTime)
        {
            return dateTime.ToString("yyyy-MM-dd HH:mm:ss");
        }

        /// <summary>
        /// 格式化为标准日期字符串
        /// </summary>
        public static string ToDateString(this DateTime dateTime)
        {
            return dateTime.ToString("yyyy-MM-dd");
        }

        /// <summary>
        /// 格式化为标准时间字符串
        /// </summary>
        public static string ToTimeString(this DateTime dateTime)
        {
            return dateTime.ToString("HH:mm:ss");
        }
    }
}
