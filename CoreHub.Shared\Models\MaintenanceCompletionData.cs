namespace CoreHub.Shared.Models
{
    /// <summary>
    /// 保养完成数据
    /// </summary>
    public class MaintenanceCompletionData
    {
        /// <summary>
        /// 保养记录ID
        /// </summary>
        public int RecordId { get; set; }

        /// <summary>
        /// 实际保养时长（分钟）
        /// </summary>
        public int? ActualDuration { get; set; }

        /// <summary>
        /// 保养结果（1=正常,2=发现问题,3=需要进一步维修,4=保养失败）
        /// </summary>
        public int MaintenanceResult { get; set; } = 1;

        /// <summary>
        /// 保养内容
        /// </summary>
        public string MaintenanceContent { get; set; } = string.Empty;

        /// <summary>
        /// 解决方案描述
        /// </summary>
        public string? SolutionDescription { get; set; }

        /// <summary>
        /// 保养后状态描述
        /// </summary>
        public string? AfterMaintenanceStatus { get; set; }

        /// <summary>
        /// 发现的问题
        /// </summary>
        public string? FoundIssues { get; set; }

        /// <summary>
        /// 建议措施
        /// </summary>
        public string? Recommendations { get; set; }

        /// <summary>
        /// 使用的工具
        /// </summary>
        public string? UsedTools { get; set; }

        /// <summary>
        /// 使用的材料
        /// </summary>
        public string? UsedMaterials { get; set; }

        /// <summary>
        /// 更换的部件清单
        /// </summary>
        public string? ReplacedParts { get; set; }

        /// <summary>
        /// 保养费用
        /// </summary>
        public decimal? MaintenanceCost { get; set; }

        /// <summary>
        /// 下次保养建议日期
        /// </summary>
        public DateTime? NextMaintenanceSuggestion { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }
}
