-- 查看所有角色
SELECT * FROM Roles ORDER BY SortOrder;

-- 查看所有用户
SELECT * FROM Users WHERE Username = 'admin';

-- 查看用户角色分配
SELECT 
    u.Username,
    u.DisplayName,
    r.Code AS RoleCode,
    r.Name AS RoleName,
    ur.IsEnabled,
    ur.ExpiresAt
FROM Users u
INNER JOIN UserRoles ur ON u.Id = ur.UserId
INNER JOIN Roles r ON ur.RoleId = r.Id
WHERE u.Username = 'admin'
ORDER BY r.SortOrder;

-- 查看admin用户的所有权限
SELECT DISTINCT
    p.Code AS PermissionCode,
    p.Name AS PermissionName,
    p.Module
FROM Users u
INNER JOIN UserRoles ur ON u.Id = ur.UserId
INNER JOIN Roles r ON ur.RoleId = r.Id
INNER JOIN RolePermissions rp ON r.Id = rp.RoleId
INNER JOIN Permissions p ON rp.PermissionId = p.Id
WHERE u.Username = 'admin'
AND ur.IsEnabled = 1
AND r.IsEnabled = 1
AND rp.IsEnabled = 1
AND p.IsEnabled = 1
ORDER BY p.Module, p.SortOrder;
