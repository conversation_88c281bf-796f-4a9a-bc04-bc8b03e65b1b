using CoreHub.Shared.Components.Common;
using MudBlazor;
using static CoreHub.Shared.Components.Common.ConfirmDialog;

namespace CoreHub.Shared.Extensions
{
    /// <summary>
    /// 对话框服务扩展方法
    /// </summary>
    public static class DialogServiceExtensions
    {
        /// <summary>
        /// 显示确认对话框
        /// </summary>
        public static async Task<bool> ShowConfirmAsync(this IDialogService dialogService, 
            string title, 
            string? message = null, 
            string? content = null,
            ConfirmDialogType type = ConfirmDialogType.Confirm,
            string confirmText = "确认",
            string cancelText = "取消")
        {
            var parameters = new DialogParameters
            {
                ["Title"] = title,
                ["Message"] = message,
                ["Content"] = content,
                ["DialogType"] = type,
                ["ConfirmText"] = confirmText,
                ["CancelText"] = cancelText
            };

            var options = new DialogOptions
            {
                CloseButton = true,
                MaxWidth = MaxWidth.Small,
                FullWidth = true
            };

            var dialog = await dialogService.ShowAsync<ConfirmDialog>("", parameters, options);
            var result = await dialog.Result;
            
            return !result.Canceled && result.Data is bool confirmed && confirmed;
        }

        /// <summary>
        /// 显示删除确认对话框
        /// </summary>
        public static async Task<bool> ShowDeleteConfirmAsync(this IDialogService dialogService, 
            string itemName, 
            string? additionalInfo = null)
        {
            var content = $"确定要删除 \"{itemName}\" 吗？";
            if (!string.IsNullOrEmpty(additionalInfo))
            {
                content += $"\n\n{additionalInfo}";
            }
            content += "\n\n此操作不可撤销。";

            return await ShowConfirmAsync(dialogService, 
                "删除确认", 
                null, 
                content, 
                ConfirmDialogType.Delete, 
                "删除", 
                "取消");
        }

        /// <summary>
        /// 显示警告对话框
        /// </summary>
        public static async Task<bool> ShowWarningAsync(this IDialogService dialogService, 
            string title, 
            string content, 
            string confirmText = "继续")
        {
            return await ShowConfirmAsync(dialogService, 
                title, 
                null, 
                content, 
                ConfirmDialogType.Warning, 
                confirmText, 
                "取消");
        }
    }
}
