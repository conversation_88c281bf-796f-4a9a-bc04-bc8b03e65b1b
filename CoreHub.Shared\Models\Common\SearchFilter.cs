using System;
using System.Collections.Generic;

namespace CoreHub.Shared.Models.Common
{
    /// <summary>
    /// 搜索筛选基类
    /// </summary>
    public class SearchFilter : PagedQuery
    {
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 状态筛选
        /// </summary>
        public int? Status { get; set; }

        /// <summary>
        /// 部门ID筛选
        /// </summary>
        public int? DepartmentId { get; set; }

        /// <summary>
        /// 创建人ID筛选
        /// </summary>
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 是否启用筛选
        /// </summary>
        public bool? IsEnabled { get; set; }

        /// <summary>
        /// 自定义筛选条件
        /// </summary>
        public Dictionary<string, object> CustomFilters { get; set; } = new();

        /// <summary>
        /// 添加自定义筛选条件
        /// </summary>
        /// <param name="key">筛选键</param>
        /// <param name="value">筛选值</param>
        public void AddFilter(string key, object value)
        {
            CustomFilters[key] = value;
        }

        /// <summary>
        /// 获取自定义筛选条件
        /// </summary>
        /// <typeparam name="T">值类型</typeparam>
        /// <param name="key">筛选键</param>
        /// <returns>筛选值</returns>
        public T? GetFilter<T>(string key)
        {
            if (CustomFilters.TryGetValue(key, out var value) && value is T result)
            {
                return result;
            }
            return default;
        }

        /// <summary>
        /// 检查是否有筛选条件
        /// </summary>
        public bool HasFilters()
        {
            return !string.IsNullOrWhiteSpace(Keyword) ||
                   StartDate.HasValue ||
                   EndDate.HasValue ||
                   Status.HasValue ||
                   DepartmentId.HasValue ||
                   CreatedBy.HasValue ||
                   IsEnabled.HasValue ||
                   CustomFilters.Count > 0;
        }

        /// <summary>
        /// 清除所有筛选条件
        /// </summary>
        public virtual void ClearFilters()
        {
            Keyword = null;
            StartDate = null;
            EndDate = null;
            Status = null;
            DepartmentId = null;
            CreatedBy = null;
            IsEnabled = null;
            CustomFilters.Clear();
        }
    }





    /// <summary>
    /// 保养记录搜索筛选
    /// </summary>
    public class MaintenanceRecordFilter : SearchFilter
    {
        /// <summary>
        /// 保养类型
        /// </summary>
        public int? MaintenanceType { get; set; }

        /// <summary>
        /// 部件ID
        /// </summary>
        public int? ComponentId { get; set; }

        /// <summary>
        /// 保养计划ID
        /// </summary>
        public int? MaintenancePlanId { get; set; }

        /// <summary>
        /// 保养人员ID
        /// </summary>
        public int? MaintenancePersonId { get; set; }

        /// <summary>
        /// 保养结果
        /// </summary>
        public int? MaintenanceResult { get; set; }

        /// <summary>
        /// 审核状态
        /// </summary>
        public int? ReviewStatus { get; set; }

        /// <summary>
        /// 审核人ID
        /// </summary>
        public int? ReviewedBy { get; set; }

        /// <summary>
        /// 保养日期范围-开始
        /// </summary>
        public DateTime? MaintenanceStartDate { get; set; }

        /// <summary>
        /// 保养日期范围-结束
        /// </summary>
        public DateTime? MaintenanceEndDate { get; set; }

        /// <summary>
        /// 成本范围-最小值
        /// </summary>
        public decimal? MinCost { get; set; }

        /// <summary>
        /// 成本范围-最大值
        /// </summary>
        public decimal? MaxCost { get; set; }

        /// <summary>
        /// 清除筛选条件
        /// </summary>
        public override void ClearFilters()
        {
            base.ClearFilters();
            MaintenanceType = null;
            ComponentId = null;
            MaintenancePlanId = null;
            MaintenancePersonId = null;
            MaintenanceResult = null;
            ReviewStatus = null;
            ReviewedBy = null;
            MaintenanceStartDate = null;
            MaintenanceEndDate = null;
            MinCost = null;
            MaxCost = null;
        }
    }
}
