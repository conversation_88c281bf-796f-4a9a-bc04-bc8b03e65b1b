namespace CoreHub.Shared.Models
{
    /// <summary>
    /// 保养计划统计数据
    /// </summary>
    public class MaintenancePlanStatistics
    {
        /// <summary>
        /// 总计划数
        /// </summary>
        public int TotalPlans { get; set; }

        /// <summary>
        /// 启用的计划数
        /// </summary>
        public int ActivePlans { get; set; }

        /// <summary>
        /// 暂停的计划数
        /// </summary>
        public int PausedPlans { get; set; }

        /// <summary>
        /// 停用的计划数
        /// </summary>
        public int DisabledPlans { get; set; }

        /// <summary>
        /// 本周计划数
        /// </summary>
        public int WeekPlans { get; set; }

        /// <summary>
        /// 本月计划数
        /// </summary>
        public int MonthPlans { get; set; }

        /// <summary>
        /// 逾期计划数
        /// </summary>
        public int OverduePlans { get; set; }

        /// <summary>
        /// 即将到期计划数
        /// </summary>
        public int UpcomingPlans { get; set; }

        /// <summary>
        /// 今日计划数
        /// </summary>
        public int TodayPlans { get; set; }

        /// <summary>
        /// 按保养类型分组的统计
        /// </summary>
        public Dictionary<int, int> MaintenanceTypeStatistics { get; set; } = new Dictionary<int, int>();

        /// <summary>
        /// 按周期类型分组的统计
        /// </summary>
        public Dictionary<int, int> CycleTypeStatistics { get; set; } = new Dictionary<int, int>();

        /// <summary>
        /// 按优先级分组的统计
        /// </summary>
        public Dictionary<int, int> PriorityStatistics { get; set; } = new Dictionary<int, int>();

        /// <summary>
        /// 按部门分组的统计
        /// </summary>
        public Dictionary<string, int> DepartmentStatistics { get; set; } = new Dictionary<string, int>();
    }
}
