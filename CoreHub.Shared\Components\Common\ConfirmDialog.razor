@using MudBlazor

<MudDialog>
    <DialogContent>
        <div class="d-flex align-center mb-4">
            <MudIcon Icon="@GetIcon()" Color="@GetIconColor()" Size="Size.Large" Class="mr-3" />
            <div>
                <MudText Typo="Typo.h6">@Title</MudText>
                @if (!string.IsNullOrEmpty(Message))
                {
                    <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mt-1">@Message</MudText>
                }
            </div>
        </div>
        
        @if (!string.IsNullOrEmpty(Content))
        {
            <MudText Typo="Typo.body1" Class="mb-4">@Content</MudText>
        }
        
        @if (ContentTemplate != null)
        {
            <div class="mb-4">
                @ContentTemplate
            </div>
        }
        
        @if (ShowDetails && !string.IsNullOrEmpty(Details))
        {
            <MudExpansionPanels Elevation="0">
                <MudExpansionPanel Text="查看详情">
                    <MudText Typo="Typo.body2" Style="white-space: pre-wrap;">@Details</MudText>
                </MudExpansionPanel>
            </MudExpansionPanels>
        }
    </DialogContent>
    
    <DialogActions>
        <MudButton OnClick="Cancel" 
                  Variant="Variant.Text" 
                  Color="Color.Secondary">
            @CancelText
        </MudButton>
        <MudButton OnClick="Confirm" 
                  Variant="@GetConfirmVariant()" 
                  Color="@GetConfirmColor()"
                  StartIcon="@ConfirmIcon">
            @ConfirmText
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;

    [Parameter] public string Title { get; set; } = "确认操作";
    [Parameter] public string? Message { get; set; }
    [Parameter] public string? Content { get; set; }
    [Parameter] public RenderFragment? ContentTemplate { get; set; }
    [Parameter] public string? Details { get; set; }
    [Parameter] public bool ShowDetails { get; set; } = false;
    
    [Parameter] public string ConfirmText { get; set; } = "确认";
    [Parameter] public string CancelText { get; set; } = "取消";
    [Parameter] public string? ConfirmIcon { get; set; }
    
    [Parameter] public ConfirmDialogType DialogType { get; set; } = ConfirmDialogType.Confirm;
    
    private void Cancel() => MudDialog.Cancel();
    
    private void Confirm() => MudDialog.Close(DialogResult.Ok(true));

    private string GetIcon()
    {
        return DialogType switch
        {
            ConfirmDialogType.Warning => Icons.Material.Filled.Warning,
            ConfirmDialogType.Error => Icons.Material.Filled.Error,
            ConfirmDialogType.Info => Icons.Material.Filled.Info,
            ConfirmDialogType.Success => Icons.Material.Filled.CheckCircle,
            ConfirmDialogType.Delete => Icons.Material.Filled.Delete,
            _ => Icons.Material.Filled.HelpOutline
        };
    }

    private Color GetIconColor()
    {
        return DialogType switch
        {
            ConfirmDialogType.Warning => Color.Warning,
            ConfirmDialogType.Error => Color.Error,
            ConfirmDialogType.Info => Color.Info,
            ConfirmDialogType.Success => Color.Success,
            ConfirmDialogType.Delete => Color.Error,
            _ => Color.Primary
        };
    }

    private Variant GetConfirmVariant()
    {
        return DialogType switch
        {
            ConfirmDialogType.Delete => Variant.Filled,
            ConfirmDialogType.Error => Variant.Filled,
            _ => Variant.Filled
        };
    }

    private Color GetConfirmColor()
    {
        return DialogType switch
        {
            ConfirmDialogType.Warning => Color.Warning,
            ConfirmDialogType.Error => Color.Error,
            ConfirmDialogType.Delete => Color.Error,
            ConfirmDialogType.Success => Color.Success,
            _ => Color.Primary
        };
    }

    protected override void OnInitialized()
    {
        // 根据对话框类型设置默认图标
        if (string.IsNullOrEmpty(ConfirmIcon))
        {
            ConfirmIcon = DialogType switch
            {
                ConfirmDialogType.Delete => Icons.Material.Filled.Delete,
                ConfirmDialogType.Warning => Icons.Material.Filled.Warning,
                ConfirmDialogType.Error => Icons.Material.Filled.Error,
                _ => Icons.Material.Filled.Check
            };
        }
    }
}

@code {
    public enum ConfirmDialogType
    {
        Confirm,
        Warning,
        Error,
        Info,
        Success,
        Delete
    }
}
