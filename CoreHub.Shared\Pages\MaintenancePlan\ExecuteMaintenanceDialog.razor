@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@using CoreHub.Shared.Extensions
@inject IMaintenanceRecordService MaintenanceRecordService
@inject IDialogService DialogService

<MudDialog>
    <DialogContent>
        <MudContainer MaxWidth="MaxWidth.Medium">
            @if (Plan != null)
            {
                <MudGrid>
                    <MudItem xs="12">
                        <MudText Typo="Typo.h6">执行保养计划：@Plan.Name</MudText>
                        <MudDivider Class="my-4" />
                    </MudItem>
                    
                    <MudItem xs="12">
                        <MudTextField @bind-Value="Record.MaintenanceContent" 
                                      Label="保养内容" 
                                      Multiline="true" 
                                      Lines="3"
                                      Required="true" />
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudDatePicker Date="Record.MaintenanceDate"
                                       DateChanged="@((DateTime? date) => Record.MaintenanceDate = date ?? DateTime.Now)"
                                       Label="保养日期"
                                       Required="true" />
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudTimePicker @bind-Time="MaintenanceTime" 
                                       Label="保养时间" />
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudNumericField @bind-Value="Record.EstimatedDuration" 
                                         Label="预计耗时（分钟）" 
                                         Min="0" />
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudNumericField @bind-Value="Record.EstimatedCost" 
                                         Label="预计成本" 
                                         Min="0" 
                                         Format="F2" />
                    </MudItem>
                    
                    <MudItem xs="12">
                        <MudTextField @bind-Value="Record.Notes" 
                                      Label="备注" 
                                      Multiline="true" 
                                      Lines="2" />
                    </MudItem>
                </MudGrid>
            }
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
        <MudButton Color="Color.Primary" OnClick="Submit" Disabled="@_processing">
            @if (_processing)
            {
                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                <MudText Class="ms-2">处理中...</MudText>
            }
            else
            {
                <MudText>确定</MudText>
            }
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public MaintenancePlan? Plan { get; set; }

    private MaintenanceRecord Record = new();
    private TimeSpan? MaintenanceTime;
    private bool _processing = false;

    protected override void OnInitialized()
    {
        if (Plan != null)
        {
            Record.MaintenancePlanId = Plan.Id;
            Record.ComponentId = Plan.ComponentId;
            Record.MaintenanceDate = DateTime.Now;
            Record.MaintenanceContent = Plan.MaintenanceContent;
            Record.EstimatedDuration = Plan.EstimatedDuration;
            Record.EstimatedCost = Plan.EstimatedCost;
            MaintenanceTime = DateTime.Now.TimeOfDay;
        }
    }

    private async Task Submit()
    {
        if (Plan == null) return;

        _processing = true;
        try
        {
            // 合并日期和时间
            if (MaintenanceTime.HasValue)
            {
                Record.MaintenanceDate = Record.MaintenanceDate.Date.Add(MaintenanceTime.Value);
            }

            Record.Status = 1; // 待执行
            await MaintenanceRecordService.CreateAsync(Record);
            MudDialog.Close(DialogResult.Ok(true));
        }
        catch (Exception ex)
        {
            await DialogService.ShowMessageBox("错误", $"创建保养记录失败：{ex.Message}");
        }
        finally
        {
            _processing = false;
        }
    }

    void Cancel() => MudDialog.Cancel();
}
