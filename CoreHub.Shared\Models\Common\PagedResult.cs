using System;
using System.Collections.Generic;
using System.Linq;

namespace CoreHub.Shared.Models.Common
{
    /// <summary>
    /// 分页结果
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    public class PagedResult<T>
    {
        /// <summary>
        /// 数据列表
        /// </summary>
        public List<T> Items { get; set; } = new();

        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 当前页码（从1开始）
        /// </summary>
        public int PageIndex { get; set; }

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

        /// <summary>
        /// 是否有上一页
        /// </summary>
        public bool HasPreviousPage => PageIndex > 1;

        /// <summary>
        /// 是否有下一页
        /// </summary>
        public bool HasNextPage => PageIndex < TotalPages;

        /// <summary>
        /// 开始记录索引（从1开始）
        /// </summary>
        public int StartIndex => (PageIndex - 1) * PageSize + 1;

        /// <summary>
        /// 结束记录索引
        /// </summary>
        public int EndIndex => Math.Min(StartIndex + PageSize - 1, TotalCount);

        /// <summary>
        /// 是否为空结果
        /// </summary>
        public bool IsEmpty => !Items.Any();

        /// <summary>
        /// 构造函数
        /// </summary>
        public PagedResult()
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="items">数据列表</param>
        /// <param name="totalCount">总记录数</param>
        /// <param name="pageIndex">当前页码</param>
        /// <param name="pageSize">每页大小</param>
        public PagedResult(List<T> items, int totalCount, int pageIndex, int pageSize)
        {
            Items = items ?? new List<T>();
            TotalCount = totalCount;
            PageIndex = pageIndex;
            PageSize = pageSize;
        }

        /// <summary>
        /// 创建空的分页结果
        /// </summary>
        /// <param name="pageIndex">当前页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>空的分页结果</returns>
        public static PagedResult<T> Empty(int pageIndex = 1, int pageSize = 10)
        {
            return new PagedResult<T>(new List<T>(), 0, pageIndex, pageSize);
        }

        /// <summary>
        /// 从IQueryable创建分页结果
        /// </summary>
        /// <param name="source">数据源</param>
        /// <param name="pageIndex">当前页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>分页结果</returns>
        public static PagedResult<T> Create(IQueryable<T> source, int pageIndex, int pageSize)
        {
            var totalCount = source.Count();
            var items = source.Skip((pageIndex - 1) * pageSize).Take(pageSize).ToList();
            return new PagedResult<T>(items, totalCount, pageIndex, pageSize);
        }

        /// <summary>
        /// 从List创建分页结果
        /// </summary>
        /// <param name="source">数据源</param>
        /// <param name="pageIndex">当前页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>分页结果</returns>
        public static PagedResult<T> Create(List<T> source, int pageIndex, int pageSize)
        {
            var totalCount = source.Count;
            var items = source.Skip((pageIndex - 1) * pageSize).Take(pageSize).ToList();
            return new PagedResult<T>(items, totalCount, pageIndex, pageSize);
        }

        /// <summary>
        /// 从已分页的数据创建分页结果
        /// </summary>
        /// <param name="items">已分页的数据列表</param>
        /// <param name="totalCount">总记录数</param>
        /// <param name="pageIndex">当前页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>分页结果</returns>
        public static PagedResult<T> Create(List<T> items, int totalCount, int pageIndex, int pageSize)
        {
            return new PagedResult<T>(items, totalCount, pageIndex, pageSize);
        }

        /// <summary>
        /// 转换为其他类型的分页结果
        /// </summary>
        /// <typeparam name="TResult">目标类型</typeparam>
        /// <param name="converter">转换函数</param>
        /// <returns>转换后的分页结果</returns>
        public PagedResult<TResult> Select<TResult>(Func<T, TResult> converter)
        {
            var convertedItems = Items.Select(converter).ToList();
            return new PagedResult<TResult>(convertedItems, TotalCount, PageIndex, PageSize);
        }

        /// <summary>
        /// 获取页码范围（用于分页导航）
        /// </summary>
        /// <param name="maxPages">最大显示页数</param>
        /// <returns>页码范围</returns>
        public List<int> GetPageRange(int maxPages = 10)
        {
            var pages = new List<int>();
            
            if (TotalPages <= maxPages)
            {
                // 如果总页数不超过最大显示页数，显示所有页码
                for (int i = 1; i <= TotalPages; i++)
                {
                    pages.Add(i);
                }
            }
            else
            {
                // 计算显示范围
                var half = maxPages / 2;
                var start = Math.Max(1, PageIndex - half);
                var end = Math.Min(TotalPages, start + maxPages - 1);
                
                // 调整开始位置
                if (end - start + 1 < maxPages)
                {
                    start = Math.Max(1, end - maxPages + 1);
                }
                
                for (int i = start; i <= end; i++)
                {
                    pages.Add(i);
                }
            }
            
            return pages;
        }
    }

    /// <summary>
    /// 分页查询参数
    /// </summary>
    public class PagedQuery
    {
        /// <summary>
        /// 页码（从1开始）
        /// </summary>
        public int PageIndex { get; set; } = 1;

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; } = 10;

        /// <summary>
        /// 搜索关键词
        /// </summary>
        public string? Keyword { get; set; }

        /// <summary>
        /// 排序字段
        /// </summary>
        public string? SortField { get; set; }

        /// <summary>
        /// 排序方向（asc/desc）
        /// </summary>
        public string? SortDirection { get; set; } = "asc";

        /// <summary>
        /// 是否降序排序
        /// </summary>
        public bool IsDescending => string.Equals(SortDirection, "desc", StringComparison.OrdinalIgnoreCase);

        /// <summary>
        /// 跳过的记录数
        /// </summary>
        public int Skip => (PageIndex - 1) * PageSize;

        /// <summary>
        /// 获取的记录数
        /// </summary>
        public int Take => PageSize;

        /// <summary>
        /// 验证分页参数
        /// </summary>
        public void Validate()
        {
            if (PageIndex < 1) PageIndex = 1;
            if (PageSize < 1) PageSize = 10;
            if (PageSize > 100) PageSize = 100; // 限制最大页面大小
        }
    }
}
