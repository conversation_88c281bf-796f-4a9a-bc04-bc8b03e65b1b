using System;
using System.Linq;
using System.Text.RegularExpressions;

namespace CoreHub.Shared.Extensions
{
    /// <summary>
    /// 字符串扩展方法
    /// </summary>
    public static class StringExtensions
    {
        /// <summary>
        /// 判断字符串是否为空或null
        /// </summary>
        public static bool IsNullOrEmpty(this string? value)
        {
            return string.IsNullOrEmpty(value);
        }

        /// <summary>
        /// 判断字符串是否为空、null或仅包含空白字符
        /// </summary>
        public static bool IsNullOrWhiteSpace(this string? value)
        {
            return string.IsNullOrWhiteSpace(value);
        }

        /// <summary>
        /// 如果字符串为空则返回默认值
        /// </summary>
        public static string IfEmpty(this string? value, string defaultValue)
        {
            return string.IsNullOrEmpty(value) ? defaultValue : value;
        }

        /// <summary>
        /// 如果字符串为空或仅包含空白字符则返回默认值
        /// </summary>
        public static string IfWhiteSpace(this string? value, string defaultValue)
        {
            return string.IsNullOrWhiteSpace(value) ? defaultValue : value!;
        }

        /// <summary>
        /// 截断字符串到指定长度
        /// </summary>
        public static string Truncate(this string value, int maxLength, string suffix = "...")
        {
            if (string.IsNullOrEmpty(value) || value.Length <= maxLength)
                return value ?? string.Empty;

            return value.Substring(0, maxLength - suffix.Length) + suffix;
        }

        /// <summary>
        /// 移除HTML标签
        /// </summary>
        public static string RemoveHtmlTags(this string value)
        {
            if (string.IsNullOrEmpty(value))
                return string.Empty;

            return Regex.Replace(value, "<.*?>", string.Empty);
        }

        /// <summary>
        /// 转换为驼峰命名
        /// </summary>
        public static string ToCamelCase(this string value)
        {
            if (string.IsNullOrEmpty(value))
                return string.Empty;

            if (value.Length == 1)
                return value.ToLower();

            return char.ToLower(value[0]) + value.Substring(1);
        }

        /// <summary>
        /// 转换为帕斯卡命名
        /// </summary>
        public static string ToPascalCase(this string value)
        {
            if (string.IsNullOrEmpty(value))
                return string.Empty;

            if (value.Length == 1)
                return value.ToUpper();

            return char.ToUpper(value[0]) + value.Substring(1);
        }

        /// <summary>
        /// 检查字符串是否包含中文字符
        /// </summary>
        public static bool ContainsChinese(this string value)
        {
            if (string.IsNullOrEmpty(value))
                return false;

            return Regex.IsMatch(value, @"[\u4e00-\u9fa5]");
        }

        /// <summary>
        /// 获取字符串的字节长度（中文字符按2个字节计算）
        /// </summary>
        public static int GetByteLength(this string value)
        {
            if (string.IsNullOrEmpty(value))
                return 0;

            return value.Sum(c => c > 127 ? 2 : 1);
        }

        /// <summary>
        /// 按字节长度截断字符串
        /// </summary>
        public static string TruncateByBytes(this string value, int maxBytes, string suffix = "...")
        {
            if (string.IsNullOrEmpty(value))
                return string.Empty;

            var currentBytes = 0;
            var result = string.Empty;

            foreach (var c in value)
            {
                var charBytes = c > 127 ? 2 : 1;
                if (currentBytes + charBytes > maxBytes - suffix.GetByteLength())
                    break;

                result += c;
                currentBytes += charBytes;
            }

            return result.Length < value.Length ? result + suffix : value;
        }

        /// <summary>
        /// 验证是否为有效的邮箱地址
        /// </summary>
        public static bool IsValidEmail(this string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return false;

            try
            {
                var addr = new System.Net.Mail.MailAddress(value);
                return addr.Address == value;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证是否为有效的手机号码
        /// </summary>
        public static bool IsValidMobilePhone(this string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return false;

            return Regex.IsMatch(value, @"^1[3-9]\d{9}$");
        }

        /// <summary>
        /// 掩码显示字符串（用于敏感信息）
        /// </summary>
        public static string Mask(this string value, int startVisible = 3, int endVisible = 4, char maskChar = '*')
        {
            if (string.IsNullOrEmpty(value))
                return string.Empty;

            if (value.Length <= startVisible + endVisible)
                return new string(maskChar, value.Length);

            var start = value.Substring(0, startVisible);
            var end = value.Substring(value.Length - endVisible);
            var middle = new string(maskChar, value.Length - startVisible - endVisible);

            return start + middle + end;
        }

        /// <summary>
        /// 高亮搜索关键词
        /// </summary>
        public static string HighlightKeywords(this string value, string keyword, string highlightStart = "<mark>", string highlightEnd = "</mark>")
        {
            if (string.IsNullOrEmpty(value) || string.IsNullOrEmpty(keyword))
                return value ?? string.Empty;

            return Regex.Replace(value, Regex.Escape(keyword), 
                $"{highlightStart}$&{highlightEnd}", RegexOptions.IgnoreCase);
        }
    }
}
