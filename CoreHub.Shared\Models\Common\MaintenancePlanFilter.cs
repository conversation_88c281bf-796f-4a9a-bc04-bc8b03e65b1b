namespace CoreHub.Shared.Models.Common
{
    /// <summary>
    /// 保养计划筛选条件
    /// </summary>
    public class MaintenancePlanFilter : SearchFilter
    {
        /// <summary>
        /// 部件ID
        /// </summary>
        public int? ComponentId { get; set; }

        /// <summary>
        /// 设备ID
        /// </summary>
        public int? EquipmentId { get; set; }

        /// <summary>
        /// 保养类型
        /// </summary>
        public int? MaintenanceType { get; set; }

        /// <summary>
        /// 周期类型
        /// </summary>
        public int? CycleType { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public new bool? IsEnabled { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        public int? Priority { get; set; }

        /// <summary>
        /// 负责人ID
        /// </summary>
        public int? ResponsiblePersonId { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public new int? DepartmentId { get; set; }

        /// <summary>
        /// 下次保养时间范围 - 开始时间
        /// </summary>
        public DateTime? NextMaintenanceDateStart { get; set; }

        /// <summary>
        /// 下次保养时间范围 - 结束时间
        /// </summary>
        public DateTime? NextMaintenanceDateEnd { get; set; }

        /// <summary>
        /// 创建时间范围 - 开始时间
        /// </summary>
        public DateTime? CreatedAtStart { get; set; }

        /// <summary>
        /// 创建时间范围 - 结束时间
        /// </summary>
        public DateTime? CreatedAtEnd { get; set; }

        /// <summary>
        /// 是否即将到期（7天内）
        /// </summary>
        public bool? IsDueSoon { get; set; }

        /// <summary>
        /// 是否已过期
        /// </summary>
        public bool? IsOverdue { get; set; }

        /// <summary>
        /// 下次保养开始日期
        /// </summary>
        public DateTime? NextMaintenanceStartDate { get; set; }

        /// <summary>
        /// 下次保养结束日期
        /// </summary>
        public DateTime? NextMaintenanceEndDate { get; set; }
    }
}
