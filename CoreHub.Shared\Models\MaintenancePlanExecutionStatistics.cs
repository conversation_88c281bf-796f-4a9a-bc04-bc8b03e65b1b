namespace CoreHub.Shared.Models
{
    /// <summary>
    /// 保养计划执行统计数据
    /// </summary>
    public class MaintenancePlanExecutionStatistics
    {
        /// <summary>
        /// 总计划数
        /// </summary>
        public int TotalPlans { get; set; }

        /// <summary>
        /// 已执行计划数
        /// </summary>
        public int ExecutedPlans { get; set; }

        /// <summary>
        /// 执行率
        /// </summary>
        public decimal ExecutionRate { get; set; }

        /// <summary>
        /// 按时执行数
        /// </summary>
        public int OnTimeExecutions { get; set; }

        /// <summary>
        /// 按时执行率
        /// </summary>
        public decimal OnTimeRate { get; set; }

        /// <summary>
        /// 逾期执行数
        /// </summary>
        public int OverdueExecutions { get; set; }

        /// <summary>
        /// 逾期执行率
        /// </summary>
        public decimal OverdueRate { get; set; }

        /// <summary>
        /// 平均执行时长（分钟）
        /// </summary>
        public int AverageExecutionDuration { get; set; }

        /// <summary>
        /// 成功执行数
        /// </summary>
        public int SuccessfulExecutions { get; set; }

        /// <summary>
        /// 成功执行率
        /// </summary>
        public decimal SuccessRate { get; set; }

        /// <summary>
        /// 按月份分组的执行统计
        /// </summary>
        public Dictionary<string, int> MonthlyExecutions { get; set; } = new Dictionary<string, int>();

        /// <summary>
        /// 按保养类型分组的执行统计
        /// </summary>
        public Dictionary<int, int> MaintenanceTypeExecutions { get; set; } = new Dictionary<int, int>();

        /// <summary>
        /// 按部门分组的执行统计
        /// </summary>
        public Dictionary<string, int> DepartmentExecutions { get; set; } = new Dictionary<string, int>();
    }
}
