using System.Security.Claims;
using Microsoft.Extensions.Logging;

namespace CoreHub.Shared.Services
{

    public class UserAuthenticationService : IUserAuthenticationService
    {
        private readonly ILogger<UserAuthenticationService> _logger;
        private readonly Dictionary<string, UserInfo> _users;

        public UserAuthenticationService(ILogger<UserAuthenticationService> logger)
        {
            _logger = logger;
            _users = InitializeUsers();
        }

        public async Task<UserLoginResult> ValidateUserAsync(string username, string password)
        {
            try
            {
                await Task.Delay(300); // 模拟验证延迟

                if (_users.TryGetValue(username, out var user) && user.Password == password)
                {
                    _logger.LogInformation($"用户 {username} 验证成功");
                    return new UserLoginResult
                    {
                        IsSuccess = true,
                        User = user,
                        ErrorMessage = null
                    };
                }

                _logger.LogWarning($"用户 {username} 验证失败：用户名或密码错误");
                return new UserLoginResult
                {
                    IsSuccess = false,
                    User = null,
                    ErrorMessage = "用户名或密码错误"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"验证用户 {username} 时发生异常");
                return new UserLoginResult
                {
                    IsSuccess = false,
                    User = null,
                    ErrorMessage = "验证过程中发生错误"
                };
            }
        }

        public UserInfo? GetUserInfo(string username)
        {
            return _users.TryGetValue(username, out var user) ? user : null;
        }

        public List<UserInfo> GetAllUsers()
        {
            return _users.Values.ToList();
        }

        private Dictionary<string, UserInfo> InitializeUsers()
        {
            return new Dictionary<string, UserInfo>
            {
                ["admin"] = new UserInfo
                {
                    UserId = 1,
                    Username = "admin",
                    Password = "admin123",
                    Email = "<EMAIL>",
                    DisplayName = "系统管理员",
                    Role = "Administrator",
                    Permissions = new List<string>
                    {
                        "Home.View",
                        "Counter.View",
                        "Weather.View",
                        "DeviceScanner.View",
                        "DeviceRepair.View",
                        "CameraTest.View",
                        "UserManagement.View"
                    }
                },
                ["operator"] = new UserInfo
                {
                    UserId = 2,
                    Username = "operator",
                    Password = "op123",
                    Email = "<EMAIL>",
                    DisplayName = "设备操作员",
                    Role = "Operator",
                    Permissions = new List<string>
                    {
                        "Home.View",
                        "DeviceScanner.View",
                        "DeviceRepair.View",
                        "CameraTest.View"
                    }
                },
                ["viewer"] = new UserInfo
                {
                    UserId = 3,
                    Username = "viewer",
                    Password = "view123",
                    Email = "<EMAIL>",
                    DisplayName = "访客用户",
                    Role = "Viewer",
                    Permissions = new List<string>
                    {
                        "Home.View",
                        "Weather.View"
                    }
                }
            };
        }
    }

    public class UserInfo
    {
        public int UserId { get; set; }
        public string Username { get; set; } = "";
        public string Password { get; set; } = "";
        public string Email { get; set; } = "";
        public string DisplayName { get; set; } = "";
        public string Role { get; set; } = "";
        public string RoleName { get; set; } = "";
        public List<string> Permissions { get; set; } = [];
        public List<string> AllRoles { get; set; } = []; // 所有角色代码

        /// <summary>
        /// 转换为 ClaimsPrincipal
        /// </summary>
        public ClaimsPrincipal ToClaimsPrincipal()
        {
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.Name, Username),
                new Claim(ClaimTypes.Email, Email),
                new Claim("UserId", UserId.ToString()),
                new Claim("DisplayName", DisplayName),
                new Claim("Role", Role),
                new Claim("RoleName", RoleName)
            };

            // 添加所有角色声明
            foreach (var role in AllRoles)
            {
                claims.Add(new Claim("Role", role));
            }

            // 添加权限声明
            foreach (var permission in Permissions)
            {
                claims.Add(new Claim("Permission", permission));
            }

            var identity = new ClaimsIdentity(claims, "custom");
            return new ClaimsPrincipal(identity);
        }
    }

    public class UserLoginResult
    {
        public bool IsSuccess { get; set; }
        public UserInfo? User { get; set; }
        public string? ErrorMessage { get; set; }
    }
} 