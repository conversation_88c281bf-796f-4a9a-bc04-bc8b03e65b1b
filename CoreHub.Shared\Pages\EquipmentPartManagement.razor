@page "/equipment-part-management"
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@inject IEquipmentPartService EquipmentPartService
@inject IEquipmentService EquipmentService
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>设备部位管理</PageTitle>

<div>
    <MudPaper Class="pa-4">
        <MudGrid>
            <MudItem xs="12">
                <MudText Typo="Typo.h4" Class="mb-4">
                    <MudIcon Icon="@Icons.Material.Filled.Build" Class="mr-2" />
                    设备部位管理
                </MudText>
            </MudItem>

            <!-- 设备选择和操作按钮 -->
            <MudItem xs="12" Class="mb-4">
                <MudGrid>
                    <MudItem xs="12" md="6">
                        <MudSelect T="int?" @bind-Value="selectedEquipmentId" Label="选择设备" Variant="Variant.Outlined" 
                                   Placeholder="请选择设备" OnSelectionChanged="OnEquipmentChanged">
                            @foreach (var equipment in equipmentList)
                            {
                                <MudSelectItem Value="@((int?)equipment.Id)">@equipment.Name (@equipment.Code)</MudSelectItem>
                            }
                        </MudSelect>
                    </MudItem>
                    <MudItem xs="12" md="6" Class="d-flex align-center">
                        <MudButton Variant="Variant.Filled" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Add"
                                   OnClick="OpenCreateDialog" Disabled="@(!selectedEquipmentId.HasValue)">
                            添加部位
                        </MudButton>
                        <MudButton Variant="Variant.Outlined" Color="Color.Secondary" StartIcon="@Icons.Material.Filled.Refresh"
                                   OnClick="RefreshData" Class="ml-2">
                            刷新
                        </MudButton>
                    </MudItem>
                </MudGrid>
            </MudItem>

            <!-- 部位树形结构 -->
            <MudItem xs="12">
                @if (selectedEquipmentId.HasValue)
                {
                    <MudCard>
                        <MudCardHeader>
                            <CardHeaderContent>
                                <MudText Typo="Typo.h6">设备部位结构</MudText>
                            </CardHeaderContent>
                        </MudCardHeader>
                        <MudCardContent>
                            @if (equipmentParts.Any())
                            {
                                @foreach (var part in equipmentParts.Where(p => p.ParentId == null))
                                {
                                    @RenderPartItem(part, 0)
                                }
                            }
                            else
                            {
                                <MudAlert Severity="Severity.Info">
                                    该设备暂无部位信息，请点击"添加部位"按钮添加。
                                </MudAlert>
                            }
                        </MudCardContent>
                    </MudCard>
                }
                else
                {
                    <MudAlert Severity="Severity.Warning">
                        请先选择设备以查看和管理其部位信息。
                    </MudAlert>
                }
            </MudItem>
        </MudGrid>
    </MudPaper>
</div>

@code {
    private List<Equipment> equipmentList = new();
    private List<EquipmentPart> equipmentParts = new();
    private int? selectedEquipmentId;
    private bool isLoading = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadEquipmentList();
    }

    private async Task LoadEquipmentList()
    {
        try
        {
            isLoading = true;
            equipmentList = await EquipmentService.GetAllEquipmentAsync();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载设备列表失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task OnEquipmentChanged()
    {
        if (selectedEquipmentId.HasValue)
        {
            await LoadEquipmentParts();
        }
        else
        {
            equipmentParts.Clear();
        }
    }

    private async Task LoadEquipmentParts()
    {
        if (!selectedEquipmentId.HasValue) return;

        try
        {
            isLoading = true;
            equipmentParts = await EquipmentPartService.GetEquipmentPartTreeAsync(selectedEquipmentId.Value);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载设备部位失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task RefreshData()
    {
        await LoadEquipmentList();
        if (selectedEquipmentId.HasValue)
        {
            await LoadEquipmentParts();
        }
    }

    private string GetPartDisplayText(EquipmentPart part)
    {
        return $"{part.Name} ({part.Code})";
    }

    private string GetPartIcon(EquipmentPart part)
    {
        return part.Children.Any() ? Icons.Material.Filled.Folder : Icons.Material.Filled.Build;
    }

    private async Task OpenCreateDialog()
    {
        if (!selectedEquipmentId.HasValue) return;

        var parameters = new DialogParameters
        {
            ["EquipmentId"] = selectedEquipmentId.Value,
            ["ParentId"] = (int?)null
        };

        var dialog = await DialogService.ShowAsync<EquipmentPartDialog>("添加设备部位", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadEquipmentParts();
            Snackbar.Add("设备部位添加成功", Severity.Success);
        }
    }

    private async Task OpenCreateChildDialog(EquipmentPart parent)
    {
        var parameters = new DialogParameters
        {
            ["EquipmentId"] = selectedEquipmentId!.Value,
            ["ParentId"] = parent.Id
        };

        var dialog = await DialogService.ShowAsync<EquipmentPartDialog>("添加子部位", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadEquipmentParts();
            Snackbar.Add("子部位添加成功", Severity.Success);
        }
    }

    private async Task OpenEditDialog(EquipmentPart part)
    {
        var parameters = new DialogParameters
        {
            ["EquipmentPart"] = part
        };

        var dialog = await DialogService.ShowAsync<EquipmentPartDialog>("编辑设备部位", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadEquipmentParts();
            Snackbar.Add("设备部位更新成功", Severity.Success);
        }
    }

    private async Task DeletePart(EquipmentPart part)
    {
        var canDelete = await EquipmentPartService.CanDeletePartAsync(part.Id);
        if (!canDelete)
        {
            Snackbar.Add("该部位存在子部位或关联的报修单，无法删除", Severity.Warning);
            return;
        }

        var parameters = new DialogParameters
        {
            ["ContentText"] = $"确定要删除部位 '{part.Name}' 吗？此操作不可撤销。",
            ["ButtonText"] = "删除",
            ["Color"] = Color.Error
        };

        var dialog = await DialogService.ShowAsync<MudMessageBox>("确认删除", parameters);
        var result = await dialog.Result;

        if (!result.Canceled && bool.TryParse(result.Data?.ToString(), out bool confirmed) && confirmed)
        {
            var deleteResult = await EquipmentPartService.DeleteEquipmentPartAsync(part.Id);
            if (deleteResult.IsSuccess)
            {
                await LoadEquipmentParts();
                Snackbar.Add("设备部位删除成功", Severity.Success);
            }
            else
            {
                Snackbar.Add($"删除失败: {deleteResult.ErrorMessage}", Severity.Error);
            }
        }
    }

    private async Task TogglePartStatus(EquipmentPart part, bool isEnabled)
    {
        var result = await EquipmentPartService.TogglePartStatusAsync(part.Id, isEnabled);
        if (result.IsSuccess)
        {
            part.IsEnabled = isEnabled;
            Snackbar.Add($"部位状态已{(isEnabled ? "启用" : "禁用")}", Severity.Success);
        }
        else
        {
            part.IsEnabled = !isEnabled; // 回滚状态
            Snackbar.Add($"状态切换失败: {result.ErrorMessage}", Severity.Error);
        }
    }

    private RenderFragment RenderPartItem(EquipmentPart part, int level) => __builder =>
    {
        var indent = level * 20;
        <div style="margin-left: @(indent)px; border-left: 1px solid #e0e0e0; padding-left: 10px; margin-bottom: 8px;">
            <MudPaper Class="pa-3" Elevation="1">
                <div style="display: flex; align-items: center; width: 100%;">
                    <div style="flex-grow: 1;">
                        <MudText Typo="Typo.body1">@part.Name</MudText>
                        <MudText Typo="Typo.caption" Color="Color.Secondary">
                            编码: @part.Code
                            @if (!string.IsNullOrEmpty(part.Description))
                            {
                                <span> | @part.Description</span>
                            }
                        </MudText>
                    </div>
                    <div>
                        <MudIconButton Icon="@Icons.Material.Filled.Add" Size="Size.Small"
                                       OnClick="() => OpenCreateChildDialog(part)"
                                       Title="添加子部位" />
                        <MudIconButton Icon="@Icons.Material.Filled.Edit" Size="Size.Small"
                                       OnClick="() => OpenEditDialog(part)"
                                       Title="编辑" />
                        <MudIconButton Icon="@Icons.Material.Filled.Delete" Size="Size.Small"
                                       OnClick="() => DeletePart(part)"
                                       Title="删除" Color="Color.Error" />
                        <MudSwitch T="bool" @bind-Checked="part.IsEnabled"
                                   Size="Size.Small" Color="Color.Success" />
                    </div>
                </div>
            </MudPaper>
            @foreach (var child in equipmentParts.Where(p => p.ParentId == part.Id))
            {
                @RenderPartItem(child, level + 1)
            }
        </div>
    };
}
