namespace CoreHub.Shared.Models
{
    /// <summary>
    /// 部门保养工作负荷统计
    /// </summary>
    public class DepartmentMaintenanceWorkload
    {
        /// <summary>
        /// 部门ID
        /// </summary>
        public int DepartmentId { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        public string DepartmentName { get; set; } = string.Empty;

        /// <summary>
        /// 总计划数
        /// </summary>
        public int TotalPlans { get; set; }

        /// <summary>
        /// 已完成任务数
        /// </summary>
        public int CompletedTasks { get; set; }

        /// <summary>
        /// 待处理任务数
        /// </summary>
        public int PendingTasks { get; set; }

        /// <summary>
        /// 逾期任务数
        /// </summary>
        public int OverdueTasks { get; set; }

        /// <summary>
        /// 平均完成时间（分钟）
        /// </summary>
        public int AverageCompletionTime { get; set; }

        /// <summary>
        /// 工作负荷评分（1-10）
        /// </summary>
        public decimal WorkloadScore { get; set; }

        /// <summary>
        /// 完成率
        /// </summary>
        public decimal CompletionRate { get; set; }

        /// <summary>
        /// 按时完成率
        /// </summary>
        public decimal OnTimeCompletionRate { get; set; }

        /// <summary>
        /// 总工作时长（分钟）
        /// </summary>
        public int TotalWorkDuration { get; set; }

        /// <summary>
        /// 平均每日工作时长（分钟）
        /// </summary>
        public int AverageDailyWorkDuration { get; set; }

        /// <summary>
        /// 人员数量
        /// </summary>
        public int PersonnelCount { get; set; }

        /// <summary>
        /// 人均工作负荷
        /// </summary>
        public decimal WorkloadPerPerson { get; set; }
    }
}
