using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace CoreHub.Shared.Models.Database
{
    /// <summary>
    /// 设备部件实体
    /// </summary>
    [SugarTable("EquipmentComponents")]
    public class EquipmentComponent
    {
        /// <summary>
        /// 部件ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 部件编码（唯一）
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        [Required(ErrorMessage = "部件编码不能为空")]
        [StringLength(50, ErrorMessage = "部件编码长度不能超过50个字符")]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 部件名称
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        [Required(ErrorMessage = "部件名称不能为空")]
        [StringLength(100, ErrorMessage = "部件名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 部件型号
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true)]
        [StringLength(100, ErrorMessage = "部件型号长度不能超过100个字符")]
        public string? Model { get; set; }

        /// <summary>
        /// 规格参数
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "规格参数长度不能超过500个字符")]
        public string? Specifications { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true)]
        [StringLength(100, ErrorMessage = "供应商名称长度不能超过100个字符")]
        public string? Supplier { get; set; }

        /// <summary>
        /// 供应商联系方式
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = true)]
        [StringLength(200, ErrorMessage = "供应商联系方式长度不能超过200个字符")]
        public string? SupplierContact { get; set; }

        /// <summary>
        /// 部件分类
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = true)]
        [StringLength(50, ErrorMessage = "部件分类长度不能超过50个字符")]
        public string? Category { get; set; }

        /// <summary>
        /// 部件类型（1=机械部件,2=电气部件,3=液压部件,4=气动部件,5=传感器,6=其他）（仅用于显示，不映射到数据库）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int ComponentType { get; set; } = 1;

        /// <summary>
        /// 库存数量
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int StockQuantity { get; set; } = 0;

        /// <summary>
        /// 最小库存量（低于此数量时提醒补货）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int MinStockLevel { get; set; } = 0;

        /// <summary>
        /// 计量单位
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = false)]
        [Required(ErrorMessage = "计量单位不能为空")]
        [StringLength(20, ErrorMessage = "计量单位长度不能超过20个字符")]
        public string Unit { get; set; } = "个";

        /// <summary>
        /// 单价
        /// </summary>
        [SugarColumn(DecimalDigits = 2, IsNullable = true)]
        public decimal? UnitPrice { get; set; }

        /// <summary>
        /// 存储位置
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true)]
        [StringLength(100, ErrorMessage = "存储位置长度不能超过100个字符")]
        public string? StorageLocation { get; set; }

        /// <summary>
        /// 部件状态（1=正常,2=停用,3=淘汰）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int Status { get; set; } = 1;

        /// <summary>
        /// 是否为关键部件
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsCritical { get; set; } = false;

        /// <summary>
        /// 预计使用寿命（天）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? ExpectedLifeDays { get; set; }

        /// <summary>
        /// 保修期（天）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? WarrantyDays { get; set; }

        /// <summary>
        /// 部件描述
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "部件描述长度不能超过1000个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 交货周期（天）（仅用于显示，不映射到数据库）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int? LeadTime { get; set; }

        /// <summary>
        /// 保修期（天）- 别名
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int? WarrantyPeriod { get => WarrantyDays; set => WarrantyDays = value; }

        /// <summary>
        /// 是否激活 - 别名
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool IsActive { get => IsEnabled; set => IsEnabled = value; }

        /// <summary>
        /// 是否需要检验（仅用于显示，不映射到数据库）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool RequiresInspection { get; set; } = false;

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
        public string? Remark { get; set; }

        // 导航属性
        /// <summary>
        /// 创建人
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public User? Creator { get; set; }

        /// <summary>
        /// 更新人
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public User? Updater { get; set; }

        /// <summary>
        /// 设备部件关联关系
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<EquipmentComponentMapping> EquipmentMappings { get; set; } = new List<EquipmentComponentMapping>();

        /// <summary>
        /// 保养计划
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<MaintenancePlan> MaintenancePlans { get; set; } = new List<MaintenancePlan>();

        /// <summary>
        /// 保养记录
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<MaintenanceRecord> MaintenanceRecords { get; set; } = new List<MaintenanceRecord>();

        /// <summary>
        /// 部件状态名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string StatusName => Status switch
        {
            1 => "正常",
            2 => "停用",
            3 => "淘汰",
            _ => "未知"
        };

        /// <summary>
        /// 库存状态（基于最小库存量）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string StockStatus
        {
            get
            {
                if (StockQuantity <= 0)
                    return "缺货";
                else if (StockQuantity <= MinStockLevel)
                    return "库存不足";
                else
                    return "库存充足";
            }
        }

        /// <summary>
        /// 是否库存不足
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool IsLowStock => StockQuantity <= MinStockLevel;

        /// <summary>
        /// 是否缺货
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool IsOutOfStock => StockQuantity <= 0;

        /// <summary>
        /// 总价值（单价 × 库存数量）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public decimal? TotalValue => UnitPrice.HasValue ? UnitPrice.Value * StockQuantity : null;
    }
}
