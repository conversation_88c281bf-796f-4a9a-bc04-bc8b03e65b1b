@using MudBlazor

@if (IsLoading)
{
    <div class="loading-container">
        @if (ShowOverlay)
        {
            <MudOverlay Visible="true" DarkBackground="true" Absolute="true">
                <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" />
                @if (!string.IsNullOrEmpty(LoadingText))
                {
                    <MudText Typo="Typo.body1" Class="mt-4" Style="color: white;">@LoadingText</MudText>
                }
            </MudOverlay>
        }
        else
        {
            <div class="loading-content">
                <MudProgressCircular Color="Color.Primary" Size="@ProgressSize" Indeterminate="true" />
                @if (!string.IsNullOrEmpty(LoadingText))
                {
                    <MudText Typo="Typo.body2" Class="mt-2 text-center">@LoadingText</MudText>
                }
            </div>
        }
    </div>
}
else if (HasError)
{
    <div class="error-container">
        <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Class="mb-4">
            <div class="d-flex align-center">
                <MudIcon Icon="Icons.Material.Filled.Error" Class="mr-2" />
                <div>
                    <MudText Typo="Typo.subtitle2">加载失败</MudText>
                    @if (!string.IsNullOrEmpty(ErrorMessage))
                    {
                        <MudText Typo="Typo.body2">@ErrorMessage</MudText>
                    }
                </div>
            </div>
        </MudAlert>
        
        @if (ShowRetryButton)
        {
            <div class="text-center">
                <MudButton Variant="Variant.Outlined"
                          Color="Color.Primary"
                          StartIcon="Icons.Material.Filled.Refresh"
                          OnClick="OnRetry">
                    重试
                </MudButton>
            </div>
        }
    </div>
}
else if (IsEmpty)
{
    <div class="empty-container">
        <MudPaper Class="pa-8 text-center" Elevation="0">
            <MudIcon Icon="@EmptyIcon" Size="Size.Large" Color="Color.Secondary" Class="mb-4" />
            <MudText Typo="Typo.h6" Color="Color.Secondary" Class="mb-2">@EmptyTitle</MudText>
            @if (!string.IsNullOrEmpty(EmptyMessage))
            {
                <MudText Typo="Typo.body2" Color="Color.Secondary">@EmptyMessage</MudText>
            }
            @if (ShowCreateButton && !string.IsNullOrEmpty(CreateButtonText))
            {
                <MudButton Variant="Variant.Filled"
                          Color="Color.Primary"
                          StartIcon="Icons.Material.Filled.Add"
                          OnClick="OnCreate"
                          Class="mt-4">
                    @CreateButtonText
                </MudButton>
            }
        </MudPaper>
    </div>
}
else
{
    @ChildContent
}

<style>
    .loading-container {
        position: relative;
        min-height: 200px;
    }
    
    .loading-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 200px;
        padding: 2rem;
    }
    
    .error-container {
        padding: 2rem;
    }
    
    .empty-container {
        min-height: 300px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>

@code {
    [Parameter] public bool IsLoading { get; set; }
    [Parameter] public bool HasError { get; set; }
    [Parameter] public bool IsEmpty { get; set; }
    [Parameter] public string? LoadingText { get; set; } = "加载中...";
    [Parameter] public string? ErrorMessage { get; set; }
    [Parameter] public bool ShowOverlay { get; set; } = false;
    [Parameter] public bool ShowRetryButton { get; set; } = true;
    [Parameter] public Size ProgressSize { get; set; } = Size.Medium;
    
    [Parameter] public string EmptyTitle { get; set; } = "暂无数据";
    [Parameter] public string? EmptyMessage { get; set; }
    [Parameter] public string EmptyIcon { get; set; } = Icons.Material.Filled.Inbox;
    [Parameter] public bool ShowCreateButton { get; set; } = false;
    [Parameter] public string? CreateButtonText { get; set; }
    
    [Parameter] public EventCallback OnRetry { get; set; }
    [Parameter] public EventCallback OnCreate { get; set; }
    [Parameter] public RenderFragment? ChildContent { get; set; }

    /// <summary>
    /// 设置加载状态
    /// </summary>
    public void SetLoading(bool loading, string? text = null)
    {
        IsLoading = loading;
        HasError = false;
        IsEmpty = false;
        if (!string.IsNullOrEmpty(text))
        {
            LoadingText = text;
        }
        StateHasChanged();
    }

    /// <summary>
    /// 设置错误状态
    /// </summary>
    public void SetError(string? message = null)
    {
        IsLoading = false;
        HasError = true;
        IsEmpty = false;
        if (!string.IsNullOrEmpty(message))
        {
            ErrorMessage = message;
        }
        StateHasChanged();
    }

    /// <summary>
    /// 设置空数据状态
    /// </summary>
    public void SetEmpty(string? title = null, string? message = null)
    {
        IsLoading = false;
        HasError = false;
        IsEmpty = true;
        if (!string.IsNullOrEmpty(title))
        {
            EmptyTitle = title;
        }
        if (!string.IsNullOrEmpty(message))
        {
            EmptyMessage = message;
        }
        StateHasChanged();
    }

    /// <summary>
    /// 设置成功状态（显示内容）
    /// </summary>
    public void SetSuccess()
    {
        IsLoading = false;
        HasError = false;
        IsEmpty = false;
        StateHasChanged();
    }
}
