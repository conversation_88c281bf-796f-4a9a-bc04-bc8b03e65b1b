using SqlSugar;
using CoreHub.Shared.Models.Database;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace CoreHub.Shared.Data
{
    /// <summary>
    /// 数据库上下文
    /// </summary>
    public class DatabaseContext
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<DatabaseContext> _logger;
        private readonly ISqlSugarClient _db;

        public DatabaseContext(IConfiguration configuration, ILogger<DatabaseContext> logger)
        {
            _configuration = configuration;
            _logger = logger;

            // 获取连接字符串
            var connectionString = _configuration.GetConnectionString("DefaultConnection") 
                ?? throw new InvalidOperationException("数据库连接字符串未配置");

            // 配置SqlSugar
            _db = new SqlSugarClient(new ConnectionConfig
            {
                ConnectionString = connectionString,
                DbType = DbType.SqlServer,
                IsAutoCloseConnection = false, // 修改为false，避免DataReader过早关闭
                InitKeyType = InitKeyType.Attribute,
                ConfigureExternalServices = new ConfigureExternalServices
                {
                    EntityService = (property, column) =>
                    {
                        // 自动处理创建时间和更新时间
                        if (property.Name.Equals("CreatedAt", StringComparison.OrdinalIgnoreCase))
                        {
                            column.IsOnlyIgnoreUpdate = true; // 更新时忽略
                        }
                        if (property.Name.Equals("UpdatedAt", StringComparison.OrdinalIgnoreCase))
                        {
                            column.IsOnlyIgnoreInsert = true; // 插入时忽略
                        }
                    }
                }
            }, db =>
            {
                // 配置日志
                db.Aop.OnLogExecuting = (sql, pars) =>
                {
                    _logger.LogDebug("执行SQL：{sql}，参数：{parameters}", sql, 
                        string.Join(",", pars?.Select(p => $"{p.ParameterName}={p.Value}") ?? Array.Empty<string>()));
                };

                db.Aop.OnError = (exp) =>
                {
                    _logger.LogError(exp, "SQL执行异常：{message}", exp.Message);
                };
            });
        }

        /// <summary>
        /// 获取SqlSugar客户端
        /// </summary>
        public ISqlSugarClient Db => _db;

        #region 实体集合

        /// <summary>
        /// 用户
        /// </summary>
        public ISugarQueryable<User> Users => _db.Queryable<User>();

        /// <summary>
        /// 角色
        /// </summary>
        public ISugarQueryable<Role> Roles => _db.Queryable<Role>();

        /// <summary>
        /// 权限
        /// </summary>
        public ISugarQueryable<Permission> Permissions => _db.Queryable<Permission>();

        /// <summary>
        /// 用户角色关联
        /// </summary>
        public ISugarQueryable<UserRole> UserRoles => _db.Queryable<UserRole>();

        /// <summary>
        /// 角色权限关联
        /// </summary>
        public ISugarQueryable<RolePermission> RolePermissions => _db.Queryable<RolePermission>();

        /// <summary>
        /// 用户直接权限关联
        /// </summary>
        public ISugarQueryable<UserPermission> UserPermissions => _db.Queryable<UserPermission>();

        /// <summary>
        /// 菜单项
        /// </summary>
        public ISugarQueryable<MenuItem> MenuItems => _db.Queryable<MenuItem>();

        /// <summary>
        /// 部门
        /// </summary>
        public ISugarQueryable<Department> Departments => _db.Queryable<Department>();

        /// <summary>
        /// 设备型号
        /// </summary>
        public ISugarQueryable<EquipmentModel> EquipmentModels => _db.Queryable<EquipmentModel>();

        /// <summary>
        /// 位置
        /// </summary>
        public ISugarQueryable<Location> Locations => _db.Queryable<Location>();

        /// <summary>
        /// 设备
        /// </summary>
        public ISugarQueryable<Equipment> Equipment => _db.Queryable<Equipment>();

        /// <summary>
        /// 设备部位
        /// </summary>
        public ISugarQueryable<EquipmentPart> EquipmentParts => _db.Queryable<EquipmentPart>();

        /// <summary>
        /// 报修单
        /// </summary>
        public ISugarQueryable<RepairOrder> RepairOrders => _db.Queryable<RepairOrder>();

        /// <summary>
        /// 报修单附件
        /// </summary>
        public ISugarQueryable<RepairOrderAttachment> RepairOrderAttachments => _db.Queryable<RepairOrderAttachment>();

        /// <summary>
        /// 维修单零件申请
        /// </summary>
        public ISugarQueryable<RepairOrderPartRequest> RepairOrderPartRequests => _db.Queryable<RepairOrderPartRequest>();

        /// <summary>
        /// 设备部件
        /// </summary>
        public ISugarQueryable<EquipmentComponent> EquipmentComponents => _db.Queryable<EquipmentComponent>();

        /// <summary>
        /// 设备部件关联关系
        /// </summary>
        public ISugarQueryable<EquipmentComponentMapping> EquipmentComponentMappings => _db.Queryable<EquipmentComponentMapping>();

        /// <summary>
        /// 保养计划
        /// </summary>
        public ISugarQueryable<MaintenancePlan> MaintenancePlans => _db.Queryable<MaintenancePlan>();

        /// <summary>
        /// 保养记录
        /// </summary>
        public ISugarQueryable<MaintenanceRecord> MaintenanceRecords => _db.Queryable<MaintenanceRecord>();

        #endregion



        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _db?.Dispose();
        }
    }
}
