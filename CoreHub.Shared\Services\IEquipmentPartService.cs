using CoreHub.Shared.Models.Database;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 设备部位服务接口
    /// </summary>
    public interface IEquipmentPartService
    {
        /// <summary>
        /// 获取所有设备部位
        /// </summary>
        Task<List<EquipmentPart>> GetAllEquipmentPartsAsync();

        /// <summary>
        /// 根据ID获取设备部位
        /// </summary>
        Task<EquipmentPart?> GetEquipmentPartByIdAsync(int id);

        /// <summary>
        /// 根据设备ID获取设备部位列表
        /// </summary>
        Task<List<EquipmentPart>> GetEquipmentPartsByEquipmentIdAsync(int equipmentId);

        /// <summary>
        /// 获取设备的根级部位（一级部位）
        /// </summary>
        Task<List<EquipmentPart>> GetRootPartsByEquipmentIdAsync(int equipmentId);

        /// <summary>
        /// 获取指定部位的子部位
        /// </summary>
        Task<List<EquipmentPart>> GetChildPartsByParentIdAsync(int parentId);

        /// <summary>
        /// 获取设备部位树形结构
        /// </summary>
        Task<List<EquipmentPart>> GetEquipmentPartTreeAsync(int equipmentId);

        /// <summary>
        /// 获取设备部位的扁平化列表（用于下拉选择）
        /// </summary>
        Task<List<EquipmentPartDto>> GetEquipmentPartsFlatListAsync(int equipmentId);

        /// <summary>
        /// 创建设备部位
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage, int? PartId)> CreateEquipmentPartAsync(EquipmentPart equipmentPart);

        /// <summary>
        /// 更新设备部位
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateEquipmentPartAsync(EquipmentPart equipmentPart);

        /// <summary>
        /// 删除设备部位
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> DeleteEquipmentPartAsync(int id);

        /// <summary>
        /// 批量删除设备部位
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> BatchDeleteEquipmentPartsAsync(List<int> ids);

        /// <summary>
        /// 检查部位编码是否唯一
        /// </summary>
        Task<bool> IsPartCodeUniqueAsync(string code, int equipmentId, int? excludeId = null);

        /// <summary>
        /// 检查是否可以删除部位（没有子部位且没有关联的报修单）
        /// </summary>
        Task<bool> CanDeletePartAsync(int id);

        /// <summary>
        /// 获取部位的完整路径
        /// </summary>
        Task<string> GetPartFullPathAsync(int id);

        /// <summary>
        /// 移动部位到新的父级
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> MovePartAsync(int partId, int? newParentId);

        /// <summary>
        /// 复制部位结构到其他设备
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> CopyPartStructureAsync(int sourceEquipmentId, int targetEquipmentId);

        /// <summary>
        /// 获取部位使用统计（关联的报修单数量）
        /// </summary>
        Task<Dictionary<int, int>> GetPartUsageStatisticsAsync(int equipmentId);

        /// <summary>
        /// 搜索设备部位
        /// </summary>
        Task<List<EquipmentPart>> SearchEquipmentPartsAsync(EquipmentPartSearchDto searchDto);

        /// <summary>
        /// 启用/禁用设备部位
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> TogglePartStatusAsync(int id, bool isEnabled);

        /// <summary>
        /// 批量更新部位排序
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> UpdatePartSortOrderAsync(List<PartSortOrderDto> sortOrders);
    }

    /// <summary>
    /// 设备部位DTO（用于下拉选择等场景）
    /// </summary>
    public class EquipmentPartDto
    {
        public int Id { get; set; }
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string IndentedName { get; set; } = string.Empty;
        public int Level { get; set; }
        public int? ParentId { get; set; }
        public bool IsEnabled { get; set; }
        public string? Description { get; set; }
    }

    /// <summary>
    /// 设备部位搜索DTO
    /// </summary>
    public class EquipmentPartSearchDto
    {
        public string? SearchText { get; set; }
        public int? EquipmentId { get; set; }
        public int? ParentId { get; set; }
        public int? Level { get; set; }
        public bool? IsEnabled { get; set; }
    }

    /// <summary>
    /// 部位排序DTO
    /// </summary>
    public class PartSortOrderDto
    {
        public int Id { get; set; }
        public int SortOrder { get; set; }
    }
}
