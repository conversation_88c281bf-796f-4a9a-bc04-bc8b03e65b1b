using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using CoreHub.Shared.Models.Common;
using CoreHub.Shared.Models;
using SqlSugar;
using System.Linq;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 保养计划管理服务实现
    /// </summary>
    public class MaintenancePlanService : IMaintenancePlanService
    {
        private readonly DatabaseContext _context;

        public MaintenancePlanService(DatabaseContext context)
        {
            _context = context;
        }

        #region 基础CRUD操作

        /// <summary>
        /// 获取所有保养计划
        /// </summary>
        public async Task<List<MaintenancePlan>> GetAllPlansAsync()
        {
            return await _context.MaintenancePlans
                .Where(p => p.IsEnabled)
                .OrderBy(p => p.Priority)
                .OrderBy(p => p.NextMaintenanceDate)
                .ToListAsync();
        }

        /// <summary>
        /// 根据ID获取保养计划
        /// </summary>
        public async Task<MaintenancePlan?> GetPlanByIdAsync(int id)
        {
            return await _context.MaintenancePlans
                .Where(p => p.Id == id)
                .FirstAsync();
        }

        /// <summary>
        /// 根据编码获取保养计划
        /// </summary>
        public async Task<MaintenancePlan?> GetPlanByCodeAsync(string code)
        {
            return await _context.MaintenancePlans
                .Where(p => p.Code == code)
                .FirstAsync();
        }

        /// <summary>
        /// 创建保养计划
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage, int? Id)> CreatePlanAsync(MaintenancePlan plan)
        {
            try
            {
                // 检查编码是否已存在
                var existingPlan = await _context.MaintenancePlans
                    .Where(p => p.Code == plan.Code)
                    .FirstAsync();

                if (existingPlan != null)
                {
                    return (false, "保养计划编码已存在", null);
                }

                // 检查部件是否存在
                var component = await _context.EquipmentComponents
                    .Where(c => c.Id == plan.ComponentId && c.IsEnabled)
                    .FirstAsync();

                if (component == null)
                {
                    return (false, "指定的部件不存在或已禁用", null);
                }

                plan.CreatedAt = DateTime.Now;
                
                // 计算下次保养日期
                if (!plan.NextMaintenanceDate.HasValue)
                {
                    plan.NextMaintenanceDate = await CalculateNextMaintenanceDateAsync(plan);
                }

                var result = await _context.Db.Insertable(plan).ExecuteReturnIdentityAsync();
                return (true, string.Empty, result);
            }
            catch (Exception ex)
            {
                return (false, $"创建保养计划失败：{ex.Message}", null);
            }
        }

        /// <summary>
        /// 更新保养计划
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> UpdatePlanAsync(MaintenancePlan plan)
        {
            try
            {
                // 检查编码是否与其他计划冲突
                var existingPlan = await _context.MaintenancePlans
                    .Where(p => p.Code == plan.Code && p.Id != plan.Id)
                    .FirstAsync();

                if (existingPlan != null)
                {
                    return (false, "保养计划编码已存在");
                }

                plan.UpdatedAt = DateTime.Now;
                await _context.Db.Updateable(plan).ExecuteCommandAsync();
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, $"更新保养计划失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 删除保养计划
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> DeletePlanAsync(int id)
        {
            try
            {
                // 检查是否有关联的保养记录
                var hasRecords = await _context.MaintenanceRecords
                    .Where(r => r.MaintenancePlanId == id)
                    .AnyAsync();

                if (hasRecords)
                {
                    return (false, "该保养计划已有保养记录，无法删除");
                }

                await _context.Db.Deleteable<MaintenancePlan>().Where(p => p.Id == id).ExecuteCommandAsync();
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, $"删除保养计划失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 切换保养计划启用状态
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> ToggleStatusAsync(int id)
        {
            try
            {
                var plan = await GetPlanByIdAsync(id);
                if (plan == null)
                {
                    return (false, "保养计划不存在");
                }

                plan.IsEnabled = !plan.IsEnabled;
                plan.UpdatedAt = DateTime.Now;

                await _context.Db.Updateable(plan).ExecuteCommandAsync();
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, $"切换状态失败：{ex.Message}");
            }
        }

        #endregion

        #region 搜索和筛选

        /// <summary>
        /// 搜索保养计划（分页版本）
        /// </summary>
        public async Task<PagedResult<MaintenancePlan>> SearchPlansAsync(MaintenancePlanFilter filter)
        {
            try
            {
                var query = _context.MaintenancePlans.Where(p => p.IsEnabled);

                // 应用搜索条件
                if (!string.IsNullOrEmpty(filter.Keyword))
                {
                    query = query.Where(p => p.Name.Contains(filter.Keyword) ||
                                           p.MaintenanceContent.Contains(filter.Keyword));
                }

                if (filter.ComponentId.HasValue)
                {
                    query = query.Where(p => p.ComponentId == filter.ComponentId.Value);
                }

                if (filter.MaintenanceType.HasValue)
                {
                    query = query.Where(p => p.MaintenanceType == filter.MaintenanceType.Value);
                }

                if (filter.CycleType.HasValue)
                {
                    query = query.Where(p => p.CycleType == filter.CycleType.Value);
                }

                if (filter.Priority.HasValue)
                {
                    query = query.Where(p => p.Priority == filter.Priority.Value);
                }

                if (filter.IsActive.HasValue)
                {
                    query = query.Where(p => p.IsActive == filter.IsActive.Value);
                }

                if (filter.IsOverdue.HasValue && filter.IsOverdue.Value)
                {
                    query = query.Where(p => p.NextMaintenanceDate.HasValue && p.NextMaintenanceDate.Value < DateTime.Today);
                }

                if (filter.NextMaintenanceStartDate.HasValue)
                {
                    query = query.Where(p => p.NextMaintenanceDate.HasValue && p.NextMaintenanceDate.Value >= filter.NextMaintenanceStartDate.Value);
                }

                if (filter.NextMaintenanceEndDate.HasValue)
                {
                    query = query.Where(p => p.NextMaintenanceDate.HasValue && p.NextMaintenanceDate.Value <= filter.NextMaintenanceEndDate.Value);
                }

                // 获取总数
                var totalCount = await query.CountAsync();

                // 应用分页
                var items = await query
                    .OrderBy(p => p.Name)
                    .Skip((filter.PageIndex - 1) * filter.PageSize)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return PagedResult<MaintenancePlan>.Create(items, totalCount, filter.PageIndex, filter.PageSize);
            }
            catch (Exception ex)
            {
                throw new Exception($"搜索保养计划失败：{ex.Message}", ex);
            }
        }

        /// <summary>
        /// 搜索保养计划（简单版本）
        /// </summary>
        public async Task<List<MaintenancePlan>> SearchPlansAsync(string? keyword = null, int? componentId = null, int? maintenanceType = null, int? status = null, int? responsibleDepartmentId = null)
        {
            var query = _context.MaintenancePlans.Where(p => p.IsEnabled);

            if (!string.IsNullOrEmpty(keyword))
            {
                query = query.Where(p => p.Code.Contains(keyword) || 
                                       p.Name.Contains(keyword) ||
                                       p.MaintenanceContent.Contains(keyword));
            }

            if (componentId.HasValue)
            {
                query = query.Where(p => p.ComponentId == componentId.Value);
            }

            if (maintenanceType.HasValue)
            {
                query = query.Where(p => p.MaintenanceType == maintenanceType.Value);
            }

            if (status.HasValue)
            {
                query = query.Where(p => p.Status == status.Value);
            }

            if (responsibleDepartmentId.HasValue)
            {
                query = query.Where(p => p.ResponsibleDepartmentId == responsibleDepartmentId.Value);
            }

            return await query.OrderBy(p => p.Priority).OrderBy(p => p.NextMaintenanceDate).ToListAsync();
        }

        /// <summary>
        /// 获取部件的保养计划
        /// </summary>
        public async Task<List<MaintenancePlan>> GetPlansByComponentIdAsync(int componentId)
        {
            return await _context.MaintenancePlans
                .Where(p => p.ComponentId == componentId && p.IsEnabled)
                .OrderBy(p => p.Priority)
                .OrderBy(p => p.NextMaintenanceDate)
                .ToListAsync();
        }

        /// <summary>
        /// 获取设备的所有保养计划
        /// </summary>
        public async Task<List<MaintenancePlan>> GetPlansByEquipmentIdAsync(int equipmentId)
        {
            // 通过设备部件关联获取保养计划
            var componentIds = await _context.EquipmentComponentMappings
                .Where(m => m.EquipmentId == equipmentId && m.IsEnabled)
                .Select(m => m.ComponentId)
                .ToListAsync();

            return await _context.MaintenancePlans
                .Where(p => componentIds.Contains(p.ComponentId) && p.IsEnabled)
                .OrderBy(p => p.Priority)
                .OrderBy(p => p.NextMaintenanceDate)
                .ToListAsync();
        }

        #endregion

        #region 保养提醒和调度

        /// <summary>
        /// 获取需要提醒的保养计划
        /// </summary>
        public async Task<List<MaintenancePlan>> GetPlansNeedingReminderAsync(int? days = null)
        {
            var query = _context.MaintenancePlans
                .Where(p => p.IsEnabled && p.Status == 1 && p.NextMaintenanceDate.HasValue);

            if (days.HasValue)
            {
                var reminderDate = DateTime.Now.AddDays(days.Value);
                query = query.Where(p => p.NextMaintenanceDate.Value <= reminderDate);
            }
            else
            {
                // 使用计划中的提醒天数
                var currentDate = DateTime.Now;
                query = query.Where(p => p.NextMaintenanceDate.Value <= currentDate.AddDays(p.ReminderDays));
            }

            return await query.OrderBy(p => p.NextMaintenanceDate).ToListAsync();
        }

        /// <summary>
        /// 获取已过期的保养计划
        /// </summary>
        public async Task<List<MaintenancePlan>> GetOverduePlansAsync()
        {
            var currentDate = DateTime.Now.Date;
            return await _context.MaintenancePlans
                .Where(p => p.IsEnabled && p.Status == 1 && 
                           p.NextMaintenanceDate.HasValue && 
                           p.NextMaintenanceDate.Value.Date < currentDate)
                .OrderBy(p => p.NextMaintenanceDate)
                .ToListAsync();
        }

        /// <summary>
        /// 获取今日需要保养的计划
        /// </summary>
        public async Task<List<MaintenancePlan>> GetTodayMaintenancePlansAsync()
        {
            var today = DateTime.Now.Date;
            return await _context.MaintenancePlans
                .Where(p => p.IsEnabled && p.Status == 1 && 
                           p.NextMaintenanceDate.HasValue && 
                           p.NextMaintenanceDate.Value.Date == today)
                .OrderBy(p => p.Priority)
                .ToListAsync();
        }

        /// <summary>
        /// 获取本周需要保养的计划
        /// </summary>
        public async Task<List<MaintenancePlan>> GetWeeklyMaintenancePlansAsync()
        {
            var startOfWeek = DateTime.Now.Date.AddDays(-(int)DateTime.Now.DayOfWeek);
            var endOfWeek = startOfWeek.AddDays(6);

            return await _context.MaintenancePlans
                .Where(p => p.IsEnabled && p.Status == 1 &&
                           p.NextMaintenanceDate.HasValue &&
                           p.NextMaintenanceDate.Value.Date >= startOfWeek &&
                           p.NextMaintenanceDate.Value.Date <= endOfWeek)
                .OrderBy(p => p.NextMaintenanceDate)
                .OrderBy(p => p.Priority)
                .ToListAsync();
        }

        /// <summary>
        /// 获取本月需要保养的计划
        /// </summary>
        public async Task<List<MaintenancePlan>> GetMonthlyMaintenancePlansAsync()
        {
            var startOfMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);

            return await _context.MaintenancePlans
                .Where(p => p.IsEnabled && p.Status == 1 &&
                           p.NextMaintenanceDate.HasValue &&
                           p.NextMaintenanceDate.Value.Date >= startOfMonth &&
                           p.NextMaintenanceDate.Value.Date <= endOfMonth)
                .OrderBy(p => p.NextMaintenanceDate)
                .OrderBy(p => p.Priority)
                .ToListAsync();
        }

        #endregion

        #region 周期计算

        /// <summary>
        /// 计算下次保养日期
        /// </summary>
        public async Task<DateTime?> CalculateNextMaintenanceDateAsync(MaintenancePlan plan, DateTime? lastMaintenanceDate = null)
        {
            try
            {
                var baseDate = lastMaintenanceDate ?? plan.LastMaintenanceDate ?? DateTime.Now;

                return plan.CycleType switch
                {
                    1 => baseDate.AddDays(plan.CycleValue), // 按天数
                    2 => baseDate.AddDays(plan.CycleValue * 7), // 按周数
                    3 => baseDate.AddMonths(plan.CycleValue), // 按月数
                    4 => baseDate.AddYears(plan.CycleValue), // 按年数
                    5 => await CalculateUsageBasedDateAsync(plan), // 按使用次数
                    6 => await CalculateRuntimeBasedDateAsync(plan), // 按运行时间
                    7 => await CalculateProductionBasedDateAsync(plan), // 按生产数量
                    _ => baseDate.AddDays(plan.CycleValue)
                };
            }
            catch (Exception)
            {
                return DateTime.Now.AddDays(plan.CycleValue);
            }
        }

        /// <summary>
        /// 更新下次保养日期
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateNextMaintenanceDateAsync(int planId, DateTime lastMaintenanceDate)
        {
            try
            {
                var plan = await GetPlanByIdAsync(planId);
                if (plan == null)
                {
                    return (false, "保养计划不存在");
                }

                plan.LastMaintenanceDate = lastMaintenanceDate;
                plan.NextMaintenanceDate = await CalculateNextMaintenanceDateAsync(plan);
                plan.UpdatedAt = DateTime.Now;

                await _context.Db.Updateable(plan).ExecuteCommandAsync();
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, $"更新下次保养日期失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 批量更新下次保养日期
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> BatchUpdateNextMaintenanceDateAsync(List<(int PlanId, DateTime LastMaintenanceDate)> updates)
        {
            try
            {
                await _context.Db.Ado.BeginTranAsync();

                foreach (var update in updates)
                {
                    var result = await UpdateNextMaintenanceDateAsync(update.PlanId, update.LastMaintenanceDate);
                    if (!result.IsSuccess)
                    {
                        await _context.Db.Ado.RollbackTranAsync();
                        return (false, $"批量更新失败：{result.ErrorMessage}");
                    }
                }

                await _context.Db.Ado.CommitTranAsync();
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                await _context.Db.Ado.RollbackTranAsync();
                return (false, $"批量更新下次保养日期失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 重新计算所有保养计划的下次保养日期
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage, int UpdatedCount)> RecalculateAllNextMaintenanceDatesAsync()
        {
            try
            {
                var plans = await GetAllPlansAsync();
                int updatedCount = 0;
                await _context.Db.Ado.BeginTranAsync();

                foreach (var plan in plans)
                {
                    var newDate = await CalculateNextMaintenanceDateAsync(plan);
                    if (newDate != plan.NextMaintenanceDate)
                    {
                        plan.NextMaintenanceDate = newDate;
                        plan.UpdatedAt = DateTime.Now;
                        await _context.Db.Updateable(plan).ExecuteCommandAsync();
                        updatedCount++;
                    }
                }

                await _context.Db.Ado.CommitTranAsync();
                return (true, string.Empty, updatedCount);
            }
            catch (Exception ex)
            {
                await _context.Db.Ado.RollbackTranAsync();
                return (false, $"重新计算保养日期失败：{ex.Message}", 0);
            }
        }

        #endregion

        #region 保养计划执行

        /// <summary>
        /// 标记保养计划为已执行
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> MarkPlanAsExecutedAsync(int planId, DateTime executionDate, int? maintenanceRecordId = null)
        {
            try
            {
                var result = await UpdateNextMaintenanceDateAsync(planId, executionDate);
                if (!result.IsSuccess)
                {
                    return result;
                }

                // TODO: 如果有保养记录ID，可以建立关联
                // if (maintenanceRecordId.HasValue)
                // {
                //     await LinkMaintenanceRecordToPlanAsync(planId, maintenanceRecordId.Value);
                // }

                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, $"标记计划执行失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 暂停保养计划
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> PausePlanAsync(int planId, string reason)
        {
            try
            {
                var plan = await GetPlanByIdAsync(planId);
                if (plan == null)
                {
                    return (false, "保养计划不存在");
                }

                plan.Status = 2; // 暂停状态
                plan.UpdatedAt = DateTime.Now;
                // TODO: 记录暂停原因
                // plan.PauseReason = reason;

                await _context.Db.Updateable(plan).ExecuteCommandAsync();
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, $"暂停计划失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 恢复保养计划
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> ResumePlanAsync(int planId)
        {
            try
            {
                var plan = await GetPlanByIdAsync(planId);
                if (plan == null)
                {
                    return (false, "保养计划不存在");
                }

                plan.Status = 1; // 活动状态
                plan.UpdatedAt = DateTime.Now;

                await _context.Db.Updateable(plan).ExecuteCommandAsync();
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, $"恢复计划失败：{ex.Message}");
            }
        }

        #endregion

        #region 私有辅助方法

        private async Task<DateTime?> CalculateUsageBasedDateAsync(MaintenancePlan plan)
        {
            // TODO: 根据设备使用次数计算下次保养日期
            // 这需要从设备使用记录中获取数据
            return DateTime.Now.AddDays(30); // 临时返回30天后
        }

        private async Task<DateTime?> CalculateRuntimeBasedDateAsync(MaintenancePlan plan)
        {
            // TODO: 根据设备运行时间计算下次保养日期
            // 这需要从设备运行记录中获取数据
            return DateTime.Now.AddDays(30); // 临时返回30天后
        }

        private async Task<DateTime?> CalculateProductionBasedDateAsync(MaintenancePlan plan)
        {
            // TODO: 根据生产数量计算下次保养日期
            // 这需要从生产记录中获取数据
            return DateTime.Now.AddDays(30); // 临时返回30天后
        }

        #endregion

        #region 统计分析

        /// <summary>
        /// 获取保养计划统计信息
        /// </summary>
        public async Task<MaintenancePlanStatistics> GetPlanStatisticsAsync()
        {
            var allPlans = await _context.MaintenancePlans.Where(p => p.IsEnabled).ToListAsync();
            var today = DateTime.Now.Date;

            return new MaintenancePlanStatistics
            {
                TotalPlans = allPlans.Count,
                ActivePlans = allPlans.Count(p => p.Status == 1),
                PausedPlans = allPlans.Count(p => p.Status == 2),
                OverduePlans = allPlans.Count(p => p.Status == 1 && p.NextMaintenanceDate.HasValue && p.NextMaintenanceDate.Value.Date < today),
                TodayPlans = allPlans.Count(p => p.Status == 1 && p.NextMaintenanceDate.HasValue && p.NextMaintenanceDate.Value.Date == today),
                WeekPlans = allPlans.Count(p => p.Status == 1 && p.NextMaintenanceDate.HasValue &&
                                         p.NextMaintenanceDate.Value.Date >= today &&
                                         p.NextMaintenanceDate.Value.Date <= today.AddDays(7)),
                MonthPlans = allPlans.Count(p => p.Status == 1 && p.NextMaintenanceDate.HasValue &&
                                          p.NextMaintenanceDate.Value.Date >= today &&
                                          p.NextMaintenanceDate.Value.Date <= today.AddDays(30))
            };
        }

        /// <summary>
        /// 获取保养类型分布统计
        /// </summary>
        public async Task<Dictionary<int, int>> GetMaintenanceTypeDistributionAsync()
        {
            var plans = await _context.MaintenancePlans
                .Where(p => p.IsEnabled)
                .ToListAsync();

            return plans.GroupBy(p => p.MaintenanceType)
                       .ToDictionary(g => g.Key, g => g.Count());
        }

        /// <summary>
        /// 获取部门保养计划分布
        /// </summary>
        public async Task<Dictionary<int, int>> GetDepartmentPlanDistributionAsync()
        {
            var plans = await _context.MaintenancePlans
                .Where(p => p.IsEnabled && p.ResponsibleDepartmentId.HasValue)
                .ToListAsync();

            return plans.GroupBy(p => p.ResponsibleDepartmentId.Value)
                       .ToDictionary(g => g.Key, g => g.Count());
        }

        /// <summary>
        /// 获取保养计划执行率统计
        /// </summary>
        public async Task<MaintenancePlanExecutionStatistics> GetPlanExecutionStatisticsAsync(DateTime startDate, DateTime endDate)
        {
            var plans = await _context.MaintenancePlans
                .Where(p => p.IsEnabled)
                .ToListAsync();

            var records = await _context.MaintenanceRecords
                .Where(r => r.MaintenanceDate >= startDate && r.MaintenanceDate <= endDate && r.ReviewStatus == 2)
                .ToListAsync();

            var totalPlans = plans.Count;
            var executedPlans = records.Where(r => r.MaintenancePlanId.HasValue).Select(r => r.MaintenancePlanId.Value).Distinct().Count();
            var onTimeExecutions = records.Count(r => r.MaintenancePlanId.HasValue &&
                plans.Any(p => p.Id == r.MaintenancePlanId.Value &&
                              p.NextMaintenanceDate.HasValue &&
                              r.MaintenanceDate <= p.NextMaintenanceDate.Value));

            return new MaintenancePlanExecutionStatistics
            {
                TotalPlans = totalPlans,
                ExecutedPlans = executedPlans,
                ExecutionRate = totalPlans > 0 ? (decimal)executedPlans / totalPlans * 100 : 0,
                OnTimeExecutions = onTimeExecutions,
                OnTimeRate = executedPlans > 0 ? (decimal)onTimeExecutions / executedPlans * 100 : 0,
                OverdueExecutions = executedPlans - onTimeExecutions
            };
        }

        /// <summary>
        /// 获取部门保养工作量统计
        /// </summary>
        public async Task<List<DepartmentMaintenanceWorkload>> GetDepartmentWorkloadStatisticsAsync(int? departmentId = null, int days = 30)
        {
            var startDate = DateTime.Now.AddDays(-days);
            var endDate = DateTime.Now;

            var query = _context.MaintenancePlans
                .Where(p => p.IsEnabled && p.ResponsibleDepartmentId.HasValue);

            if (departmentId.HasValue)
            {
                query = query.Where(p => p.ResponsibleDepartmentId == departmentId.Value);
            }

            var plans = await query.ToListAsync();

            var records = await _context.MaintenanceRecords
                .Where(r => r.MaintenanceDate >= startDate && r.MaintenanceDate <= endDate && r.ReviewStatus == 2)
                .ToListAsync();

            return plans.GroupBy(p => p.ResponsibleDepartmentId.Value)
                .Select(g => new DepartmentMaintenanceWorkload
                {
                    DepartmentId = g.Key,
                    TotalPlans = g.Count(),
                    CompletedTasks = records.Count(r => r.MaintenancePlanId.HasValue && g.Any(p => p.Id == r.MaintenancePlanId.Value)),
                    PendingTasks = g.Count(p => p.NextMaintenanceDate.HasValue && p.NextMaintenanceDate.Value <= endDate),
                    OverdueTasks = g.Count(p => p.NextMaintenanceDate.HasValue && p.NextMaintenanceDate.Value < DateTime.Now.Date),
                    AverageCompletionTime = (int)(records.Where(r => r.MaintenancePlanId.HasValue &&
                                                              g.Any(p => p.Id == r.MaintenancePlanId.Value) &&
                                                              r.ActualDuration.HasValue)
                                                   .Any() ?
                                                   records.Where(r => r.MaintenancePlanId.HasValue &&
                                                              g.Any(p => p.Id == r.MaintenancePlanId.Value) &&
                                                              r.ActualDuration.HasValue)
                                                   .Average(r => r.ActualDuration.Value) : 0)
                }).ToList();
        }

        #endregion

        #region 模板和批量操作

        /// <summary>
        /// 从模板创建保养计划
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage, int SuccessCount, int FailCount)> CreatePlansFromTemplateAsync(MaintenancePlan template, List<int> componentIds)
        {
            var successCount = 0;
            var failCount = 0;
            var errors = new List<string>();

            try
            {
                await _context.Db.Ado.BeginTranAsync();

                foreach (var componentId in componentIds)
                {
                    try
                    {
                        var newPlan = new MaintenancePlan
                        {
                            Code = await GenerateNewPlanCodeAsync(),
                            Name = $"{template.Name} - 部件{componentId}",
                            ComponentId = componentId,
                            MaintenanceType = template.MaintenanceType,
                            CycleType = template.CycleType,
                            CycleValue = template.CycleValue,
                            ReminderDays = template.ReminderDays,
                            EstimatedDuration = template.EstimatedDuration,
                            MaintenanceContent = template.MaintenanceContent,
                            MaintenanceStandard = template.MaintenanceStandard,
                            RequiredTools = template.RequiredTools,
                            RequiredMaterials = template.RequiredMaterials,
                            ResponsibleDepartmentId = template.ResponsibleDepartmentId,
                            ResponsiblePersonId = template.ResponsiblePersonId,
                            Priority = template.Priority,
                            Status = 1,
                            IsEnabled = true
                        };

                        var result = await CreatePlanAsync(newPlan);
                        if (result.IsSuccess)
                        {
                            successCount++;
                        }
                        else
                        {
                            failCount++;
                            errors.Add($"部件 {componentId}: {result.ErrorMessage}");
                        }
                    }
                    catch (Exception ex)
                    {
                        failCount++;
                        errors.Add($"部件 {componentId}: {ex.Message}");
                    }
                }

                await _context.Db.Ado.CommitTranAsync();

                var errorMessage = errors.Any() ? string.Join("; ", errors) : string.Empty;
                return (true, errorMessage, successCount, failCount);
            }
            catch (Exception ex)
            {
                await _context.Db.Ado.RollbackTranAsync();
                return (false, $"批量创建失败：{ex.Message}", successCount, failCount);
            }
        }

        /// <summary>
        /// 从模板创建保养计划
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage, int? Id)> CreatePlanFromTemplateAsync(int templateId, int componentId, Dictionary<string, object>? customizations = null)
        {
            try
            {
                var template = await GetPlanByIdAsync(templateId);
                if (template == null)
                {
                    return (false, "模板计划不存在", null);
                }

                var newPlan = new MaintenancePlan
                {
                    Code = await GenerateNewPlanCodeAsync(),
                    Name = customizations?.ContainsKey("Name") == true ? customizations["Name"].ToString() : $"{template.Name} - 部件{componentId}",
                    ComponentId = componentId,
                    MaintenanceType = template.MaintenanceType,
                    CycleType = template.CycleType,
                    CycleValue = template.CycleValue,
                    ReminderDays = template.ReminderDays,
                    EstimatedDuration = template.EstimatedDuration,
                    MaintenanceContent = template.MaintenanceContent,
                    MaintenanceStandard = template.MaintenanceStandard,
                    RequiredTools = template.RequiredTools,
                    RequiredMaterials = template.RequiredMaterials,
                    ResponsibleDepartmentId = template.ResponsibleDepartmentId,
                    ResponsiblePersonId = template.ResponsiblePersonId,
                    Priority = template.Priority,
                    Status = 1,
                    IsEnabled = true
                };

                // 应用自定义参数
                if (customizations != null)
                {
                    foreach (var custom in customizations)
                    {
                        switch (custom.Key)
                        {
                            case "CycleValue":
                                if (int.TryParse(custom.Value.ToString(), out int cycleValue))
                                    newPlan.CycleValue = cycleValue;
                                break;
                            case "ReminderDays":
                                if (int.TryParse(custom.Value.ToString(), out int reminderDays))
                                    newPlan.ReminderDays = reminderDays;
                                break;
                            case "Priority":
                                if (int.TryParse(custom.Value.ToString(), out int priority))
                                    newPlan.Priority = priority;
                                break;
                        }
                    }
                }

                return await CreatePlanAsync(newPlan);
            }
            catch (Exception ex)
            {
                return (false, $"从模板创建计划失败：{ex.Message}", null);
            }
        }

        /// <summary>
        /// 批量创建保养计划
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage, int SuccessCount, int FailCount)> BatchCreatePlansAsync(List<MaintenancePlan> plans)
        {
            var successCount = 0;
            var failCount = 0;
            var errors = new List<string>();

            try
            {
                await _context.Db.Ado.BeginTranAsync();

                foreach (var plan in plans)
                {
                    try
                    {
                        if (string.IsNullOrEmpty(plan.Code))
                        {
                            plan.Code = await GenerateNewPlanCodeAsync();
                        }

                        var result = await CreatePlanAsync(plan);
                        if (result.IsSuccess)
                        {
                            successCount++;
                        }
                        else
                        {
                            failCount++;
                            errors.Add($"计划 {plan.Code}: {result.ErrorMessage}");
                        }
                    }
                    catch (Exception ex)
                    {
                        failCount++;
                        errors.Add($"计划 {plan.Code}: {ex.Message}");
                    }
                }

                await _context.Db.Ado.CommitTranAsync();

                var errorMessage = errors.Any() ? string.Join("; ", errors) : string.Empty;
                return (true, errorMessage, successCount, failCount);
            }
            catch (Exception ex)
            {
                await _context.Db.Ado.RollbackTranAsync();
                return (false, $"批量创建失败：{ex.Message}", successCount, failCount);
            }
        }

        /// <summary>
        /// 复制保养计划
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage, int? Id)> CopyPlanAsync(int sourcePlanId, int targetComponentId, string newCode, string newName)
        {
            try
            {
                var sourcePlan = await GetPlanByIdAsync(sourcePlanId);
                if (sourcePlan == null)
                {
                    return (false, "源保养计划不存在", null);
                }

                var newPlan = new MaintenancePlan
                {
                    Code = newCode,
                    Name = newName,
                    ComponentId = targetComponentId,
                    MaintenanceType = sourcePlan.MaintenanceType,
                    CycleType = sourcePlan.CycleType,
                    CycleValue = sourcePlan.CycleValue,
                    ReminderDays = sourcePlan.ReminderDays,
                    EstimatedDuration = sourcePlan.EstimatedDuration,
                    MaintenanceContent = sourcePlan.MaintenanceContent,
                    MaintenanceStandard = sourcePlan.MaintenanceStandard,
                    RequiredTools = sourcePlan.RequiredTools,
                    RequiredMaterials = sourcePlan.RequiredMaterials,
                    ResponsibleDepartmentId = sourcePlan.ResponsibleDepartmentId,
                    ResponsiblePersonId = sourcePlan.ResponsiblePersonId,
                    Priority = sourcePlan.Priority,
                    Status = 1,
                    IsEnabled = true
                };

                return await CreatePlanAsync(newPlan);
            }
            catch (Exception ex)
            {
                return (false, $"复制计划失败：{ex.Message}", null);
            }
        }

        /// <summary>
        /// 生成新的保养计划编码
        /// </summary>
        private async Task<string> GenerateNewPlanCodeAsync()
        {
            var today = DateTime.Now;
            var prefix = $"MP{today:yyyyMM}";

            var lastPlan = await _context.MaintenancePlans
                .Where(p => p.Code.StartsWith(prefix))
                .OrderByDescending(p => p.Code)
                .FirstAsync();

            if (lastPlan != null && lastPlan.Code.Length > prefix.Length)
            {
                var numberPart = lastPlan.Code.Substring(prefix.Length);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    return $"{prefix}{(lastNumber + 1):D4}";
                }
            }

            return $"{prefix}0001";
        }

        #endregion
    }
}
