using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using CoreHub.Shared.Models.Common;
using SqlSugar;
using System.Linq;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 设备部件管理服务实现
    /// </summary>
    public class EquipmentComponentService : IEquipmentComponentService
    {
        private readonly DatabaseContext _context;

        public EquipmentComponentService(DatabaseContext context)
        {
            _context = context;
        }

        #region 基础CRUD操作

        /// <summary>
        /// 获取所有设备部件
        /// </summary>
        public async Task<List<EquipmentComponent>> GetAllComponentsAsync()
        {
            return await _context.EquipmentComponents
                .Where(c => c.IsEnabled)
                .OrderBy(c => c.Category)
                .OrderBy(c => c.Code)
                .ToListAsync();
        }

        /// <summary>
        /// 根据ID获取设备部件
        /// </summary>
        public async Task<EquipmentComponent?> GetComponentByIdAsync(int id)
        {
            return await _context.EquipmentComponents
                .Where(c => c.Id == id)
                .FirstAsync();
        }

        /// <summary>
        /// 根据编码获取设备部件
        /// </summary>
        public async Task<EquipmentComponent?> GetComponentByCodeAsync(string code)
        {
            return await _context.EquipmentComponents
                .Where(c => c.Code == code)
                .FirstAsync();
        }

        /// <summary>
        /// 创建设备部件
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage, int? Id)> CreateComponentAsync(EquipmentComponent component)
        {
            try
            {
                // 检查编码是否已存在
                var existingComponent = await _context.EquipmentComponents
                    .Where(c => c.Code == component.Code)
                    .FirstAsync();

                if (existingComponent != null)
                {
                    return (false, "部件编码已存在", null);
                }

                component.CreatedAt = DateTime.Now;
                var result = await _context.Db.Insertable(component).ExecuteReturnIdentityAsync();
                return (true, string.Empty, result);
            }
            catch (Exception ex)
            {
                return (false, $"创建部件失败：{ex.Message}", null);
            }
        }

        /// <summary>
        /// 更新设备部件
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateComponentAsync(EquipmentComponent component)
        {
            try
            {
                // 检查编码是否与其他部件冲突
                var existingComponent = await _context.EquipmentComponents
                    .Where(c => c.Code == component.Code && c.Id != component.Id)
                    .FirstAsync();

                if (existingComponent != null)
                {
                    return (false, "部件编码已存在");
                }

                component.UpdatedAt = DateTime.Now;
                await _context.Db.Updateable(component).ExecuteCommandAsync();
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, $"更新部件失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 删除设备部件
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> DeleteComponentAsync(int id)
        {
            try
            {
                // 检查是否有关联的设备映射
                var mappingCount = await _context.EquipmentComponentMappings
                    .Where(m => m.ComponentId == id && m.IsEnabled)
                    .CountAsync();

                if (mappingCount > 0)
                {
                    return (false, "该部件已关联设备，无法删除");
                }

                // 检查是否有保养计划
                var planCount = await _context.MaintenancePlans
                    .Where(p => p.ComponentId == id && p.IsEnabled)
                    .CountAsync();

                if (planCount > 0)
                {
                    return (false, "该部件已有保养计划，无法删除");
                }

                await _context.Db.Deleteable<EquipmentComponent>().Where(c => c.Id == id).ExecuteCommandAsync();
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, $"删除部件失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 切换部件启用状态
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> ToggleStatusAsync(int id)
        {
            try
            {
                var component = await GetComponentByIdAsync(id);
                if (component == null)
                {
                    return (false, "部件不存在");
                }

                component.IsEnabled = !component.IsEnabled;
                component.UpdatedAt = DateTime.Now;

                await _context.Db.Updateable(component).ExecuteCommandAsync();
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, $"切换状态失败：{ex.Message}");
            }
        }

        #endregion

        #region 搜索和筛选

        /// <summary>
        /// 搜索设备部件（分页版本）
        /// </summary>
        public async Task<PagedResult<EquipmentComponent>> SearchComponentsAsync(EquipmentComponentFilter filter)
        {
            try
            {
                var query = _context.EquipmentComponents.Where(c => c.IsEnabled);

                // 应用搜索条件
                if (!string.IsNullOrEmpty(filter.Keyword))
                {
                    query = query.Where(c => c.Name.Contains(filter.Keyword) ||
                                           c.Code.Contains(filter.Keyword) ||
                                           c.Model.Contains(filter.Keyword) ||
                                           c.Specifications.Contains(filter.Keyword));
                }

                if (!string.IsNullOrEmpty(filter.Category))
                {
                    query = query.Where(c => c.Category == filter.Category);
                }

                if (filter.Status.HasValue)
                {
                    query = query.Where(c => c.Status == filter.Status.Value);
                }

                if (filter.ComponentType.HasValue)
                {
                    query = query.Where(c => c.ComponentType == filter.ComponentType.Value);
                }

                if (filter.IsLowStock.HasValue && filter.IsLowStock.Value)
                {
                    query = query.Where(c => c.StockQuantity <= c.MinStockLevel);
                }

                if (filter.IsCritical.HasValue)
                {
                    query = query.Where(c => c.IsCritical == filter.IsCritical.Value);
                }

                if (filter.IsEnabled.HasValue)
                {
                    query = query.Where(c => c.IsEnabled == filter.IsEnabled.Value);
                }

                // 获取总数
                var totalCount = await query.CountAsync();

                // 应用分页
                var items = await query
                    .OrderBy(c => c.Code)
                    .Skip((filter.PageIndex - 1) * filter.PageSize)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return PagedResult<EquipmentComponent>.Create(items, totalCount, filter.PageIndex, filter.PageSize);
            }
            catch (Exception ex)
            {
                throw new Exception($"搜索设备部件失败：{ex.Message}", ex);
            }
        }

        /// <summary>
        /// 搜索设备部件（简单版本）
        /// </summary>
        public async Task<List<EquipmentComponent>> SearchComponentsAsync(string? keyword = null, string? category = null, int? status = null, bool? isLowStock = null)
        {
            var query = _context.EquipmentComponents.Where(c => c.IsEnabled);

            if (!string.IsNullOrEmpty(keyword))
            {
                query = query.Where(c => c.Name.Contains(keyword) || 
                                       c.Code.Contains(keyword) || 
                                       c.Model.Contains(keyword) ||
                                       c.Specifications.Contains(keyword));
            }

            if (!string.IsNullOrEmpty(category))
            {
                query = query.Where(c => c.Category == category);
            }

            if (status.HasValue)
            {
                query = query.Where(c => c.Status == status.Value);
            }

            if (isLowStock.HasValue && isLowStock.Value)
            {
                query = query.Where(c => c.StockQuantity <= c.MinStockLevel);
            }

            return await query.OrderBy(c => c.Category).OrderBy(c => c.Code).ToListAsync();
        }

        /// <summary>
        /// 获取部件分类列表
        /// </summary>
        public async Task<List<string>> GetCategoriesAsync()
        {
            return await _context.EquipmentComponents
                .Where(c => c.IsEnabled && !string.IsNullOrEmpty(c.Category))
                .Select(c => c.Category)
                .Distinct()
                .ToListAsync();
        }

        /// <summary>
        /// 获取供应商列表
        /// </summary>
        public async Task<List<string>> GetSuppliersAsync()
        {
            return await _context.EquipmentComponents
                .Where(c => c.IsEnabled && !string.IsNullOrEmpty(c.Supplier))
                .Select(c => c.Supplier)
                .Distinct()
                .ToListAsync();
        }

        #endregion

        #region 库存管理

        /// <summary>
        /// 更新库存数量
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateStockQuantityAsync(int componentId, int quantity, string reason)
        {
            try
            {
                var component = await GetComponentByIdAsync(componentId);
                if (component == null)
                {
                    return (false, "部件不存在");
                }

                var newQuantity = component.StockQuantity + quantity;
                if (newQuantity < 0)
                {
                    return (false, "库存数量不足");
                }

                component.StockQuantity = newQuantity;
                component.UpdatedAt = DateTime.Now;

                await _context.Db.Updateable(component).ExecuteCommandAsync();

                // TODO: 记录库存变更日志
                // await LogStockChangeAsync(componentId, quantity, reason);

                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, $"更新库存失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取库存不足的部件
        /// </summary>
        public async Task<List<EquipmentComponent>> GetLowStockComponentsAsync()
        {
            return await _context.EquipmentComponents
                .Where(c => c.IsEnabled && c.StockQuantity <= c.MinStockLevel)
                .OrderBy(c => c.Category)
                .OrderBy(c => c.Code)
                .ToListAsync();
        }

        /// <summary>
        /// 获取缺货的部件
        /// </summary>
        public async Task<List<EquipmentComponent>> GetOutOfStockComponentsAsync()
        {
            return await _context.EquipmentComponents
                .Where(c => c.IsEnabled && c.StockQuantity <= 0)
                .OrderBy(c => c.Category)
                .OrderBy(c => c.Code)
                .ToListAsync();
        }

        /// <summary>
        /// 批量更新库存
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> BatchUpdateStockAsync(List<(int ComponentId, int Quantity, string Reason)> stockUpdates)
        {
            try
            {
                await _context.Db.Ado.BeginTranAsync();

                foreach (var update in stockUpdates)
                {
                    var result = await UpdateStockQuantityAsync(update.ComponentId, update.Quantity, update.Reason);
                    if (!result.IsSuccess)
                    {
                        await _context.Db.Ado.RollbackTranAsync();
                        return (false, $"批量更新失败：{result.ErrorMessage}");
                    }
                }

                await _context.Db.Ado.CommitTranAsync();
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                await _context.Db.Ado.RollbackTranAsync();
                return (false, $"批量更新库存失败：{ex.Message}");
            }
        }

        #endregion

        #region 设备关联管理

        /// <summary>
        /// 获取设备的所有部件
        /// </summary>
        public async Task<List<EquipmentComponentMapping>> GetEquipmentComponentsAsync(int equipmentId)
        {
            return await _context.EquipmentComponentMappings
                .Where(m => m.EquipmentId == equipmentId && m.IsEnabled)
                .OrderBy(m => m.Position)
                .OrderBy(m => m.ComponentId)
                .ToListAsync();
        }

        /// <summary>
        /// 获取部件关联的所有设备
        /// </summary>
        public async Task<List<EquipmentComponentMapping>> GetComponentEquipmentsAsync(int componentId)
        {
            return await _context.EquipmentComponentMappings
                .Where(m => m.ComponentId == componentId && m.IsEnabled)
                .OrderBy(m => m.EquipmentId)
                .ToListAsync();
        }

        /// <summary>
        /// 添加设备部件关联
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage, int? Id)> AddEquipmentComponentMappingAsync(EquipmentComponentMapping mapping)
        {
            try
            {
                // 检查是否已存在相同的关联
                var existingMapping = await _context.EquipmentComponentMappings
                    .Where(m => m.EquipmentId == mapping.EquipmentId &&
                               m.ComponentId == mapping.ComponentId &&
                               m.IsEnabled)
                    .FirstAsync();

                if (existingMapping != null)
                {
                    return (false, "该设备已关联此部件", null);
                }

                mapping.CreatedAt = DateTime.Now;
                var result = await _context.Db.Insertable(mapping).ExecuteReturnIdentityAsync();
                return (true, string.Empty, result);
            }
            catch (Exception ex)
            {
                return (false, $"添加关联失败：{ex.Message}", null);
            }
        }

        /// <summary>
        /// 更新设备部件关联
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateEquipmentComponentMappingAsync(EquipmentComponentMapping mapping)
        {
            try
            {
                mapping.UpdatedAt = DateTime.Now;
                await _context.Db.Updateable(mapping).ExecuteCommandAsync();
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, $"更新关联失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 删除设备部件关联
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> DeleteEquipmentComponentMappingAsync(int id)
        {
            try
            {
                await _context.Db.Deleteable<EquipmentComponentMapping>().Where(m => m.Id == id).ExecuteCommandAsync();
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, $"删除关联失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取需要更换的部件
        /// </summary>
        public async Task<List<EquipmentComponentMapping>> GetComponentsNeedingReplacementAsync(int? equipmentId = null)
        {
            var query = _context.EquipmentComponentMappings
                .Where(m => m.IsEnabled && (m.Status >= 3 ||
                           (m.ExpectedReplacementDate.HasValue && m.ExpectedReplacementDate.Value <= DateTime.Now)));

            if (equipmentId.HasValue)
            {
                query = query.Where(m => m.EquipmentId == equipmentId.Value);
            }

            return await query.OrderBy(m => m.ExpectedReplacementDate).ToListAsync();
        }

        #endregion

        #region 统计分析

        /// <summary>
        /// 获取部件统计信息
        /// </summary>
        public async Task<ComponentStatistics> GetComponentStatisticsAsync()
        {
            var components = await _context.EquipmentComponents.ToListAsync();

            var statistics = new ComponentStatistics
            {
                TotalComponents = components.Count,
                ActiveComponents = components.Count(c => c.IsEnabled && c.Status == 1),
                InactiveComponents = components.Count(c => !c.IsEnabled || c.Status != 1),
                LowStockComponents = components.Count(c => c.IsEnabled && c.StockQuantity <= c.MinStockLevel),
                OutOfStockComponents = components.Count(c => c.IsEnabled && c.StockQuantity <= 0),
                CriticalComponents = components.Count(c => c.IsEnabled && c.IsCritical),
                TotalStockValue = components.Where(c => c.IsEnabled && c.UnitPrice.HasValue)
                                           .Sum(c => c.StockQuantity * c.UnitPrice.Value),
                CategoryStatistics = components.Where(c => c.IsEnabled && !string.IsNullOrEmpty(c.Category))
                                              .GroupBy(c => c.Category)
                                              .ToDictionary(g => g.Key, g => g.Count())
            };

            return statistics;
        }

        /// <summary>
        /// 获取部件价值统计
        /// </summary>
        public async Task<decimal> GetComponentsTotalValueAsync(string? category = null)
        {
            var query = _context.EquipmentComponents
                .Where(c => c.IsEnabled && c.UnitPrice.HasValue);

            if (!string.IsNullOrEmpty(category))
            {
                query = query.Where(c => c.Category == category);
            }

            var components = await query.ToListAsync();
            return components.Sum(c => c.StockQuantity * c.UnitPrice.Value);
        }

        /// <summary>
        /// 获取单个部件的使用统计信息
        /// </summary>
        public async Task<Models.ComponentUsageStatistics> GetComponentUsageStatisticsAsync(int componentId)
        {
            var component = await GetComponentByIdAsync(componentId);
            if (component == null)
            {
                throw new ArgumentException("部件不存在", nameof(componentId));
            }

            var startDate = DateTime.Now.AddMonths(-12); // 统计最近12个月的数据
            var endDate = DateTime.Now;

            // 获取保养记录
            var maintenanceRecords = await _context.MaintenanceRecords
                .Where(r => r.ComponentId == componentId && r.MaintenanceDate >= startDate)
                .ToListAsync();

            // 计算统计数据
            var totalCount = maintenanceRecords.Count;
            var preventiveCount = maintenanceRecords.Count(r => r.MaintenanceType == 1);
            var correctiveCount = maintenanceRecords.Count(r => r.MaintenanceType == 2);
            var emergencyCount = maintenanceRecords.Count(r => r.MaintenanceType == 4);

            var totalCost = maintenanceRecords.Sum(r => r.MaintenanceCost ?? 0);
            var avgCost = totalCount > 0 ? totalCost / totalCount : 0;

            var totalDuration = maintenanceRecords.Sum(r => r.ActualDuration ?? 0);
            var avgDuration = totalCount > 0 ? (double)totalDuration / totalCount : 0;

            var successCount = maintenanceRecords.Count(r => r.MaintenanceResult == 1);
            var successRate = totalCount > 0 ? (double)successCount / totalCount * 100 : 0;

            var onTimeCount = maintenanceRecords.Count(r => r.ActualDuration <= r.PlannedDuration);
            var onTimeRate = totalCount > 0 ? (double)onTimeCount / totalCount * 100 : 0;

            var firstMaintenance = maintenanceRecords.OrderBy(r => r.MaintenanceDate).FirstOrDefault();
            var lastMaintenance = maintenanceRecords.OrderByDescending(r => r.MaintenanceDate).FirstOrDefault();

            // 计算库存状态
            var stockStatus = component.StockQuantity <= 0 ? 3 :
                             component.StockQuantity <= component.MinStockLevel ? 2 : 1;

            var stockValue = component.StockQuantity * (component.UnitPrice ?? 0);

            // 计算保养频率（次/月）
            var monthsDiff = Math.Max(1, (endDate - startDate).Days / 30.0);
            var frequency = totalCount / monthsDiff;

            // 计算故障率
            var failureCount = maintenanceRecords.Count(r => r.MaintenanceResult == 3);
            var failureRate = totalCount > 0 ? (double)failureCount / totalCount * 100 : 0;

            // 计算可靠性评分
            var reliabilityScore = Math.Max(0, 100 - failureRate - (100 - successRate) * 0.5);

            return new Models.ComponentUsageStatistics
            {
                ComponentId = component.Id,
                ComponentName = component.Name,
                ComponentCode = component.Code,
                TotalMaintenanceCount = totalCount,
                PreventiveMaintenanceCount = preventiveCount,
                CorrectiveMaintenanceCount = correctiveCount,
                EmergencyMaintenanceCount = emergencyCount,
                TotalMaintenanceCost = totalCost,
                AverageMaintenanceCost = avgCost,
                TotalMaintenanceDuration = totalDuration,
                AverageMaintenanceDuration = avgDuration,
                SuccessRate = successRate,
                OnTimeCompletionRate = onTimeRate,
                FirstMaintenanceDate = firstMaintenance?.MaintenanceDate,
                LastMaintenanceDate = lastMaintenance?.MaintenanceDate,
                CurrentStockQuantity = component.StockQuantity,
                MinStockLevel = component.MinStockLevel,
                StockStatus = stockStatus,
                StockValue = stockValue,
                IsCritical = component.IsCritical,
                ComponentStatus = component.Status,
                StatisticsPeriodStart = startDate,
                StatisticsPeriodEnd = endDate,
                MaintenanceFrequency = frequency,
                FailureRate = failureRate,
                ReliabilityScore = reliabilityScore
            };
        }

        /// <summary>
        /// 获取部件使用频率统计
        /// </summary>
        public async Task<List<ComponentUsageStatistics>> GetComponentUsageFrequencyStatisticsAsync(int days = 30)
        {
            var startDate = DateTime.Now.AddDays(-days);

            // 获取部件基本信息
            var components = await _context.EquipmentComponents
                .Where(c => c.IsEnabled)
                .ToListAsync();

            var statistics = new List<ComponentUsageStatistics>();

            foreach (var component in components)
            {
                // 获取关联设备数量
                var equipmentCount = await _context.EquipmentComponentMappings
                    .Where(m => m.ComponentId == component.Id && m.IsEnabled)
                    .CountAsync();

                // 获取更换次数（从保养记录中统计）
                var replacementCount = await _context.MaintenanceRecords
                    .Where(r => r.ComponentId == component.Id &&
                               r.MaintenanceDate >= startDate &&
                               !string.IsNullOrEmpty(r.ReplacedParts))
                    .CountAsync();

                // 获取使用次数（从保养记录中统计）
                var usageCount = await _context.MaintenanceRecords
                    .Where(r => r.ComponentId == component.Id && r.MaintenanceDate >= startDate)
                    .CountAsync();

                statistics.Add(new ComponentUsageStatistics
                {
                    ComponentId = component.Id,
                    ComponentName = component.Name,
                    ComponentCode = component.Code,
                    UsageCount = usageCount,
                    ReplacementCount = replacementCount,
                    EquipmentCount = equipmentCount
                });
            }

            return statistics.OrderByDescending(s => s.UsageCount).ToList();
        }

        #endregion

        #region 导入导出

        /// <summary>
        /// 批量导入部件
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage, int SuccessCount, int FailCount)> BatchImportComponentsAsync(List<EquipmentComponent> components)
        {
            int successCount = 0;
            int failCount = 0;
            var errors = new List<string>();

            try
            {
                await _context.Db.Ado.BeginTranAsync();

                foreach (var component in components)
                {
                    var result = await CreateComponentAsync(component);
                    if (result.IsSuccess)
                    {
                        successCount++;
                    }
                    else
                    {
                        failCount++;
                        errors.Add($"部件 {component.Code}: {result.ErrorMessage}");
                    }
                }

                await _context.Db.Ado.CommitTranAsync();

                var errorMessage = errors.Any() ? string.Join("; ", errors) : string.Empty;
                return (true, errorMessage, successCount, failCount);
            }
            catch (Exception ex)
            {
                await _context.Db.Ado.RollbackTranAsync();
                return (false, $"批量导入失败：{ex.Message}", successCount, failCount);
            }
        }

        /// <summary>
        /// 导出部件数据
        /// </summary>
        public async Task<List<EquipmentComponent>> ExportComponentsAsync(string? category = null, int? status = null)
        {
            var query = _context.EquipmentComponents.Where(c => c.IsEnabled);

            if (!string.IsNullOrEmpty(category))
            {
                query = query.Where(c => c.Category == category);
            }

            if (status.HasValue)
            {
                query = query.Where(c => c.Status == status.Value);
            }

            return await query.OrderBy(c => c.Category).OrderBy(c => c.Code).ToListAsync();
        }

        #endregion
    }
}
