using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace CoreHub.Shared.Models.Database
{
    /// <summary>
    /// 设备部件关联关系实体
    /// </summary>
    [SugarTable("EquipmentComponentMappings")]
    public class EquipmentComponentMapping
    {
        /// <summary>
        /// 关联关系ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 设备ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "设备不能为空")]
        public int EquipmentId { get; set; }

        /// <summary>
        /// 部件ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "部件不能为空")]
        public int ComponentId { get; set; }

        /// <summary>
        /// 部件在设备中的数量
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int Quantity { get; set; } = 1;

        /// <summary>
        /// 部件在设备中的位置描述
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = true)]
        [StringLength(200, ErrorMessage = "位置描述长度不能超过200个字符")]
        public string? Position { get; set; }

        /// <summary>
        /// 安装日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? InstallationDate { get; set; }

        /// <summary>
        /// 预计更换日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? ExpectedReplacementDate { get; set; }

        /// <summary>
        /// 最后更换日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? LastReplacementDate { get; set; }

        /// <summary>
        /// 累计更换次数
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int ReplacementCount { get; set; } = 0;

        /// <summary>
        /// 部件状态（1=正常,2=需要关注,3=需要更换,4=已损坏）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int Status { get; set; } = 1;

        /// <summary>
        /// 是否为关键部件
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsCritical { get; set; } = false;

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
        public string? Remark { get; set; }

        // 导航属性
        /// <summary>
        /// 关联的设备
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Equipment? Equipment { get; set; }

        /// <summary>
        /// 关联的部件
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public EquipmentComponent? Component { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public User? Creator { get; set; }

        /// <summary>
        /// 更新人
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public User? Updater { get; set; }

        /// <summary>
        /// 部件状态名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string StatusName => Status switch
        {
            1 => "正常",
            2 => "需要关注",
            3 => "需要更换",
            4 => "已损坏",
            _ => "未知"
        };

        /// <summary>
        /// 是否需要更换（基于预计更换日期）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool NeedsReplacement
        {
            get
            {
                if (!ExpectedReplacementDate.HasValue)
                    return Status >= 3;
                
                return DateTime.Now >= ExpectedReplacementDate.Value || Status >= 3;
            }
        }

        /// <summary>
        /// 使用天数（从安装日期或最后更换日期开始计算）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int? UsageDays
        {
            get
            {
                var startDate = LastReplacementDate ?? InstallationDate;
                if (!startDate.HasValue)
                    return null;
                
                return (int)(DateTime.Now - startDate.Value).TotalDays;
            }
        }

        /// <summary>
        /// 距离预计更换日期的天数
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int? DaysUntilReplacement
        {
            get
            {
                if (!ExpectedReplacementDate.HasValue)
                    return null;
                
                return (int)(ExpectedReplacementDate.Value - DateTime.Now).TotalDays;
            }
        }

        /// <summary>
        /// 是否已过期（超过预计更换日期）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool IsOverdue
        {
            get
            {
                if (!ExpectedReplacementDate.HasValue)
                    return false;
                
                return DateTime.Now > ExpectedReplacementDate.Value;
            }
        }
    }
}
