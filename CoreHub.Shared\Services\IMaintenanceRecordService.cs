using CoreHub.Shared.Models.Database;
using CoreHub.Shared.Models.Common;
using CoreHub.Shared.Models;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 保养记录管理服务接口
    /// </summary>
    public interface IMaintenanceRecordService
    {
        #region 基础CRUD操作

        /// <summary>
        /// 获取所有保养记录
        /// </summary>
        /// <returns>保养记录列表</returns>
        Task<List<MaintenanceRecord>> GetAllRecordsAsync();

        /// <summary>
        /// 根据ID获取保养记录
        /// </summary>
        /// <param name="id">记录ID</param>
        /// <returns>保养记录</returns>
        Task<MaintenanceRecord?> GetRecordByIdAsync(int id);

        /// <summary>
        /// 根据记录编号获取保养记录
        /// </summary>
        /// <param name="recordNumber">记录编号</param>
        /// <returns>保养记录</returns>
        Task<MaintenanceRecord?> GetRecordByNumberAsync(string recordNumber);

        /// <summary>
        /// 创建保养记录
        /// </summary>
        /// <param name="record">保养记录</param>
        /// <returns>创建结果</returns>
        Task<(bool IsSuccess, string ErrorMessage, int? Id)> CreateRecordAsync(MaintenanceRecord record);

        /// <summary>
        /// 创建保养记录
        /// </summary>
        /// <param name="record">保养记录</param>
        /// <returns>创建结果</returns>
        Task<(bool IsSuccess, string ErrorMessage, int? Id)> CreateAsync(MaintenanceRecord record);

        /// <summary>
        /// 更新保养记录
        /// </summary>
        /// <param name="record">保养记录</param>
        /// <returns>更新结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateRecordAsync(MaintenanceRecord record);

        /// <summary>
        /// 删除保养记录
        /// </summary>
        /// <param name="id">记录ID</param>
        /// <returns>删除结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> DeleteRecordAsync(int id);

        #endregion

        #region 搜索和筛选

        /// <summary>
        /// 搜索保养记录（分页版本）
        /// </summary>
        /// <param name="filter">搜索过滤条件</param>
        /// <returns>分页搜索结果</returns>
        Task<PagedResult<MaintenanceRecord>> SearchRecordsAsync(MaintenanceRecordFilter filter);

        /// <summary>
        /// 搜索保养记录（简单版本）
        /// </summary>
        /// <param name="keyword">关键词</param>
        /// <param name="componentId">部件ID</param>
        /// <param name="maintenanceType">保养类型</param>
        /// <param name="maintenancePersonId">保养人员ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="reviewStatus">审核状态</param>
        /// <returns>搜索结果</returns>
        Task<List<MaintenanceRecord>> SearchRecordsAsync(string? keyword = null, int? componentId = null, int? maintenanceType = null, int? maintenancePersonId = null, DateTime? startDate = null, DateTime? endDate = null, int? reviewStatus = null);

        /// <summary>
        /// 获取部件的保养记录
        /// </summary>
        /// <param name="componentId">部件ID</param>
        /// <param name="limit">记录数量限制</param>
        /// <returns>保养记录列表</returns>
        Task<List<MaintenanceRecord>> GetRecordsByComponentIdAsync(int componentId, int? limit = null);

        /// <summary>
        /// 获取设备的所有保养记录
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <param name="limit">记录数量限制</param>
        /// <returns>保养记录列表</returns>
        Task<List<MaintenanceRecord>> GetRecordsByEquipmentIdAsync(int equipmentId, int? limit = null);

        /// <summary>
        /// 获取保养计划的执行记录
        /// </summary>
        /// <param name="planId">保养计划ID</param>
        /// <returns>保养记录列表</returns>
        Task<List<MaintenanceRecord>> GetRecordsByPlanIdAsync(int planId);

        /// <summary>
        /// 获取人员的保养记录
        /// </summary>
        /// <param name="personId">人员ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>保养记录列表</returns>
        Task<List<MaintenanceRecord>> GetRecordsByPersonAsync(int personId, DateTime? startDate = null, DateTime? endDate = null);

        #endregion

        #region 记录编号生成

        /// <summary>
        /// 生成保养记录编号
        /// </summary>
        /// <param name="maintenanceType">保养类型</param>
        /// <param name="maintenanceDate">保养日期</param>
        /// <returns>记录编号</returns>
        Task<string> GenerateRecordNumberAsync(int maintenanceType, DateTime maintenanceDate);

        #endregion

        #region 审核管理

        /// <summary>
        /// 获取待审核的保养记录
        /// </summary>
        /// <returns>待审核记录列表</returns>
        Task<List<MaintenanceRecord>> GetPendingReviewRecordsAsync();

        /// <summary>
        /// 审核保养记录
        /// </summary>
        /// <param name="recordId">记录ID</param>
        /// <param name="reviewerId">审核人ID</param>
        /// <param name="reviewStatus">审核状态</param>
        /// <param name="reviewComments">审核意见</param>
        /// <returns>审核结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> ReviewRecordAsync(int recordId, int reviewerId, int reviewStatus, string? reviewComments = null);

        /// <summary>
        /// 批量审核保养记录
        /// </summary>
        /// <param name="reviews">审核列表</param>
        /// <returns>审核结果</returns>
        Task<(bool IsSuccess, string ErrorMessage, int SuccessCount, int FailCount)> BatchReviewRecordsAsync(List<(int RecordId, int ReviewerId, int ReviewStatus, string? ReviewComments)> reviews);

        #endregion

        #region 保养计划关联

        /// <summary>
        /// 从保养计划创建保养记录
        /// </summary>
        /// <param name="planId">保养计划ID</param>
        /// <param name="maintenancePersonId">保养人员ID</param>
        /// <param name="scheduledDate">计划保养日期</param>
        /// <returns>创建结果</returns>
        Task<(bool IsSuccess, string ErrorMessage, int? Id)> CreateRecordFromPlanAsync(int planId, int maintenancePersonId, DateTime scheduledDate);

        /// <summary>
        /// 完成保养记录并更新关联的保养计划
        /// </summary>
        /// <param name="recordId">记录ID</param>
        /// <param name="completionData">完成数据</param>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> CompleteRecordAndUpdatePlanAsync(int recordId, MaintenanceCompletionData completionData);

        #endregion

        #region 统计分析

        /// <summary>
        /// 获取保养记录统计信息
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>统计信息</returns>
        Task<MaintenanceRecordStatistics> GetRecordStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// 获取保养成本统计
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="componentId">部件ID（可选）</param>
        /// <returns>成本统计</returns>
        Task<MaintenanceCostStatistics> GetCostStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null, int? componentId = null);

        /// <summary>
        /// 获取保养效率统计
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="personId">人员ID（可选）</param>
        /// <returns>效率统计</returns>
        Task<MaintenanceEfficiencyStatistics> GetEfficiencyStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null, int? personId = null);

        /// <summary>
        /// 获取部件保养历史分析
        /// </summary>
        /// <param name="componentId">部件ID</param>
        /// <param name="months">分析月数</param>
        /// <returns>历史分析</returns>
        Task<ComponentMaintenanceHistory> GetComponentMaintenanceHistoryAsync(int componentId, int months = 12);

        /// <summary>
        /// 获取保养趋势分析
        /// </summary>
        /// <param name="months">分析月数</param>
        /// <param name="maintenanceType">保养类型（可选）</param>
        /// <returns>趋势分析</returns>
        Task<List<MaintenanceTrendData>> GetMaintenanceTrendAnalysisAsync(int months = 12, int? maintenanceType = null);

        #endregion

        #region 报告生成

        /// <summary>
        /// 生成保养记录报告
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="reportType">报告类型</param>
        /// <param name="filters">筛选条件</param>
        /// <returns>报告数据</returns>
        Task<MaintenanceReport> GenerateMaintenanceReportAsync(DateTime startDate, DateTime endDate, MaintenanceReportType reportType, Dictionary<string, object>? filters = null);

        /// <summary>
        /// 导出保养记录
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="componentId">部件ID（可选）</param>
        /// <param name="maintenanceType">保养类型（可选）</param>
        /// <returns>保养记录数据</returns>
        Task<List<MaintenanceRecord>> ExportRecordsAsync(DateTime? startDate = null, DateTime? endDate = null, int? componentId = null, int? maintenanceType = null);

        #endregion
    }



    /// <summary>
    /// 保养记录统计信息
    /// </summary>
    public class MaintenanceRecordStatistics
    {
        /// <summary>
        /// 总记录数量
        /// </summary>
        public int TotalRecords { get; set; }

        /// <summary>
        /// 已完成记录数量
        /// </summary>
        public int CompletedRecords { get; set; }

        /// <summary>
        /// 待审核记录数量
        /// </summary>
        public int PendingReviewRecords { get; set; }

        /// <summary>
        /// 平均保养时长（分钟）
        /// </summary>
        public int AverageMaintenanceDuration { get; set; }

        /// <summary>
        /// 总保养成本
        /// </summary>
        public decimal TotalMaintenanceCost { get; set; }

        /// <summary>
        /// 进行中记录数量
        /// </summary>
        public int InProgressRecords { get; set; }

        /// <summary>
        /// 逾期记录数量
        /// </summary>
        public int OverdueRecords { get; set; }

        /// <summary>
        /// 待处理记录数量（别名）
        /// </summary>
        public int PendingRecords { get => InProgressRecords; set => InProgressRecords = value; }

        /// <summary>
        /// 平均时长（别名）
        /// </summary>
        public int AverageDuration { get => AverageMaintenanceDuration; set => AverageMaintenanceDuration = value; }

        /// <summary>
        /// 总时长
        /// </summary>
        public int TotalDuration { get; set; }

        /// <summary>
        /// 最大时长
        /// </summary>
        public int MaxDuration { get; set; }

        /// <summary>
        /// 最小时长
        /// </summary>
        public int MinDuration { get; set; }

        /// <summary>
        /// 总成本（别名）
        /// </summary>
        public decimal TotalCost { get => TotalMaintenanceCost; set => TotalMaintenanceCost = value; }

        /// <summary>
        /// 平均成本
        /// </summary>
        public decimal AverageCost { get; set; }

        /// <summary>
        /// 最高成本
        /// </summary>
        public decimal MaxCost { get; set; }

        /// <summary>
        /// 最低成本
        /// </summary>
        public decimal MinCost { get; set; }

        /// <summary>
        /// 完成率
        /// </summary>
        public decimal CompletionRate { get; set; }

        /// <summary>
        /// 按时完成率
        /// </summary>
        public decimal OnTimeRate { get; set; }

        /// <summary>
        /// 成功率
        /// </summary>
        public decimal SuccessRate { get; set; }

        /// <summary>
        /// 保养类型统计
        /// </summary>
        public Dictionary<int, int> MaintenanceTypeStatistics { get; set; } = new Dictionary<int, int>();

        /// <summary>
        /// 保养结果统计
        /// </summary>
        public Dictionary<int, int> MaintenanceResultStatistics { get; set; } = new Dictionary<int, int>();
    }

    /// <summary>
    /// 保养成本统计
    /// </summary>
    public class MaintenanceCostStatistics
    {
        /// <summary>
        /// 总成本
        /// </summary>
        public decimal TotalCost { get; set; }

        /// <summary>
        /// 平均成本
        /// </summary>
        public decimal AverageCost { get; set; }

        /// <summary>
        /// 最高成本
        /// </summary>
        public decimal MaxCost { get; set; }

        /// <summary>
        /// 最低成本
        /// </summary>
        public decimal MinCost { get; set; }

        /// <summary>
        /// 预防性保养成本
        /// </summary>
        public decimal PreventiveCost { get; set; }

        /// <summary>
        /// 纠正性保养成本
        /// </summary>
        public decimal CorrectiveCost { get; set; }

        /// <summary>
        /// 预测性保养成本
        /// </summary>
        public decimal PredictiveCost { get; set; }

        /// <summary>
        /// 记录数量
        /// </summary>
        public int RecordCount { get; set; }

        /// <summary>
        /// 按保养类型的成本分布
        /// </summary>
        public Dictionary<int, decimal> CostByMaintenanceType { get; set; } = new Dictionary<int, decimal>();

        /// <summary>
        /// 按月份的成本趋势
        /// </summary>
        public Dictionary<string, decimal> MonthlyCostTrend { get; set; } = new Dictionary<string, decimal>();
    }

    /// <summary>
    /// 保养效率统计
    /// </summary>
    public class MaintenanceEfficiencyStatistics
    {
        /// <summary>
        /// 平均计划时长（分钟）
        /// </summary>
        public int AveragePlannedDuration { get; set; }

        /// <summary>
        /// 平均实际时长（分钟）
        /// </summary>
        public int AverageActualDuration { get; set; }

        /// <summary>
        /// 时长效率（实际/计划）
        /// </summary>
        public decimal DurationEfficiency { get; set; }

        /// <summary>
        /// 按时完成率
        /// </summary>
        public decimal OnTimeCompletionRate { get; set; }

        /// <summary>
        /// 一次性通过率
        /// </summary>
        public decimal FirstTimePassRate { get; set; }
    }





    /// <summary>
    /// 保养报告类型
    /// </summary>
    public enum MaintenanceReportType
    {
        /// <summary>
        /// 综合报告
        /// </summary>
        Comprehensive = 1,

        /// <summary>
        /// 成本分析报告
        /// </summary>
        CostAnalysis = 2,

        /// <summary>
        /// 效率分析报告
        /// </summary>
        EfficiencyAnalysis = 3,

        /// <summary>
        /// 部件保养报告
        /// </summary>
        ComponentMaintenance = 4,

        /// <summary>
        /// 人员工作量报告
        /// </summary>
        PersonWorkload = 5
    }

    /// <summary>
    /// 保养报告
    /// </summary>
    public class MaintenanceReport
    {
        /// <summary>
        /// 报告类型
        /// </summary>
        public MaintenanceReportType ReportType { get; set; }

        /// <summary>
        /// 报告标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 报告数据
        /// </summary>
        public object ReportData { get; set; } = new object();

        /// <summary>
        /// 生成时间
        /// </summary>
        public DateTime GeneratedAt { get; set; } = DateTime.Now;
    }
}
