namespace CoreHub.Shared.Models
{
    /// <summary>
    /// 保养趋势分析模型
    /// </summary>
    public class MaintenanceTrendAnalysis
    {
        /// <summary>
        /// 分析时间段开始日期
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 分析时间段结束日期
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 趋势数据点集合
        /// </summary>
        public List<MaintenanceTrendData> TrendData { get; set; } = new();

        /// <summary>
        /// 总体趋势方向（1: 上升, 0: 平稳, -1: 下降）
        /// </summary>
        public int TrendDirection { get; set; }

        /// <summary>
        /// 趋势强度（0-1之间，1表示趋势最强）
        /// </summary>
        public double TrendStrength { get; set; }

        /// <summary>
        /// 保养频率趋势
        /// </summary>
        public FrequencyTrend MaintenanceFrequencyTrend { get; set; } = new();

        /// <summary>
        /// 保养成本趋势
        /// </summary>
        public CostTrend MaintenanceCostTrend { get; set; } = new();

        /// <summary>
        /// 保养效率趋势
        /// </summary>
        public EfficiencyTrend MaintenanceEfficiencyTrend { get; set; } = new();

        /// <summary>
        /// 预测数据
        /// </summary>
        public List<MaintenancePrediction> Predictions { get; set; } = new();

        /// <summary>
        /// 关键洞察
        /// </summary>
        public List<string> KeyInsights { get; set; } = new();

        /// <summary>
        /// 建议措施
        /// </summary>
        public List<string> Recommendations { get; set; } = new();
    }



    /// <summary>
    /// 保养频率趋势
    /// </summary>
    public class FrequencyTrend
    {
        /// <summary>
        /// 当前周期平均保养频率（次/月）
        /// </summary>
        public double CurrentFrequency { get; set; }

        /// <summary>
        /// 上一周期平均保养频率（次/月）
        /// </summary>
        public double PreviousFrequency { get; set; }

        /// <summary>
        /// 频率变化率
        /// </summary>
        public double ChangeRate => PreviousFrequency > 0 ? (CurrentFrequency - PreviousFrequency) / PreviousFrequency * 100 : 0;

        /// <summary>
        /// 趋势描述
        /// </summary>
        public string TrendDescription { get; set; } = string.Empty;
    }

    /// <summary>
    /// 保养成本趋势
    /// </summary>
    public class CostTrend
    {
        /// <summary>
        /// 当前周期平均成本
        /// </summary>
        public decimal CurrentAverageCost { get; set; }

        /// <summary>
        /// 上一周期平均成本
        /// </summary>
        public decimal PreviousAverageCost { get; set; }

        /// <summary>
        /// 成本变化率
        /// </summary>
        public double ChangeRate => PreviousAverageCost > 0 ? (double)(CurrentAverageCost - PreviousAverageCost) / (double)PreviousAverageCost * 100 : 0;

        /// <summary>
        /// 当前周期总成本
        /// </summary>
        public decimal CurrentTotalCost { get; set; }

        /// <summary>
        /// 上一周期总成本
        /// </summary>
        public decimal PreviousTotalCost { get; set; }

        /// <summary>
        /// 总成本变化率
        /// </summary>
        public double TotalCostChangeRate => PreviousTotalCost > 0 ? (double)(CurrentTotalCost - PreviousTotalCost) / (double)PreviousTotalCost * 100 : 0;

        /// <summary>
        /// 趋势描述
        /// </summary>
        public string TrendDescription { get; set; } = string.Empty;
    }

    /// <summary>
    /// 保养效率趋势
    /// </summary>
    public class EfficiencyTrend
    {
        /// <summary>
        /// 当前周期平均耗时（分钟）
        /// </summary>
        public double CurrentAverageDuration { get; set; }

        /// <summary>
        /// 上一周期平均耗时（分钟）
        /// </summary>
        public double PreviousAverageDuration { get; set; }

        /// <summary>
        /// 耗时变化率
        /// </summary>
        public double DurationChangeRate => PreviousAverageDuration > 0 ? (CurrentAverageDuration - PreviousAverageDuration) / PreviousAverageDuration * 100 : 0;

        /// <summary>
        /// 当前周期成功率
        /// </summary>
        public double CurrentSuccessRate { get; set; }

        /// <summary>
        /// 上一周期成功率
        /// </summary>
        public double PreviousSuccessRate { get; set; }

        /// <summary>
        /// 成功率变化
        /// </summary>
        public double SuccessRateChange => CurrentSuccessRate - PreviousSuccessRate;

        /// <summary>
        /// 趋势描述
        /// </summary>
        public string TrendDescription { get; set; } = string.Empty;
    }

    /// <summary>
    /// 保养预测数据
    /// </summary>
    public class MaintenancePrediction
    {
        /// <summary>
        /// 预测日期
        /// </summary>
        public DateTime PredictedDate { get; set; }

        /// <summary>
        /// 预测保养记录数量
        /// </summary>
        public int PredictedRecordCount { get; set; }

        /// <summary>
        /// 预测总成本
        /// </summary>
        public decimal PredictedTotalCost { get; set; }

        /// <summary>
        /// 预测平均耗时（分钟）
        /// </summary>
        public double PredictedAverageDuration { get; set; }

        /// <summary>
        /// 置信度（0-1之间）
        /// </summary>
        public double Confidence { get; set; }
    }
}
