@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@using MudBlazor
@inject IEquipmentComponentService ComponentService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudForm @ref="_form" @bind-IsValid="_isFormValid" Model="_component">
            <MudGrid>
                <MudItem xs="12" sm="6">
                    <MudTextField @bind-Value="_component.Code"
                                 Label="部件编码"
                                 Variant="Variant.Outlined"
                                 Required="true"
                                 RequiredError="请输入部件编码"
                                 MaxLength="50" />
                </MudItem>
                <MudItem xs="12" sm="6">
                    <MudTextField @bind-Value="_component.Name"
                                 Label="部件名称"
                                 Variant="Variant.Outlined"
                                 Required="true"
                                 RequiredError="请输入部件名称"
                                 MaxLength="100" />
                </MudItem>
                
                <MudItem xs="12" sm="6">
                    <MudTextField @bind-Value="_component.Model"
                                 Label="型号"
                                 Variant="Variant.Outlined"
                                 MaxLength="100" />
                </MudItem>
                <MudItem xs="12" sm="6">
                    <MudSelect @bind-Value="_component.ComponentType"
                              Label="部件类型"
                              Variant="Variant.Outlined"
                              Required="true"
                              RequiredError="请选择部件类型">
                        <MudSelectItem Value="1">机械部件</MudSelectItem>
                        <MudSelectItem Value="2">电气部件</MudSelectItem>
                        <MudSelectItem Value="3">液压部件</MudSelectItem>
                        <MudSelectItem Value="4">气动部件</MudSelectItem>
                        <MudSelectItem Value="5">传感器</MudSelectItem>
                        <MudSelectItem Value="6">其他</MudSelectItem>
                    </MudSelect>
                </MudItem>
                
                <MudItem xs="12">
                    <MudTextField @bind-Value="_component.Specifications"
                                 Label="规格参数"
                                 Variant="Variant.Outlined"
                                 Lines="3"
                                 MaxLength="500" />
                </MudItem>
                
                <MudItem xs="12" sm="6">
                    <MudTextField @bind-Value="_component.Supplier"
                                 Label="供应商"
                                 Variant="Variant.Outlined"
                                 MaxLength="100" />
                </MudItem>
                <MudItem xs="12" sm="6">
                    <MudTextField @bind-Value="_component.SupplierContact"
                                 Label="供应商联系方式"
                                 Variant="Variant.Outlined"
                                 MaxLength="100" />
                </MudItem>
                
                <MudItem xs="12" sm="4">
                    <MudNumericField @bind-Value="_component.StockQuantity"
                                    Label="库存数量"
                                    Variant="Variant.Outlined"
                                    Min="0"
                                    Required="true"
                                    RequiredError="请输入库存数量" />
                </MudItem>
                <MudItem xs="12" sm="4">
                    <MudNumericField @bind-Value="_component.MinStockLevel"
                                    Label="最低库存"
                                    Variant="Variant.Outlined"
                                    Min="0"
                                    Required="true"
                                    RequiredError="请输入最低库存" />
                </MudItem>
                <MudItem xs="12" sm="4">
                    <MudTextField @bind-Value="_component.Unit"
                                 Label="单位"
                                 Variant="Variant.Outlined"
                                 Required="true"
                                 RequiredError="请输入单位"
                                 MaxLength="20" />
                </MudItem>
                
                <MudItem xs="12" sm="6">
                    <MudNumericField @bind-Value="_component.UnitPrice"
                                    Label="单价"
                                    Variant="Variant.Outlined"
                                    Min="0"
                                    Format="F2"
                                    Adornment="Adornment.Start"
                                    AdornmentText="¥" />
                </MudItem>
                <MudItem xs="12" sm="6">
                    <MudTextField @bind-Value="_component.StorageLocation"
                                 Label="存储位置"
                                 Variant="Variant.Outlined"
                                 MaxLength="100" />
                </MudItem>
                
                <MudItem xs="12" sm="6">
                    <MudNumericField @bind-Value="_component.LeadTime"
                                    Label="采购周期(天)"
                                    Variant="Variant.Outlined"
                                    Min="0" />
                </MudItem>
                <MudItem xs="12" sm="6">
                    <MudNumericField @bind-Value="_component.WarrantyPeriod"
                                    Label="保修期(月)"
                                    Variant="Variant.Outlined"
                                    Min="0" />
                </MudItem>
                
                <MudItem xs="12">
                    <MudGrid>
                        <MudItem xs="12" sm="4">
                            <MudSwitch T="bool" @bind-Checked="_component.IsCritical"
                                      Label="关键部件"
                                      Color="Color.Warning" />
                        </MudItem>
                        <MudItem xs="12" sm="4">
                            <MudSwitch T="bool" @bind-Checked="_component.IsActive"
                                      Label="启用状态"
                                      Color="Color.Success" />
                        </MudItem>
                        <MudItem xs="12" sm="4">
                            <MudSwitch T="bool" @bind-Checked="_component.RequiresInspection"
                                      Label="需要检验"
                                      Color="Color.Info" />
                        </MudItem>
                    </MudGrid>
                </MudItem>
                
                <MudItem xs="12">
                    <MudTextField @bind-Value="_component.Description"
                                 Label="备注说明"
                                 Variant="Variant.Outlined"
                                 Lines="3"
                                 MaxLength="500" />
                </MudItem>
            </MudGrid>
        </MudForm>
    </DialogContent>
    
    <DialogActions>
        <MudButton OnClick="Cancel" Variant="Variant.Text">取消</MudButton>
        <MudButton OnClick="Save" 
                  Variant="Variant.Filled" 
                  Color="Color.Primary"
                  Disabled="!_isFormValid || _isSaving"
                  StartIcon="@(IsEdit ? Icons.Material.Filled.Save : Icons.Material.Filled.Add)">
            @if (_isSaving)
            {
                <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                <span class="ml-2">保存中...</span>
            }
            else
            {
                @(IsEdit ? "保存" : "创建")
            }
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public bool IsEdit { get; set; }
    [Parameter] public int ComponentId { get; set; }

    private MudForm _form = null!;
    private bool _isFormValid;
    private bool _isSaving;
    private EquipmentComponent _component = new();

    protected override async Task OnInitializedAsync()
    {
        if (IsEdit && ComponentId > 0)
        {
            try
            {
                var component = await ComponentService.GetComponentByIdAsync(ComponentId);
                if (component != null)
                {
                    _component = component;
                }
                else
                {
                    Snackbar.Add("设备部件不存在", Severity.Error);
                    MudDialog.Cancel();
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"加载设备部件失败: {ex.Message}", Severity.Error);
                MudDialog.Cancel();
            }
        }
        else
        {
            // 新建时设置默认值
            _component.IsActive = true;
            _component.ComponentType = 1;
            _component.Unit = "个";
            _component.StockQuantity = 0;
            _component.MinStockLevel = 1;
            _component.UnitPrice = 0;
        }
    }

    private void Cancel() => MudDialog.Cancel();

    private async Task Save()
    {
        if (!_isFormValid)
        {
            Snackbar.Add("请检查表单输入", Severity.Warning);
            return;
        }

        try
        {
            _isSaving = true;
            StateHasChanged();

            if (IsEdit)
            {
                await ComponentService.UpdateComponentAsync(_component);
            }
            else
            {
                await ComponentService.CreateComponentAsync(_component);
            }

            MudDialog.Close(DialogResult.Ok(true));
        }
        catch (Exception ex)
        {
            Snackbar.Add($"保存失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isSaving = false;
            StateHasChanged();
        }
    }
}
