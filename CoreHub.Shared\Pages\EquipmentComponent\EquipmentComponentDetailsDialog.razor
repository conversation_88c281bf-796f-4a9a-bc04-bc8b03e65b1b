@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Models.Common
@using CoreHub.Shared.Services
@using CoreHub.Shared.Extensions
@using MudBlazor
@inject IEquipmentComponentService ComponentService
@inject IMaintenanceRecordService RecordService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        @if (_isLoading)
        {
            <div class="d-flex justify-center pa-8">
                <MudProgressCircular Indeterminate="true" />
            </div>
        }
        else if (_component != null)
        {
            <MudTabs Elevation="2" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-6">
                <!-- 基本信息 -->
                <MudTabPanel Text="基本信息" Icon="Icons.Material.Filled.Info">
                    <MudGrid>
                        <MudItem xs="12" sm="6">
                            <MudPaper Class="pa-4" Elevation="1">
                                <MudText Typo="Typo.h6" Class="mb-3">基本信息</MudText>
                                <MudSimpleTable Dense="true">
                                    <tbody>
                                        <tr>
                                            <td><strong>部件编码</strong></td>
                                            <td>@_component.Code</td>
                                        </tr>
                                        <tr>
                                            <td><strong>部件名称</strong></td>
                                            <td>
                                                @_component.Name
                                                @if (_component.IsCritical)
                                                {
                                                    <MudChip T="string" Size="Size.Small" Color="Color.Warning" Icon="Icons.Material.Filled.Star" Class="ml-2">关键部件</MudChip>
                                                }
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>型号</strong></td>
                                            <td>@_component.Model</td>
                                        </tr>
                                        <tr>
                                            <td><strong>部件分类</strong></td>
                                            <td>@(_component.Category ?? "未设置")</td>
                                        </tr>
                                        <tr>
                                            <td><strong>状态</strong></td>
                                            <td>
                                                <MudChip T="string" Size="Size.Small" Color="@(_component.IsEnabled ? Color.Success : Color.Default)">
                                                    @(_component.IsEnabled ? "启用" : "禁用")
                                                </MudChip>
                                            </td>
                                        </tr>
                                    </tbody>
                                </MudSimpleTable>
                            </MudPaper>
                        </MudItem>
                        
                        <MudItem xs="12" sm="6">
                            <MudPaper Class="pa-4" Elevation="1">
                                <MudText Typo="Typo.h6" Class="mb-3">库存信息</MudText>
                                <MudSimpleTable Dense="true">
                                    <tbody>
                                        <tr>
                                            <td><strong>当前库存</strong></td>
                                            <td>
                                                @_component.StockQuantity.ToFriendlyString() @_component.Unit
                                                <MudChip T="string" Size="Size.Small" Color="@GetStockStatusColor(_component.StockQuantity, _component.MinStockLevel)" Class="ml-2">
                                                    @GetStockStatusName(_component.StockQuantity, _component.MinStockLevel)
                                                </MudChip>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>最低库存</strong></td>
                                            <td>@_component.MinStockLevel.ToFriendlyString() @_component.Unit</td>
                                        </tr>
                                        <tr>
                                            <td><strong>单价</strong></td>
                                            <td>@((_component.UnitPrice ?? 0).ToCurrencyString())</td>
                                        </tr>
                                        <tr>
                                            <td><strong>库存价值</strong></td>
                                            <td>@((_component.StockQuantity * (_component.UnitPrice ?? 0)).ToCurrencyString())</td>
                                        </tr>
                                        <tr>
                                            <td><strong>存储位置</strong></td>
                                            <td>@_component.StorageLocation.IfEmpty("未设置")</td>
                                        </tr>
                                    </tbody>
                                </MudSimpleTable>
                            </MudPaper>
                        </MudItem>
                        
                        <MudItem xs="12">
                            <MudPaper Class="pa-4" Elevation="1">
                                <MudText Typo="Typo.h6" Class="mb-3">供应商信息</MudText>
                                <MudGrid>
                                    <MudItem xs="12" sm="4">
                                        <MudText Typo="Typo.subtitle2">供应商</MudText>
                                        <MudText Typo="Typo.body2">@_component.Supplier.IfEmpty("未设置")</MudText>
                                    </MudItem>
                                    <MudItem xs="12" sm="4">
                                        <MudText Typo="Typo.subtitle2">联系方式</MudText>
                                        <MudText Typo="Typo.body2">@_component.SupplierContact.IfEmpty("未设置")</MudText>
                                    </MudItem>
                                    <MudItem xs="12" sm="4">
                                        <MudText Typo="Typo.subtitle2">保修期</MudText>
                                        <MudText Typo="Typo.body2">@(_component.WarrantyDays?.ToString() ?? "未设置") 天</MudText>
                                    </MudItem>
                                </MudGrid>
                            </MudPaper>
                        </MudItem>
                        
                        @if (!string.IsNullOrEmpty(_component.Specifications))
                        {
                            <MudItem xs="12">
                                <MudPaper Class="pa-4" Elevation="1">
                                    <MudText Typo="Typo.h6" Class="mb-3">规格参数</MudText>
                                    <MudText Typo="Typo.body2" Style="white-space: pre-wrap;">@_component.Specifications</MudText>
                                </MudPaper>
                            </MudItem>
                        }
                        
                        @if (!string.IsNullOrEmpty(_component.Description))
                        {
                            <MudItem xs="12">
                                <MudPaper Class="pa-4" Elevation="1">
                                    <MudText Typo="Typo.h6" Class="mb-3">备注说明</MudText>
                                    <MudText Typo="Typo.body2" Style="white-space: pre-wrap;">@_component.Description</MudText>
                                </MudPaper>
                            </MudItem>
                        }
                    </MudGrid>
                </MudTabPanel>
                
                <!-- 保养记录 -->
                <MudTabPanel Text="保养记录" Icon="Icons.Material.Filled.Build">
                    @if (_maintenanceRecords == null)
                    {
                        <div class="d-flex justify-center pa-4">
                            <MudProgressCircular Indeterminate="true" Size="Size.Small" />
                            <MudText Class="ml-2">加载保养记录...</MudText>
                        </div>
                    }
                    else if (!_maintenanceRecords.Any())
                    {
                        <MudPaper Class="pa-8 text-center" Elevation="0">
                            <MudIcon Icon="Icons.Material.Filled.Build" Size="Size.Large" Color="Color.Secondary" />
                            <MudText Typo="Typo.h6" Color="Color.Secondary" Class="mt-2">暂无保养记录</MudText>
                        </MudPaper>
                    }
                    else
                    {
                        <MudTable Items="_maintenanceRecords" Dense="true" Hover="true">
                            <HeaderContent>
                                <MudTh>保养日期</MudTh>
                                <MudTh>保养类型</MudTh>
                                <MudTh>保养人员</MudTh>
                                <MudTh>保养结果</MudTh>
                                <MudTh>耗时</MudTh>
                                <MudTh>费用</MudTh>
                            </HeaderContent>
                            <RowTemplate>
                                <MudTd>@context.MaintenanceDate.ToStandardString()</MudTd>
                                <MudTd>@GetMaintenanceTypeName(context.MaintenanceType)</MudTd>
                                <MudTd>@(context.MaintenancePerson?.DisplayName ?? "未知")</MudTd>
                                <MudTd>
                                    <MudChip T="string" Size="Size.Small" Color="@GetMaintenanceResultColor(context.MaintenanceResult)">
                                        @GetMaintenanceResultName(context.MaintenanceResult)
                                    </MudChip>
                                </MudTd>
                                <MudTd>@((context.ActualDuration ?? 0).ToFriendlyString()) 分钟</MudTd>
                                <MudTd>@((context.MaintenanceCost ?? 0).ToCurrencyString())</MudTd>
                            </RowTemplate>
                        </MudTable>
                    }
                </MudTabPanel>
                
                <!-- 使用统计 -->
                <MudTabPanel Text="使用统计" Icon="Icons.Material.Filled.Analytics">
                    @if (_usageStatistics == null)
                    {
                        <div class="d-flex justify-center pa-4">
                            <MudProgressCircular Indeterminate="true" Size="Size.Small" />
                            <MudText Class="ml-2">加载统计数据...</MudText>
                        </div>
                    }
                    else
                    {
                        <MudGrid>
                            <MudItem xs="12" sm="6" md="3">
                                <MudPaper Class="pa-4 text-center" Elevation="2">
                                    <MudText Typo="Typo.h4" Color="Color.Primary">@_usageStatistics.TotalMaintenanceCount</MudText>
                                    <MudText Typo="Typo.body2">保养次数</MudText>
                                </MudPaper>
                            </MudItem>
                            <MudItem xs="12" sm="6" md="3">
                                <MudPaper Class="pa-4 text-center" Elevation="2">
                                    <MudText Typo="Typo.h4" Color="Color.Success">@_usageStatistics.TotalMaintenanceCost.ToCurrencyString()</MudText>
                                    <MudText Typo="Typo.body2">保养费用</MudText>
                                </MudPaper>
                            </MudItem>
                            <MudItem xs="12" sm="6" md="3">
                                <MudPaper Class="pa-4 text-center" Elevation="2">
                                    <MudText Typo="Typo.h4" Color="Color.Info">@(((int)_usageStatistics.AverageMaintenanceDuration).ToDurationString())</MudText>
                                    <MudText Typo="Typo.body2">平均耗时</MudText>
                                </MudPaper>
                            </MudItem>
                            <MudItem xs="12" sm="6" md="3">
                                <MudPaper Class="pa-4 text-center" Elevation="2">
                                    <MudText Typo="Typo.h4" Color="Color.Warning">@_usageStatistics.LastMaintenanceDate.ToFriendlyString()</MudText>
                                    <MudText Typo="Typo.body2">最近保养</MudText>
                                </MudPaper>
                            </MudItem>
                        </MudGrid>
                    }
                </MudTabPanel>
            </MudTabs>
        }
    </DialogContent>
    
    <DialogActions>
        <MudButton OnClick="Close" Variant="Variant.Text">关闭</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public int ComponentId { get; set; }

    private bool _isLoading = true;
    private EquipmentComponent? _component;
    private List<MaintenanceRecord>? _maintenanceRecords;
    private Models.ComponentUsageStatistics? _usageStatistics;

    protected override async Task OnInitializedAsync()
    {
        await LoadComponentDetails();
        await LoadMaintenanceRecords();
        await LoadUsageStatistics();
    }

    private async Task LoadComponentDetails()
    {
        try
        {
            _component = await ComponentService.GetComponentByIdAsync(ComponentId);
            if (_component == null)
            {
                Snackbar.Add("设备部件不存在", Severity.Error);
                MudDialog.Cancel();
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载设备部件失败: {ex.Message}", Severity.Error);
            MudDialog.Cancel();
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadMaintenanceRecords()
    {
        try
        {
            _maintenanceRecords = await RecordService.GetRecordsByComponentIdAsync(ComponentId, 10);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"加载保养记录失败: {ex.Message}");
        }
        finally
        {
            StateHasChanged();
        }
    }

    private async Task LoadUsageStatistics()
    {
        try
        {
            _usageStatistics = await ComponentService.GetComponentUsageStatisticsAsync(ComponentId);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"加载使用统计失败: {ex.Message}");
        }
        finally
        {
            StateHasChanged();
        }
    }

    private void Close() => MudDialog.Close();

    private string GetComponentStatusName(int status)
    {
        return status switch
        {
            1 => "正常",
            2 => "停用",
            3 => "淘汰",
            _ => "未知"
        };
    }

    private Color GetStockStatusColor(int stockQuantity, int minStockLevel)
    {
        if (stockQuantity <= 0)
            return Color.Error;
        if (stockQuantity <= minStockLevel)
            return Color.Warning;
        return Color.Success;
    }

    private string GetStockStatusName(int stockQuantity, int minStockLevel)
    {
        if (stockQuantity <= 0)
            return "缺货";
        if (stockQuantity <= minStockLevel)
            return "不足";
        return "正常";
    }

    private string GetMaintenanceTypeName(int type)
    {
        return type switch
        {
            1 => "预防性保养",
            2 => "纠正性保养",
            3 => "预测性保养",
            4 => "紧急维修",
            _ => "其他"
        };
    }

    private Color GetMaintenanceResultColor(int result)
    {
        return result switch
        {
            1 => Color.Success,
            2 => Color.Warning,
            3 => Color.Error,
            _ => Color.Default
        };
    }

    private string GetMaintenanceResultName(int result)
    {
        return result switch
        {
            1 => "正常",
            2 => "需要关注",
            3 => "需要维修",
            _ => "未知"
        };
    }
}
