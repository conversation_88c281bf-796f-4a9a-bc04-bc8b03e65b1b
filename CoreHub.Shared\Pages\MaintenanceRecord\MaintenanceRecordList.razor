@page "/maintenance-record-management"
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Models.Common
@using CoreHub.Shared.Models
@using CoreHub.Shared.Services
@using CoreHub.Shared.Components.Common
@using CoreHub.Shared.Components
@using CoreHub.Shared.Extensions
@using MudBlazor
@inject IMaintenanceRecordService RecordService
@inject IEquipmentComponentService ComponentService
@inject IDialogService DialogService
@inject ISnackbar Snackbar

<PageTitle>保养记录管理</PageTitle>

<PermissionView RequiredPermission="MaintenanceRecordManagement.View">
    <ChildContent>
        <MudContainer MaxWidth="MaxWidth.False" Class="pa-4">
            <MudText Typo="Typo.h4" Class="mb-4">保养记录管理</MudText>
    
    <!-- 统计卡片 -->
    <MudGrid Class="mb-4">
        <MudItem xs="12" sm="6" md="3">
            <MudPaper Class="pa-4 text-center" Elevation="2">
                <MudIcon Icon="Icons.Material.Filled.Build" Color="Color.Primary" Size="Size.Large" />
                <MudText Typo="Typo.h4" Color="Color.Primary">@_statistics.TotalRecords</MudText>
                <MudText Typo="Typo.body2">总记录数</MudText>
            </MudPaper>
        </MudItem>
        <MudItem xs="12" sm="6" md="3">
            <MudPaper Class="pa-4 text-center" Elevation="2">
                <MudIcon Icon="Icons.Material.Filled.CheckCircle" Color="Color.Success" Size="Size.Large" />
                <MudText Typo="Typo.h4" Color="Color.Success">@_statistics.CompletedRecords</MudText>
                <MudText Typo="Typo.body2">已完成</MudText>
            </MudPaper>
        </MudItem>
        <MudItem xs="12" sm="6" md="3">
            <MudPaper Class="pa-4 text-center" Elevation="2">
                <MudIcon Icon="Icons.Material.Filled.Schedule" Color="Color.Warning" Size="Size.Large" />
                <MudText Typo="Typo.h4" Color="Color.Warning">@_statistics.PendingRecords</MudText>
                <MudText Typo="Typo.body2">待审核</MudText>
            </MudPaper>
        </MudItem>
        <MudItem xs="12" sm="6" md="3">
            <MudPaper Class="pa-4 text-center" Elevation="2">
                <MudIcon Icon="Icons.Material.Filled.AttachMoney" Color="Color.Info" Size="Size.Large" />
                <MudText Typo="Typo.h4" Color="Color.Info">@_statistics.TotalCost.ToCurrencyString()</MudText>
                <MudText Typo="Typo.body2">总费用</MudText>
            </MudPaper>
        </MudItem>
    </MudGrid>

    <!-- 简化的搜索区域 -->
    <MudPaper Class="pa-4 mb-4" Elevation="2">
        <MudGrid AlignItems="Center">
            <MudItem xs="12" sm="6" md="4">
                <MudTextField @bind-Value="_filter.Keyword"
                             Label="搜索关键词"
                             Placeholder="搜索记录编号、部件名称、保养人员..."
                             Variant="Variant.Outlined"
                             Adornment="Adornment.End"
                             AdornmentIcon="Icons.Material.Filled.Search"
                             OnAdornmentClick="SearchRecords"
                             Clearable="true" />
            </MudItem>
            <MudItem xs="12" sm="6" md="2">
                <MudButton StartIcon="Icons.Material.Filled.Search"
                          OnClick="SearchRecords"
                          Color="Color.Primary"
                          Variant="Variant.Filled">
                    搜索
                </MudButton>
            </MudItem>
            <MudItem xs="12" sm="6" md="2">
                <MudButton StartIcon="Icons.Material.Filled.Clear"
                          OnClick="ClearFilters"
                          Color="Color.Secondary"
                          Variant="Variant.Outlined">
                    清除
                </MudButton>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- 操作按钮区域 -->
    <MudPaper Class="pa-4 mb-4" Elevation="1">
        <MudGrid AlignItems="Center">
            <MudItem xs="12" sm="6">
                <MudButtonGroup Variant="Variant.Filled">
                    <MudButton StartIcon="Icons.Material.Filled.Add"
                              Color="Color.Primary"
                              OnClick="OpenCreateDialog">
                        新增记录
                    </MudButton>
                    <MudButton StartIcon="Icons.Material.Filled.Assessment"
                              Color="Color.Info"
                              OnClick="ShowStatistics">
                        统计分析
                    </MudButton>
                    <MudButton StartIcon="Icons.Material.Filled.FileDownload"
                              Color="Color.Secondary"
                              OnClick="ExportRecords">
                        导出数据
                    </MudButton>
                </MudButtonGroup>
            </MudItem>
            <MudItem xs="12" sm="6" Class="d-flex justify-end">
                <MudChip T="string" Icon="Icons.Material.Filled.Today"
                        Color="Color.Primary"
                        Size="Size.Medium"
                        OnClick="ShowTodayRecords">
                    今日记录: @_todayRecordsCount
                </MudChip>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- 数据表格 -->
    @if (_isLoading)
    {
        <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
        <MudText>正在加载...</MudText>
    }
    else if (_hasError)
    {
        <MudAlert Severity="Severity.Error">
            @_errorMessage
            <MudButton OnClick="LoadRecords" Color="Color.Primary" Variant="Variant.Text">重试</MudButton>
        </MudAlert>
    }
    else if (_pagedResult?.Items?.Any() != true)
    {
        <MudPaper Class="pa-8 text-center">
            <MudText Typo="Typo.h6">暂无保养记录</MudText>
            <MudText>点击上方"新增记录"按钮创建第一个保养记录</MudText>
            <MudButton OnClick="OpenCreateDialog" Color="Color.Primary" Variant="Variant.Filled" Class="mt-4">
                新增记录
            </MudButton>
        </MudPaper>
    }
    else
    {
        <MudTable Items="@_pagedResult?.Items"
                  Hover="true"
                  Striped="true"
                  Dense="true"
                  FixedHeader="true"
                  Height="600px">
            <HeaderContent>
                <MudTh>记录编号</MudTh>
                <MudTh>部件信息</MudTh>
                <MudTh>保养类型</MudTh>
                <MudTh>保养日期</MudTh>
                <MudTh>保养人员</MudTh>
                <MudTh>耗时/费用</MudTh>
                <MudTh>保养结果</MudTh>
                <MudTh>审核状态</MudTh>
                <MudTh>操作</MudTh>
            </HeaderContent>
            <RowTemplate>
                <MudTd DataLabel="记录编号">
                    <MudText Typo="Typo.body2" Style="font-family: monospace;">@context.RecordNumber</MudText>
                </MudTd>
                <MudTd DataLabel="部件信息">
                    <MudText Typo="Typo.body2">@context.ComponentName</MudText>
                    <MudText Typo="Typo.caption" Color="Color.Secondary">@context.ComponentCode</MudText>
                </MudTd>
                <MudTd DataLabel="保养类型">
                    <MudChip T="string" Size="Size.Small" Color="@GetMaintenanceTypeColor(context.MaintenanceType)">
                        @GetMaintenanceTypeName(context.MaintenanceType)
                    </MudChip>
                </MudTd>
                <MudTd DataLabel="保养日期">
                    <MudText Typo="Typo.body2">@context.MaintenanceDate.ToDateString()</MudText>
                    @if (context.MaintenanceDate.IsToday())
                    {
                        <MudChip T="string" Size="Size.Small" Color="Color.Primary">今日</MudChip>
                    }
                </MudTd>
                <MudTd DataLabel="保养人员">@context.MaintenancePersonName</MudTd>
                <MudTd DataLabel="耗时/费用">
                    <MudText Typo="Typo.body2">@context.ActualDuration.ToDurationString()</MudText>
                    <MudText Typo="Typo.caption" Color="Color.Secondary">@context.ActualCost.ToCurrencyString()</MudText>
                </MudTd>
                <MudTd DataLabel="保养结果">
                    <MudChip T="string" Size="Size.Small" Color="@GetMaintenanceResultColor(context.MaintenanceResult)">
                        @GetMaintenanceResultName(context.MaintenanceResult)
                    </MudChip>
                </MudTd>
                <MudTd DataLabel="审核状态">
                    <MudChip T="string" Size="Size.Small" Color="@GetReviewStatusColor(context.ReviewStatus)">
                        @GetReviewStatusName(context.ReviewStatus)
                    </MudChip>
                </MudTd>
                <MudTd DataLabel="操作">
                    <MudButtonGroup Size="Size.Small" Variant="Variant.Text">
                        @if (context.ReviewStatus == 1) // 待审核
                        {
                            <MudIconButton Icon="Icons.Material.Filled.CheckCircle"
                                          Color="Color.Success"
                                          Size="Size.Small"
                                          OnClick="() => ReviewRecord(context, 2)"
                                          Title="通过审核" />
                            <MudIconButton Icon="Icons.Material.Filled.Cancel"
                                          Color="Color.Error"
                                          Size="Size.Small"
                                          OnClick="() => ReviewRecord(context, 3)"
                                          Title="拒绝审核" />
                        }
                        <MudIconButton Icon="Icons.Material.Filled.Edit"
                                      Color="Color.Primary"
                                      Size="Size.Small"
                                      OnClick="() => OpenEditDialog(context)" />
                        <MudIconButton Icon="Icons.Material.Filled.Visibility"
                                      Color="Color.Info"
                                      Size="Size.Small"
                                      OnClick="() => ViewRecordDetails(context)" />
                        <MudIconButton Icon="Icons.Material.Filled.Delete"
                                      Color="Color.Error"
                                      Size="Size.Small"
                                      OnClick="() => DeleteRecord(context)" />
                    </MudButtonGroup>
                </MudTd>
            </RowTemplate>
        </MudTable>
    }

    <!-- 分页组件 -->
    @if (_pagedResult != null && _pagedResult.Items?.Any() == true)
    {
        <MudPaper Class="pa-4 mt-4">
            <MudGrid AlignItems="Center" Justify="Justify.SpaceBetween">
                <MudItem>
                    <MudText>共 @_pagedResult.TotalCount 条记录，第 @_pagedResult.PageIndex 页，共 @_pagedResult.TotalPages 页</MudText>
                </MudItem>
                <MudItem>
                    <MudButtonGroup>
                        <MudButton Disabled="@(_pagedResult.PageIndex <= 1)" OnClick="() => OnPageChanged(_pagedResult.PageIndex - 1)">上一页</MudButton>
                        <MudButton Disabled="@(_pagedResult.PageIndex >= _pagedResult.TotalPages)" OnClick="() => OnPageChanged(_pagedResult.PageIndex + 1)">下一页</MudButton>
                    </MudButtonGroup>
                </MudItem>
            </MudGrid>
        </MudPaper>
    }
</MudContainer>

@code {
    private MaintenanceRecordFilter _filter = new();
    private PagedResult<MaintenanceRecord>? _pagedResult;
    private bool _isLoading = true;
    private bool _hasError = false;
    private string? _errorMessage;
    private MaintenanceRecordStatistics _statistics = new();
    private int _todayRecordsCount = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadRecords();
        await LoadStatistics();
        await LoadTodayRecordsCount();
    }

    private async Task LoadRecords()
    {
        try
        {
            _isLoading = true;
            _hasError = false;
            StateHasChanged();

            _pagedResult = await RecordService.SearchRecordsAsync(_filter);
        }
        catch (Exception ex)
        {
            _hasError = true;
            _errorMessage = ex.Message;
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadStatistics()
    {
        try
        {
            _statistics = await RecordService.GetRecordStatisticsAsync();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"加载统计数据失败: {ex.Message}");
        }
    }

    private async Task LoadTodayRecordsCount()
    {
        try
        {
            var todayFilter = new MaintenanceRecordFilter
            {
                MaintenanceStartDate = DateTime.Today,
                MaintenanceEndDate = DateTime.Today,
                PageSize = 1
            };
            var result = await RecordService.SearchRecordsAsync(todayFilter);
            _todayRecordsCount = result.TotalCount;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"加载今日记录数失败: {ex.Message}");
        }
    }

    private async Task SearchRecords()
    {
        _filter.PageIndex = 1;
        await LoadRecords();
    }

    private async Task ClearFilters()
    {
        _filter.Keyword = null;
        _filter.PageIndex = 1;
        await LoadRecords();
    }

    private async Task OnPageChanged(int pageIndex)
    {
        _filter.PageIndex = pageIndex;
        await LoadRecords();
    }

    private async Task OnPageSizeChanged(int pageSize)
    {
        _filter.PageSize = pageSize;
        _filter.PageIndex = 1;
        await LoadRecords();
    }

    private async Task ShowTodayRecords()
    {
        _filter.Keyword = null;
        _filter.PageIndex = 1;
        _filter.MaintenanceStartDate = DateTime.Today;
        _filter.MaintenanceEndDate = DateTime.Today;
        await LoadRecords();
    }

    private async Task OpenCreateDialog()
    {
        var parameters = new DialogParameters { ["IsEdit"] = false };
        var options = new DialogOptions { CloseButton = true, MaxWidth = MaxWidth.Large, FullWidth = true };
        
        var dialog = await DialogService.ShowAsync<MaintenanceRecordDialog>("新增保养记录", parameters, options);
        var result = await dialog.Result;
        
        if (!result.Canceled)
        {
            await LoadRecords();
            await LoadStatistics();
            await LoadTodayRecordsCount();
            Snackbar.Add("保养记录创建成功", Severity.Success);
        }
    }

    private async Task OpenEditDialog(MaintenanceRecord record)
    {
        var parameters = new DialogParameters 
        { 
            ["IsEdit"] = true,
            ["RecordId"] = record.Id
        };
        var options = new DialogOptions { CloseButton = true, MaxWidth = MaxWidth.Large, FullWidth = true };
        
        var dialog = await DialogService.ShowAsync<MaintenanceRecordDialog>("编辑保养记录", parameters, options);
        var result = await dialog.Result;
        
        if (!result.Canceled)
        {
            await LoadRecords();
            await LoadStatistics();
            Snackbar.Add("保养记录更新成功", Severity.Success);
        }
    }

    private async Task ViewRecordDetails(MaintenanceRecord record)
    {
        var parameters = new DialogParameters { ["RecordId"] = record.Id };
        var options = new DialogOptions { CloseButton = true, MaxWidth = MaxWidth.Large, FullWidth = true };
        
        await DialogService.ShowAsync<MaintenanceRecordDetailsDialog>("保养记录详情", parameters, options);
    }

    private async Task ReviewRecord(MaintenanceRecord record, int reviewStatus)
    {
        var statusName = reviewStatus == 2 ? "通过" : "拒绝";
        var confirmed = await DialogService.ShowConfirmAsync(
            $"{statusName}审核",
            $"确定要{statusName}保养记录 \"{record.RecordNumber}\" 的审核吗？");
        
        if (confirmed)
        {
            try
            {
                await RecordService.ReviewRecordAsync(record.Id, 1, reviewStatus, null); // TODO: 获取当前用户ID
                await LoadRecords();
                await LoadStatistics();
                Snackbar.Add($"审核{statusName}成功", Severity.Success);
            }
            catch (Exception ex)
            {
                Snackbar.Add($"审核失败: {ex.Message}", Severity.Error);
            }
        }
    }

    private async Task DeleteRecord(MaintenanceRecord record)
    {
        var confirmed = await DialogService.ShowDeleteConfirmAsync(
            record.RecordNumber, 
            "删除后将无法恢复，相关的统计数据也会受到影响。");
        
        if (confirmed)
        {
            try
            {
                await RecordService.DeleteRecordAsync(record.Id);
                await LoadRecords();
                await LoadStatistics();
                await LoadTodayRecordsCount();
                Snackbar.Add("保养记录删除成功", Severity.Success);
            }
            catch (Exception ex)
            {
                Snackbar.Add($"删除失败: {ex.Message}", Severity.Error);
            }
        }
    }

    private async Task ShowStatistics()
    {
        var options = new DialogOptions { CloseButton = true, MaxWidth = MaxWidth.Large, FullWidth = true };
        await DialogService.ShowAsync<MaintenanceStatisticsDialog>("保养统计分析", options);
    }

    private async Task ExportRecords()
    {
        try
        {
            var data = await RecordService.ExportRecordsAsync(_filter.StartDate, _filter.EndDate, _filter.ComponentId);
            // TODO: 实现文件下载
            Snackbar.Add("数据导出成功", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"导出失败: {ex.Message}", Severity.Error);
        }
    }

    private string GetMaintenanceTypeName(int type)
    {
        return type switch
        {
            1 => "预防性保养",
            2 => "纠正性保养",
            3 => "预测性保养",
            4 => "紧急维修",
            _ => "其他"
        };
    }

    private Color GetMaintenanceTypeColor(int type)
    {
        return type switch
        {
            1 => Color.Primary,
            2 => Color.Warning,
            3 => Color.Info,
            4 => Color.Error,
            _ => Color.Default
        };
    }

    private string GetMaintenanceResultName(int result)
    {
        return result switch
        {
            1 => "正常",
            2 => "需要关注",
            3 => "需要维修",
            _ => "未知"
        };
    }

    private Color GetMaintenanceResultColor(int result)
    {
        return result switch
        {
            1 => Color.Success,
            2 => Color.Warning,
            3 => Color.Error,
            _ => Color.Default
        };
    }

    private string GetReviewStatusName(int status)
    {
        return status switch
        {
            1 => "待审核",
            2 => "已通过",
            3 => "已拒绝",
            _ => "未知"
        };
    }

    private Color GetReviewStatusColor(int status)
    {
        return status switch
        {
            1 => Color.Warning,
            2 => Color.Success,
            3 => Color.Error,
            _ => Color.Default
        };
    }


}
    </ChildContent>
</PermissionView>
