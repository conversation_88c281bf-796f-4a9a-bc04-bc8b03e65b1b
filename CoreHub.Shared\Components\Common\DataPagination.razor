@using CoreHub.Shared.Models.Common
@using MudBlazor

@if (PagedResult != null && PagedResult.TotalCount > 0)
{
    <MudPaper Class="pa-4 mt-4" Elevation="1">
        <MudGrid AlignItems="Center" Justify="Justify.SpaceBetween">
            <MudItem xs="12" sm="6">
                <MudText Typo="Typo.body2" Color="Color.Secondary">
                    显示第 @PagedResult.StartIndex - @PagedResult.EndIndex 条，共 @PagedResult.TotalCount 条记录
                </MudText>
            </MudItem>
            
            <MudItem xs="12" sm="6" Class="d-flex justify-end align-center">
                <MudSelect @bind-Value="CurrentPageSize"
                          Label="每页显示"
                          Variant="Variant.Outlined"
                          Margin="Margin.Dense"
                          Style="width: 100px; margin-right: 16px;">
                    @foreach (var size in PageSizeOptions)
                    {
                        <MudSelectItem Value="@size">@size</MudSelectItem>
                    }
                </MudSelect>
                
                <MudPagination Count="@PagedResult.TotalPages"
                              Selected="@PagedResult.PageIndex"
                              SelectedChanged="OnPageChanged"
                              ShowFirstButton="true"
                              ShowLastButton="true"
                              ShowPreviousButton="true"
                              ShowNextButton="true"
                              BoundaryCount="1"
                              MiddleCount="3"
                              Class="mud-pagination-custom" />
            </MudItem>
        </MudGrid>
    </MudPaper>
}

<style>
    .mud-pagination-custom .mud-pagination-item {
        margin: 0 2px;
    }
    
    .mud-pagination-custom .mud-button-root {
        min-width: 32px;
        height: 32px;
    }
</style>

@code {
    [Parameter] public PagedResult<object>? PagedResult { get; set; }
    [Parameter] public EventCallback<int> OnPageIndexChanged { get; set; }
    [Parameter] public EventCallback<int> OnPageSizeChanged { get; set; }
    [Parameter] public List<int> PageSizeOptions { get; set; } = new() { 10, 20, 50, 100 };

    private int _currentPageSize;
    
    private int CurrentPageSize
    {
        get => _currentPageSize;
        set
        {
            if (_currentPageSize != value)
            {
                _currentPageSize = value;
                OnPageSizeChanged.InvokeAsync(value);
            }
        }
    }

    protected override void OnParametersSet()
    {
        if (PagedResult != null && _currentPageSize != PagedResult.PageSize)
        {
            _currentPageSize = PagedResult.PageSize;
        }

        if (TypedPagedResult != null && _typedCurrentPageSize != TypedPagedResult.PageSize)
        {
            _typedCurrentPageSize = TypedPagedResult.PageSize;
        }
    }

    private async Task OnPageChanged(int page)
    {
        if (PagedResult != null && page != PagedResult.PageIndex)
        {
            await OnPageIndexChanged.InvokeAsync(page);
        }
    }
}

@* 泛型版本的分页组件 *@
@typeparam T

@if (TypedPagedResult != null && TypedPagedResult.TotalCount > 0)
{
    <MudPaper Class="pa-4 mt-4" Elevation="1">
        <MudGrid AlignItems="Center" Justify="Justify.SpaceBetween">
            <MudItem xs="12" sm="6">
                <MudText Typo="Typo.body2" Color="Color.Secondary">
                    显示第 @TypedPagedResult.StartIndex - @TypedPagedResult.EndIndex 条，共 @TypedPagedResult.TotalCount 条记录
                </MudText>
            </MudItem>
            
            <MudItem xs="12" sm="6" Class="d-flex justify-end align-center">
                <MudSelect @bind-Value="TypedCurrentPageSize"
                          Label="每页显示"
                          Variant="Variant.Outlined"
                          Margin="Margin.Dense"
                          Style="width: 100px; margin-right: 16px;">
                    @foreach (var size in TypedPageSizeOptions)
                    {
                        <MudSelectItem Value="@size">@size</MudSelectItem>
                    }
                </MudSelect>
                
                <MudPagination Count="@TypedPagedResult.TotalPages"
                              Selected="@TypedPagedResult.PageIndex"
                              SelectedChanged="OnTypedPageChanged"
                              ShowFirstButton="true"
                              ShowLastButton="true"
                              ShowPreviousButton="true"
                              ShowNextButton="true"
                              BoundaryCount="1"
                              MiddleCount="3"
                              Class="mud-pagination-custom" />
            </MudItem>
        </MudGrid>
    </MudPaper>
}

@code {
    [Parameter] public PagedResult<T>? TypedPagedResult { get; set; }
    [Parameter] public EventCallback<int> OnTypedPageIndexChanged { get; set; }
    [Parameter] public EventCallback<int> OnTypedPageSizeChanged { get; set; }
    [Parameter] public List<int> TypedPageSizeOptions { get; set; } = new() { 10, 20, 50, 100 };

    private int _typedCurrentPageSize;
    
    private int TypedCurrentPageSize
    {
        get => _typedCurrentPageSize;
        set
        {
            if (_typedCurrentPageSize != value)
            {
                _typedCurrentPageSize = value;
                OnTypedPageSizeChanged.InvokeAsync(value);
            }
        }
    }



    private async Task OnTypedPageChanged(int page)
    {
        if (TypedPagedResult != null && page != TypedPagedResult.PageIndex)
        {
            await OnTypedPageIndexChanged.InvokeAsync(page);
        }
    }
}
