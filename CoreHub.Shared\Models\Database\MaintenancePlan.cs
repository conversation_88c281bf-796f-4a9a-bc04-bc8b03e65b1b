using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace CoreHub.Shared.Models.Database
{
    /// <summary>
    /// 保养计划实体
    /// </summary>
    [SugarTable("MaintenancePlans")]
    public class MaintenancePlan
    {
        /// <summary>
        /// 保养计划ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 计划编码（唯一）
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        [Required(ErrorMessage = "计划编码不能为空")]
        [StringLength(50, ErrorMessage = "计划编码长度不能超过50个字符")]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 计划名称
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        [Required(ErrorMessage = "计划名称不能为空")]
        [StringLength(100, ErrorMessage = "计划名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 计划名称（别名，用于兼容）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string PlanName { get => Name; set => Name = value; }

        /// <summary>
        /// 计划描述（仅用于显示，不映射到数据库）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string? Description { get; set; }

        /// <summary>
        /// 关联的设备部件ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "设备部件不能为空")]
        public int ComponentId { get; set; }

        /// <summary>
        /// 保养类型（1=预防性保养,2=定期检查,3=深度保养,4=应急保养）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int MaintenanceType { get; set; } = 1;

        /// <summary>
        /// 周期类型（1=按天数,2=按使用次数,3=按运行小时,4=按生产数量）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int CycleType { get; set; } = 1;

        /// <summary>
        /// 周期值（根据周期类型确定单位）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int CycleValue { get; set; }

        /// <summary>
        /// 提前提醒天数
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int ReminderDays { get; set; } = 3;

        /// <summary>
        /// 预计保养时长（分钟）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? EstimatedDuration { get; set; }

        /// <summary>
        /// 预计成本（仅用于显示，不映射到数据库）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public decimal? EstimatedCost { get; set; }

        /// <summary>
        /// 开始日期（仅用于显示，不映射到数据库）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 安全要求（仅用于显示，不映射到数据库）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string? SafetyRequirements { get; set; }

        /// <summary>
        /// 是否自动创建记录（仅用于显示，不映射到数据库）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool AutoCreateRecord { get; set; } = true;

        /// <summary>
        /// 是否发送提醒（仅用于显示，不映射到数据库）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool SendReminder { get; set; } = true;

        /// <summary>
        /// 保养内容描述
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "保养内容描述长度不能超过1000个字符")]
        public string? MaintenanceContent { get; set; }

        /// <summary>
        /// 保养标准/要求
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "保养标准长度不能超过1000个字符")]
        public string? MaintenanceStandard { get; set; }

        /// <summary>
        /// 所需工具清单
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "所需工具清单长度不能超过500个字符")]
        public string? RequiredTools { get; set; }

        /// <summary>
        /// 所需材料清单
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "所需材料清单长度不能超过500个字符")]
        public string? RequiredMaterials { get; set; }

        /// <summary>
        /// 负责部门ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? ResponsibleDepartmentId { get; set; }

        /// <summary>
        /// 负责人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? ResponsiblePersonId { get; set; }

        /// <summary>
        /// 优先级（1=低,2=中,3=高,4=紧急）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int Priority { get; set; } = 2;

        /// <summary>
        /// 计划状态（1=启用,2=暂停,3=停用）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int Status { get; set; } = 1;

        /// <summary>
        /// 下次保养日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? NextMaintenanceDate { get; set; }

        /// <summary>
        /// 最后保养日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? LastMaintenanceDate { get; set; }

        /// <summary>
        /// 累计保养次数
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int TotalMaintenanceCount { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 是否激活（别名，用于兼容）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool IsActive { get => IsEnabled && Status == 1; set { IsEnabled = value; if (value) Status = 1; } }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
        public string? Remark { get; set; }

        // 导航属性
        /// <summary>
        /// 关联的设备部件
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public EquipmentComponent? Component { get; set; }

        /// <summary>
        /// 负责部门
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Department? ResponsibleDepartment { get; set; }

        /// <summary>
        /// 负责人
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public User? ResponsiblePerson { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public User? Creator { get; set; }

        /// <summary>
        /// 更新人
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public User? Updater { get; set; }

        /// <summary>
        /// 保养记录
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<MaintenanceRecord> MaintenanceRecords { get; set; } = new List<MaintenanceRecord>();

        /// <summary>
        /// 部件名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ComponentName => Component?.Name ?? "";

        /// <summary>
        /// 部件编码
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ComponentCode => Component?.Code ?? "";

        /// <summary>
        /// 负责人姓名
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ResponsiblePersonName => ResponsiblePerson?.DisplayName ?? "";

        /// <summary>
        /// 保养类型名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string MaintenanceTypeName => MaintenanceType switch
        {
            1 => "预防性保养",
            2 => "定期检查",
            3 => "深度保养",
            4 => "应急保养",
            _ => "未知"
        };

        /// <summary>
        /// 周期类型名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string CycleTypeName => CycleType switch
        {
            1 => "按天数",
            2 => "按使用次数",
            3 => "按运行小时",
            4 => "按生产数量",
            _ => "未知"
        };

        /// <summary>
        /// 优先级名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string PriorityName => Priority switch
        {
            1 => "低",
            2 => "中",
            3 => "高",
            4 => "紧急",
            _ => "未知"
        };

        /// <summary>
        /// 计划状态名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string StatusName => Status switch
        {
            1 => "启用",
            2 => "暂停",
            3 => "停用",
            _ => "未知"
        };

        /// <summary>
        /// 是否需要提醒（基于下次保养日期和提前提醒天数）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool NeedsReminder
        {
            get
            {
                if (!NextMaintenanceDate.HasValue || Status != 1)
                    return false;
                
                var reminderDate = NextMaintenanceDate.Value.AddDays(-ReminderDays);
                return DateTime.Now >= reminderDate;
            }
        }

        /// <summary>
        /// 是否已过期
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool IsOverdue
        {
            get
            {
                if (!NextMaintenanceDate.HasValue || Status != 1)
                    return false;
                
                return DateTime.Now > NextMaintenanceDate.Value;
            }
        }

        /// <summary>
        /// 距离下次保养的天数
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int? DaysUntilNextMaintenance
        {
            get
            {
                if (!NextMaintenanceDate.HasValue)
                    return null;

                return (int)(NextMaintenanceDate.Value - DateTime.Now).TotalDays;
            }
        }

        /// <summary>
        /// 备注（别名）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string? Notes { get => Remark; set => Remark = value; }
    }
}
