using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using CoreHub.Shared.Models.Dto;
using CoreHub.Shared.Utils;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 报修单服务实现
    /// </summary>
    public class RepairOrderService : IRepairOrderService
    {
        private readonly DatabaseContext _dbContext;
        private readonly ILogger<RepairOrderService> _logger;
        private readonly IRoleDepartmentAssignmentServiceV2 _roleDepartmentAssignmentService;
        private readonly IJobTypeService _jobTypeService;
        private readonly IUserManagementService _userManagementService;
        private readonly IRepairOrderPartRequestService _partRequestService;

        public RepairOrderService(
            DatabaseContext dbContext,
            ILogger<RepairOrderService> logger,
            IRoleDepartmentAssignmentServiceV2 roleDepartmentAssignmentService,
            IJobTypeService jobTypeService,
            IUserManagementService userManagementService,
            IRepairOrderPartRequestService partRequestService)
        {
            _dbContext = dbContext;
            _logger = logger;
            _roleDepartmentAssignmentService = roleDepartmentAssignmentService;
            _jobTypeService = jobTypeService;
            _userManagementService = userManagementService;
            _partRequestService = partRequestService;
        }

        public async Task<List<RepairOrder>> GetAllRepairOrdersAsync()
        {
            try
            {
                return await _dbContext.Db.Queryable<RepairOrder>()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有报修单失败");
                throw;
            }
        }

        public async Task<RepairOrder?> GetRepairOrderByIdAsync(int id)
        {
            try
            {
                return await _dbContext.Db.Queryable<RepairOrder>()
                    .Where(ro => ro.Id == id)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据ID获取报修单失败: {id}", id);
                throw;
            }
        }

        public async Task<RepairOrder?> GetRepairOrderByNumberAsync(string orderNumber)
        {
            try
            {
                return await _dbContext.Db.Queryable<RepairOrder>()
                    .Where(ro => ro.OrderNumber == orderNumber)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据报修单号获取报修单失败: {orderNumber}", orderNumber);
                throw;
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage, string? OrderNumber)> CreateRepairOrderAsync(RepairOrder repairOrder)
        {
            try
            {
                // 生成报修单号
                repairOrder.OrderNumber = await GenerateOrderNumberAsync();

                // 验证关联数据
                var validationResult = await ValidateRepairOrderRelationsAsync(repairOrder);
                if (!validationResult.IsSuccess)
                {
                    return (false, validationResult.ErrorMessage, null);
                }

                repairOrder.CreatedAt = DateTime.Now;
                repairOrder.ReportedAt = DateTime.Now; // 设置报修时间
                repairOrder.Status = 1; // 待处理

                var result = await _dbContext.Db.Insertable(repairOrder).ExecuteReturnIdentityAsync();

                // 同步更新设备状态
                await SyncEquipmentStatusAsync(repairOrder.EquipmentId);

                _logger.LogInformation("创建报修单成功: {orderNumber}", repairOrder.OrderNumber);
                return (true, string.Empty, repairOrder.OrderNumber);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建报修单失败");
                return (false, $"创建报修单失败: {ex.Message}", null);
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateRepairOrderAsync(RepairOrder repairOrder)
        {
            try
            {
                // 验证关联数据
                var validationResult = await ValidateRepairOrderRelationsAsync(repairOrder);
                if (!validationResult.IsSuccess)
                {
                    return validationResult;
                }

                repairOrder.UpdatedAt = DateTime.Now;
                var result = await _dbContext.Db.Updateable(repairOrder).ExecuteCommandAsync();
                
                if (result > 0)
                {
                    _logger.LogInformation("更新报修单成功: {orderNumber}", repairOrder.OrderNumber);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "报修单不存在或未发生变更");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新报修单失败: {id}", repairOrder.Id);
                return (false, $"更新报修单失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> DeleteRepairOrderAsync(int id)
        {
            try
            {
                // 检查报修单状态，只有待处理状态的才能删除
                var repairOrder = await GetRepairOrderByIdAsync(id);
                if (repairOrder == null)
                {
                    return (false, "报修单不存在");
                }

                if (repairOrder.Status != 1) // 不是待处理状态
                {
                    return (false, "只有待处理状态的报修单才能删除");
                }

                // 删除关联的附件记录
                await _dbContext.Db.Deleteable<RepairOrderAttachment>()
                    .Where(roa => roa.RepairOrderId == id)
                    .ExecuteCommandAsync();

                var result = await _dbContext.Db.Deleteable<RepairOrder>()
                    .Where(ro => ro.Id == id)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("删除报修单成功: {id}", id);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "报修单不存在");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除报修单失败: {id}", id);
                return (false, $"删除报修单失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> CancelRepairOrderAsync(int id, string cancelReason)
        {
            try
            {
                var repairOrder = await GetRepairOrderByIdAsync(id);
                if (repairOrder == null)
                {
                    return (false, "报修单不存在");
                }

                // 只有待处理和处理中的报修单才能作废
                if (repairOrder.Status != 1 && repairOrder.Status != 2)
                {
                    return (false, "只有待处理或处理中的报修单才能作废");
                }

                repairOrder.Status = 4; // 已作废
                repairOrder.CancelledAt = DateTime.Now;
                repairOrder.CancelReason = cancelReason;
                repairOrder.UpdatedAt = DateTime.Now;

                var result = await _dbContext.Db.Updateable(repairOrder)
                    .UpdateColumns(ro => new { ro.Status, ro.CancelledAt, ro.CancelReason, ro.UpdatedAt })
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    // 同步更新设备状态
                    await SyncEquipmentStatusAsync(repairOrder.EquipmentId);

                    _logger.LogInformation("作废报修单成功: {orderNumber}", repairOrder.OrderNumber);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "操作失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "作废报修单失败: {id}", id);
                return (false, $"作废报修单失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateRepairOrderStatusAsync(int id, int status, int? assignedTo = null)
        {
            try
            {
                var repairOrder = await GetRepairOrderByIdAsync(id);
                if (repairOrder == null)
                {
                    return (false, "报修单不存在");
                }

                repairOrder.Status = status;
                repairOrder.UpdatedAt = DateTime.Now;

                if (assignedTo.HasValue)
                {
                    repairOrder.AssignedTo = assignedTo.Value;
                    repairOrder.AssignedAt = DateTime.Now;
                }

                if (status == 2 && repairOrder.StartedAt == null) // 开始处理
                {
                    repairOrder.StartedAt = DateTime.Now;
                }
                else if (status == 3 && repairOrder.CompletedAt == null) // 完成
                {
                    repairOrder.CompletedAt = DateTime.Now;
                }
                else if (status == 8) // 待确认状态，清空完成时间，等待确认
                {
                    repairOrder.CompletedAt = null;
                }

                var result = await _dbContext.Db.Updateable(repairOrder).ExecuteCommandAsync();

                if (result > 0)
                {
                    // 同步更新设备状态
                    await SyncEquipmentStatusAsync(repairOrder.EquipmentId);

                    _logger.LogInformation("更新报修单状态成功: {orderNumber} -> {status}", repairOrder.OrderNumber, status);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "操作失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新报修单状态失败: {id}", id);
                return (false, $"更新报修单状态失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> AssignRepairOrderAsync(int id, int assignedTo)
        {
            try
            {
                var repairOrder = await GetRepairOrderByIdAsync(id);
                if (repairOrder == null)
                {
                    return (false, "报修单不存在");
                }

                repairOrder.AssignedTo = assignedTo;
                repairOrder.AssignedAt = DateTime.Now;
                repairOrder.UpdatedAt = DateTime.Now;

                // 如果是待处理状态，自动变更为处理中
                if (repairOrder.Status == RepairOrderStatusHelper.Pending)
                {
                    repairOrder.Status = RepairOrderStatusHelper.InProgress;
                    repairOrder.StartedAt = DateTime.Now;
                }

                var result = await _dbContext.Db.Updateable(repairOrder)
                    .UpdateColumns(ro => new { ro.AssignedTo, ro.AssignedAt, ro.Status, ro.StartedAt, ro.UpdatedAt })
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    // 同步更新设备状态
                    await SyncEquipmentStatusAsync(repairOrder.EquipmentId);

                    _logger.LogInformation("指派报修单成功: {orderNumber} -> {assignedTo}", repairOrder.OrderNumber, assignedTo);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "操作失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "指派报修单失败: {id}", id);
                return (false, $"指派报修单失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> StartRepairAsync(int id)
        {
            try
            {
                return await UpdateRepairOrderStatusAsync(id, 2); // 处理中
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "开始维修失败: {id}", id);
                return (false, $"开始维修失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> CompleteRepairAsync(int id, string repairDescription, decimal? repairCost = null, string? partsUsed = null, string? testResult = null)
        {
            try
            {
                var repairOrder = await GetRepairOrderByIdAsync(id);
                if (repairOrder == null)
                {
                    return (false, "报修单不存在");
                }

                repairOrder.Status = 3; // 已完成
                repairOrder.CompletedAt = DateTime.Now;
                repairOrder.RepairDescription = repairDescription;
                repairOrder.RepairCost = repairCost;
                repairOrder.PartsUsed = partsUsed;
                repairOrder.TestResult = testResult;
                repairOrder.UpdatedAt = DateTime.Now;

                var result = await _dbContext.Db.Updateable(repairOrder).ExecuteCommandAsync();

                if (result > 0)
                {
                    // 同步更新设备状态
                    await SyncEquipmentStatusAsync(repairOrder.EquipmentId);

                    _logger.LogInformation("完成维修成功: {orderNumber}", repairOrder.OrderNumber);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "操作失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "完成维修失败: {id}", id);
                return (false, $"完成维修失败: {ex.Message}");
            }
        }

        public async Task<List<RepairOrder>> GetUserRepairOrdersAsync(int userId)
        {
            try
            {
                return await _dbContext.Db.Queryable<RepairOrder>()
                    .Where(ro => ro.ReporterId == userId)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户报修单失败: {userId}", userId);
                throw;
            }
        }

        public async Task<List<RepairOrder>> GetDepartmentRepairOrdersAsync(int departmentId)
        {
            try
            {
                return await _dbContext.Db.Queryable<RepairOrder>()
                    .Where(ro => ro.MaintenanceDepartmentId == departmentId)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取部门报修单失败: {departmentId}", departmentId);
                throw;
            }
        }

        public async Task<List<RepairOrder>> GetEquipmentRepairOrdersAsync(int equipmentId)
        {
            try
            {
                return await _dbContext.Db.Queryable<RepairOrder>()
                    .Where(ro => ro.EquipmentId == equipmentId)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备报修单失败: {equipmentId}", equipmentId);
                throw;
            }
        }

        public async Task<List<RepairOrderDetailDto>> GetRepairOrderDetailsAsync()
        {
            try
            {
                // 简化查询，避免复杂的联表操作
                var repairOrders = await _dbContext.Db.Queryable<RepairOrder>().ToListAsync();
                var equipment = await _dbContext.Db.Queryable<Equipment>().ToListAsync();
                var equipmentModels = await _dbContext.Db.Queryable<EquipmentModel>().ToListAsync();
                var equipmentParts = await _dbContext.Db.Queryable<EquipmentPart>().ToListAsync();
                var departments = await _dbContext.Db.Queryable<Department>().ToListAsync();
                var locations = await _dbContext.Db.Queryable<Location>().ToListAsync();
                var users = await _dbContext.Db.Queryable<User>().ToListAsync();
                var attachmentCounts = await _dbContext.Db.Queryable<RepairOrderAttachment>()
                    .GroupBy(roa => roa.RepairOrderId)
                    .Select(g => new { RepairOrderId = g.RepairOrderId, Count = SqlFunc.AggregateCount(g.Id) })
                    .ToListAsync();

                var result = new List<RepairOrderDetailDto>();
                foreach (var ro in repairOrders)
                {
                    var eq = equipment.FirstOrDefault(e => e.Id == ro.EquipmentId);
                    var eqModel = equipmentModels.FirstOrDefault(em => em.Id == eq?.ModelId);
                    var eqDept = departments.FirstOrDefault(d => d.Id == eq?.DepartmentId);
                    var eqLocation = locations.FirstOrDefault(l => l.Id == eq?.LocationId);
                    var faultPart = equipmentParts.FirstOrDefault(ep => ep.Id == ro.FaultPartId);
                    var faultPartParent = faultPart?.ParentId.HasValue == true ?
                        equipmentParts.FirstOrDefault(ep => ep.Id == faultPart.ParentId.Value) : null;
                    var reporter = users.FirstOrDefault(u => u.Id == ro.ReporterId);
                    var maintDept = departments.FirstOrDefault(d => d.Id == ro.MaintenanceDepartmentId);
                    var assignedUser = users.FirstOrDefault(u => u.Id == ro.AssignedTo);
                    var attachmentCount = attachmentCounts.FirstOrDefault(ac => ac.RepairOrderId == ro.Id)?.Count ?? 0;

                    result.Add(new RepairOrderDetailDto
                    {
                        Id = ro.Id,
                        OrderNumber = ro.OrderNumber,
                        EquipmentId = ro.EquipmentId,
                        EquipmentCode = eq?.Code ?? "",
                        EquipmentName = eq?.Name ?? "",
                        EquipmentDepartmentName = eqDept?.Name ?? "",
                        EquipmentModelName = eqModel?.Name ?? "",
                        EquipmentLocationName = eqLocation?.Name ?? "",
                        FaultPartId = ro.FaultPartId,
                        FaultPartCode = faultPart?.Code,
                        FaultPartName = faultPart?.Name,
                        FaultPartFullName = faultPartParent != null ?
                            $"{faultPartParent.Name} - {faultPart?.Name}" : faultPart?.Name,
                        ReporterId = ro.ReporterId,
                        ReporterName = reporter?.DisplayName ?? "",
                        FaultDescription = ro.FaultDescription,
                        UrgencyLevel = ro.UrgencyLevel,
                        UrgencyLevelName = ro.UrgencyLevelName,
                        MaintenanceDepartmentId = ro.MaintenanceDepartmentId,
                        MaintenanceDepartmentName = maintDept?.Name ?? "",
                        Status = ro.Status,
                        StatusName = ro.StatusName,
                        AssignedTo = ro.AssignedTo,
                        AssignedToName = assignedUser?.DisplayName,
                        ReportedAt = ro.ReportedAt,
                        AssignedAt = ro.AssignedAt,
                        StartedAt = ro.StartedAt,
                        CompletedAt = ro.CompletedAt,
                        CancelledAt = ro.CancelledAt,
                        CancelReason = ro.CancelReason,
                        RepairDescription = ro.RepairDescription,
                        RepairCost = ro.RepairCost,
                        PartsUsed = ro.PartsUsed,
                        TestResult = ro.TestResult,
                        ReporterRating = ro.ReporterRating,
                        ReporterComment = ro.ReporterComment,
                        CreatedAt = ro.CreatedAt,
                        UpdatedAt = ro.UpdatedAt,
                        Remark = ro.Remark,
                        AttachmentCount = attachmentCount
                    });
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取报修单详细信息失败");
                throw;
            }
        }

        public async Task<List<RepairOrderDetailDto>> SearchRepairOrdersAsync(RepairOrderSearchDto searchDto)
        {
            try
            {
                var allRepairOrders = await GetRepairOrderDetailsAsync();
                var filtered = allRepairOrders.AsEnumerable();

                // 按搜索文本过滤
                if (!string.IsNullOrWhiteSpace(searchDto.SearchText))
                {
                    filtered = filtered.Where(ro =>
                        ro.OrderNumber.Contains(searchDto.SearchText, StringComparison.OrdinalIgnoreCase) ||
                        ro.EquipmentName.Contains(searchDto.SearchText, StringComparison.OrdinalIgnoreCase) ||
                        ro.EquipmentCode.Contains(searchDto.SearchText, StringComparison.OrdinalIgnoreCase) ||
                        ro.FaultDescription.Contains(searchDto.SearchText, StringComparison.OrdinalIgnoreCase) ||
                        ro.ReporterName.Contains(searchDto.SearchText, StringComparison.OrdinalIgnoreCase) ||
                        (!string.IsNullOrEmpty(ro.FaultPartName) && ro.FaultPartName.Contains(searchDto.SearchText, StringComparison.OrdinalIgnoreCase)) ||
                        (!string.IsNullOrEmpty(ro.FaultPartCode) && ro.FaultPartCode.Contains(searchDto.SearchText, StringComparison.OrdinalIgnoreCase))
                    );
                }

                // 其他过滤条件...
                if (searchDto.EquipmentId.HasValue)
                    filtered = filtered.Where(ro => ro.EquipmentId == searchDto.EquipmentId.Value);
                if (searchDto.FaultPartId.HasValue)
                    filtered = filtered.Where(ro => ro.FaultPartId == searchDto.FaultPartId.Value);
                if (searchDto.ReporterId.HasValue)
                    filtered = filtered.Where(ro => ro.ReporterId == searchDto.ReporterId.Value);
                if (searchDto.Status.HasValue)
                    filtered = filtered.Where(ro => ro.Status == searchDto.Status.Value);
                if (searchDto.UrgencyLevel.HasValue)
                    filtered = filtered.Where(ro => ro.UrgencyLevel == searchDto.UrgencyLevel.Value);

                return filtered.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索报修单失败");
                throw;
            }
        }

        public async Task<RepairOrderStatisticsDto> GetRepairOrderStatisticsAsync()
        {
            try
            {
                var allRepairOrders = await GetRepairOrderDetailsAsync();
                var today = DateTime.Today;

                var statistics = new RepairOrderStatisticsDto
                {
                    TotalCount = allRepairOrders.Count,
                    PendingCount = allRepairOrders.Count(ro => ro.Status == RepairOrderStatusHelper.Pending),
                    InProgressCount = allRepairOrders.Count(ro => ro.Status == RepairOrderStatusHelper.InProgress),
                    CompletedCount = allRepairOrders.Count(ro => ro.Status == RepairOrderStatusHelper.Completed),
                    CancelledCount = allRepairOrders.Count(ro => ro.Status == RepairOrderStatusHelper.Cancelled),
                    ClosedCount = allRepairOrders.Count(ro => ro.Status == RepairOrderStatusHelper.Closed),
                    PendingConfirmationCount = allRepairOrders.Count(ro => ro.Status == RepairOrderStatusHelper.PendingConfirmation),
                    UrgentCount = allRepairOrders.Count(ro => ro.UrgencyLevel == 1),
                    OverdueCount = allRepairOrders.Count(ro => ro.Status == RepairOrderStatusHelper.InProgress && ro.CreatedAt < today.AddDays(-3))
                };

                // 计算平均维修时间
                var completedOrders = allRepairOrders.Where(ro => ro.Status == RepairOrderStatusHelper.Completed && ro.StartedAt.HasValue && ro.CompletedAt.HasValue);
                if (completedOrders.Any())
                {
                    statistics.AverageRepairTime = (decimal)completedOrders.Average(ro => (ro.CompletedAt!.Value - ro.StartedAt!.Value).TotalHours);
                }

                // 按部门统计
                statistics.DepartmentCounts = allRepairOrders
                    .GroupBy(ro => ro.MaintenanceDepartmentName)
                    .ToDictionary(g => g.Key, g => g.Count());

                // 按紧急程度统计
                statistics.UrgencyLevelCounts = allRepairOrders
                    .GroupBy(ro => ro.UrgencyLevelName)
                    .ToDictionary(g => g.Key, g => g.Count());

                // 按故障部位统计
                statistics.FaultPartCounts = allRepairOrders
                    .Where(ro => !string.IsNullOrEmpty(ro.FaultPartName))
                    .GroupBy(ro => ro.FaultPartName!)
                    .ToDictionary(g => g.Key, g => g.Count());

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取报修单统计信息失败");
                throw;
            }
        }

        public async Task<string> GenerateOrderNumberAsync()
        {
            try
            {
                var today = DateTime.Today.ToString("yyyyMMdd");
                var prefix = $"RO{today}";

                var maxNumber = await _dbContext.Db.Queryable<RepairOrder>()
                    .Where(ro => ro.OrderNumber.StartsWith(prefix))
                    .Select(ro => ro.OrderNumber)
                    .ToListAsync();

                var maxSeq = 0;
                if (maxNumber.Any())
                {
                    maxSeq = maxNumber.Select(n =>
                    {
                        var seqStr = n.Substring(prefix.Length);
                        return int.TryParse(seqStr, out var seq) ? seq : 0;
                    }).Max();
                }

                return $"{prefix}{(maxSeq + 1):D3}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成报修单号失败");
                throw;
            }
        }

        private async Task<(bool IsSuccess, string ErrorMessage)> ValidateRepairOrderRelationsAsync(RepairOrder repairOrder)
        {
            try
            {
                // 检查设备是否存在
                var equipment = await _dbContext.Db.Queryable<Equipment>()
                    .Where(e => e.Id == repairOrder.EquipmentId && e.IsEnabled)
                    .FirstAsync();
                if (equipment == null)
                {
                    return (false, "设备不存在或已禁用");
                }

                // 检查故障部位是否存在且属于指定设备
                if (repairOrder.FaultPartId.HasValue)
                {
                    var faultPart = await _dbContext.Db.Queryable<EquipmentPart>()
                        .Where(ep => ep.Id == repairOrder.FaultPartId.Value && ep.IsEnabled)
                        .FirstAsync();
                    if (faultPart == null)
                    {
                        return (false, "故障部位不存在或已禁用");
                    }
                    if (faultPart.EquipmentId != repairOrder.EquipmentId)
                    {
                        return (false, "故障部位不属于指定设备");
                    }
                }

                // 检查维修部门是否存在
                var department = await _dbContext.Db.Queryable<Department>()
                    .Where(d => d.Id == repairOrder.MaintenanceDepartmentId && d.IsEnabled)
                    .FirstAsync();
                if (department == null)
                {
                    return (false, "维修部门不存在或已禁用");
                }

                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证报修单关联数据失败");
                return (false, $"验证失败: {ex.Message}");
            }
        }

        #region 维修工作流方法

        /// <summary>
        /// 检查用户是否有系统管理权限
        /// </summary>
        private async Task<bool> HasSystemAdminPermission(int userId)
        {
            try
            {
                var userPermissions = await _userManagementService.GetUserAllPermissionsAsync(userId);
                return userPermissions.Any(p => p.Code == "System.Admin");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[RepairOrderService] 检查用户 {userId} 系统管理权限时出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查用户是否有维修管理权限
        /// </summary>
        private async Task<bool> HasMaintenanceManagementPermission(int userId)
        {
            try
            {
                // 获取用户的所有权限
                var userPermissions = await _userManagementService.GetUserAllPermissionsAsync(userId);

                // 检查是否有维修管理相关权限（使用数据库中实际存在的权限代码）
                var managementPermissions = new[]
                {
                    "RepairOrderManagement.View",   // 报修单管理查看
                    "RepairOrderManagement.Edit",   // 报修单管理编辑
                    "RepairOrderManagement.Delete"  // 报修单管理删除
                };

                var hasPermission = userPermissions.Any(p => managementPermissions.Contains(p.Code));

                // 如果没有直接的管理权限，检查是否为维修部门管理人员
                if (!hasPermission)
                {
                    hasPermission = await IsMaintenanceDepartmentManager(userId);
                }

                Console.WriteLine($"[RepairOrderService] 用户 {userId} 维修管理权限检查: {hasPermission}");
                Console.WriteLine($"[RepairOrderService] 用户权限: [{string.Join(", ", userPermissions.Select(p => p.Code))}]");

                return hasPermission;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[RepairOrderService] 检查用户 {userId} 维修管理权限时出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查用户是否为部门管理人员
        /// </summary>
        private async Task<bool> IsDepartmentManager(int userId)
        {
            try
            {
                // 检查用户是否有管理工种
                var userJobTypes = await _jobTypeService.GetUserJobTypesWithDetailsAsync(userId);
                var hasManagerJobType = userJobTypes.Any(ujt =>
                    ujt.JobType.Category == JobCategories.Management && ujt.UserJobType.IsEnabled);

                return hasManagerJobType;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[RepairOrderService] 检查用户 {userId} 部门管理权限时出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取部门及其所有子部门的ID列表
        /// </summary>
        private async Task<List<int>> GetDepartmentAndChildrenIds(List<int> departmentIds)
        {
            try
            {
                var allDepartmentIds = new HashSet<int>(departmentIds);

                // 获取所有部门数据
                var allDepartments = await _dbContext.Db.Queryable<Department>()
                    .Where(d => d.IsEnabled)
                    .ToListAsync();

                // 递归获取所有子部门
                foreach (var deptId in departmentIds)
                {
                    GetChildDepartmentIds(deptId, allDepartments, allDepartmentIds);
                }

                return allDepartmentIds.ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[RepairOrderService] 获取部门及子部门ID时出错: {ex.Message}");
                return departmentIds;
            }
        }

        /// <summary>
        /// 递归获取子部门ID
        /// </summary>
        private void GetChildDepartmentIds(int parentId, List<Department> allDepartments, HashSet<int> result)
        {
            var children = allDepartments.Where(d => d.ParentId == parentId).ToList();
            foreach (var child in children)
            {
                if (result.Add(child.Id)) // 如果成功添加（之前不存在），则继续递归
                {
                    GetChildDepartmentIds(child.Id, allDepartments, result);
                }
            }
        }

        /// <summary>
        /// 检查用户是否为指定维修部门的管理人员（支持上级部门管理）
        /// </summary>
        private async Task<bool> IsMaintenanceManagerForDepartments(int userId, List<int> departmentIds)
        {
            try
            {
                // 检查用户是否有管理工种
                var userJobTypes = await _jobTypeService.GetUserJobTypesWithDetailsAsync(userId);
                var hasManagerJobType = userJobTypes.Any(ujt =>
                    ujt.JobType.Category == JobCategories.Management && ujt.UserJobType.IsEnabled);

                if (!hasManagerJobType)
                {
                    return false;
                }

                // 获取用户可管理的部门
                var userAccessibleDepartments = await _roleDepartmentAssignmentService.GetUserAccessibleDepartmentsAsync(userId);
                var userDepartmentIds = userAccessibleDepartments.Select(d => d.Id).ToList();

                // 获取用户管理部门的所有子部门（支持上级部门管理下级部门）
                var userManagedDepartmentIds = await GetDepartmentAndChildrenIds(userDepartmentIds);

                // 检查是否有交集（用户管理的部门（包括子部门）中包含维修部门）
                var hasManagementAccess = departmentIds.Any(deptId => userManagedDepartmentIds.Contains(deptId));

                Console.WriteLine($"[RepairOrderService] 用户 {userId} 维修管理检查:");
                Console.WriteLine($"[RepairOrderService] - 有管理工种: {hasManagerJobType}");
                Console.WriteLine($"[RepairOrderService] - 用户直接管理部门: [{string.Join(", ", userDepartmentIds)}]");
                Console.WriteLine($"[RepairOrderService] - 用户管理部门（含子部门）: [{string.Join(", ", userManagedDepartmentIds)}]");
                Console.WriteLine($"[RepairOrderService] - 维修部门: [{string.Join(", ", departmentIds)}]");
                Console.WriteLine($"[RepairOrderService] - 有管理权限: {hasManagementAccess}");

                return hasManagementAccess;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[RepairOrderService] 检查用户 {userId} 维修部门管理权限时出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查用户是否为维修部门管理人员
        /// </summary>
        private async Task<bool> IsMaintenanceDepartmentManager(int userId)
        {
            try
            {
                // 检查用户是否有管理工种
                var userJobTypes = await _jobTypeService.GetUserJobTypesWithDetailsAsync(userId);
                var hasManagerJobType = userJobTypes.Any(ujt =>
                    ujt.JobType.Category == JobCategories.Management && ujt.UserJobType.IsEnabled);

                if (!hasManagerJobType)
                {
                    return false;
                }

                // 检查用户是否可以访问维修部门
                var accessibleDepartments = await _roleDepartmentAssignmentService.GetUserAccessibleDepartmentsAsync(userId);
                var maintenanceDepartments = accessibleDepartments.Where(d =>
                    d.DepartmentType?.Code == "Maintenance").ToList();

                Console.WriteLine($"[RepairOrderService] 用户 {userId} 维修部门管理检查:");
                Console.WriteLine($"[RepairOrderService] - 是否有管理工种: {hasManagerJobType}");
                Console.WriteLine($"[RepairOrderService] - 可访问的维修部门数量: {maintenanceDepartments.Count}");

                return maintenanceDepartments.Any();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[RepairOrderService] 检查用户 {userId} 维修部门管理权限时出错: {ex.Message}");
                return false;
            }
        }

        public async Task<List<RepairOrderDetailDto>> GetMaintenanceVisibleRepairOrdersAsync(int userId)
        {
            try
            {
                // 先检查用户是否为系统管理员
                var hasSystemAdminPermission = await HasSystemAdminPermission(userId);

                // 检查用户是否为维修人员（通过工种判断）
                var maintenanceJobTypes = await _jobTypeService.GetJobTypesByCategoryAsync(JobCategories.Maintenance);
                var userJobTypes = await _jobTypeService.GetUserJobTypesWithDetailsAsync(userId);
                var isMaintenancePersonnel = userJobTypes.Any(ujt => maintenanceJobTypes.Any(mjt => mjt.Id == ujt.UserJobType.JobTypeId));

                // 检查用户是否为部门管理人员（有管理工种）
                var hasManagerJobType = userJobTypes.Any(ujt =>
                    ujt.JobType.Category == JobCategories.Management && ujt.UserJobType.IsEnabled);

                Console.WriteLine($"[RepairOrderService] 用户 {userId} 权限检查:");
                Console.WriteLine($"[RepairOrderService] - 是否为系统管理员: {hasSystemAdminPermission}");
                Console.WriteLine($"[RepairOrderService] - 是否为维修人员: {isMaintenancePersonnel}");
                Console.WriteLine($"[RepairOrderService] - 是否为部门管理人员: {hasManagerJobType}");

                // 如果用户既不是系统管理员、也不是维修人员、也不是部门管理人员，则无权访问维修仪表板
                if (!hasSystemAdminPermission && !isMaintenancePersonnel && !hasManagerJobType)
                {
                    Console.WriteLine($"[RepairOrderService] 用户 {userId} 既不是系统管理员、也不是维修人员、也不是部门管理人员，无权访问维修仪表板");
                    return new List<RepairOrderDetailDto>();
                }

                // 构建基础查询
                var query = _dbContext.Db.Queryable<RepairOrder>()
                    .LeftJoin<Equipment>((ro, e) => ro.EquipmentId == e.Id)
                    .LeftJoin<EquipmentPart>((ro, e, ep) => ro.FaultPartId == ep.Id)
                    .LeftJoin<EquipmentPart>((ro, e, ep, pep) => ep.ParentId == pep.Id)
                    .LeftJoin<Department>((ro, e, ep, pep, ed) => e.DepartmentId == ed.Id)
                    .LeftJoin<EquipmentModel>((ro, e, ep, pep, ed, em) => e.ModelId == em.Id)
                    .LeftJoin<Location>((ro, e, ep, pep, ed, em, el) => e.LocationId == el.Id)
                    .LeftJoin<Department>((ro, e, ep, pep, ed, em, el, md) => ro.MaintenanceDepartmentId == md.Id)
                    .LeftJoin<User>((ro, e, ep, pep, ed, em, el, md, u) => ro.ReporterId == u.Id)
                    .LeftJoin<User>((ro, e, ep, pep, ed, em, el, md, u, au) => ro.AssignedTo == au.Id);

                // 根据用户角色应用不同的过滤条件
                if (hasSystemAdminPermission)
                {
                    Console.WriteLine($"[RepairOrderService] 用户 {userId} 是系统管理员，可以管理所有报修单");
                    // 系统管理员可以看到所有报修单，不需要额外过滤
                }
                else if (hasManagerJobType)
                {
                    // 部门管理人员（有管理工种）
                    var accessibleDepartments = await _roleDepartmentAssignmentService.GetUserAccessibleDepartmentsAsync(userId);
                    var accessibleDepartmentIds = accessibleDepartments.Select(d => d.Id).ToList();

                    if (accessibleDepartmentIds.Any())
                    {
                        // 获取用户管理部门的所有子部门（支持上级部门管理下级部门）
                        var managedDepartmentIds = await GetDepartmentAndChildrenIds(accessibleDepartmentIds);

                        Console.WriteLine($"[RepairOrderService] 用户 {userId} 是部门管理人员，可以管理本部门及子部门的报修单");
                        Console.WriteLine($"[RepairOrderService] - 直接管理部门: [{string.Join(", ", accessibleDepartmentIds)}]");
                        Console.WriteLine($"[RepairOrderService] - 管理部门（含子部门）: [{string.Join(", ", managedDepartmentIds)}]");

                        if (isMaintenancePersonnel)
                        {
                            // 既是维修人员又是管理人员：可以看到分配给自己的 + 管理部门的所有报修单
                            query = query.Where((ro, e, ep, pep, ed, em, el, md, u, au) =>
                                ro.AssignedTo == userId || // 分配给自己的
                                managedDepartmentIds.Contains(e.DepartmentId) || // 设备所属部门在管理范围内（含子部门）
                                managedDepartmentIds.Contains(ro.MaintenanceDepartmentId) // 维修部门在管理范围内（含子部门）
                            );
                        }
                        else
                        {
                            // 只是部门管理人员（非维修人员）：只能看到管理部门的报修单
                            query = query.Where((ro, e, ep, pep, ed, em, el, md, u, au) =>
                                managedDepartmentIds.Contains(e.DepartmentId) || // 设备所属部门在管理范围内（含子部门）
                                managedDepartmentIds.Contains(ro.MaintenanceDepartmentId) // 维修部门在管理范围内（含子部门）
                            );
                        }
                    }
                    else
                    {
                        Console.WriteLine($"[RepairOrderService] 用户 {userId} 有管理工种但无部门管理权限，只能管理分配给自己的报修单");
                        query = query.Where((ro, e, ep, pep, ed, em, el, md, u, au) => ro.AssignedTo == userId);
                    }
                }
                else if (isMaintenancePersonnel)
                {
                    Console.WriteLine($"[RepairOrderService] 用户 {userId} 是普通维修员，只能管理分配给自己的报修单");
                    query = query.Where((ro, e, ep, pep, ed, em, el, md, u, au) => ro.AssignedTo == userId);
                }

                var result = await query.Select((ro, e, ep, pep, ed, em, el, md, u, au) => new RepairOrderDetailDto
                {
                    Id = ro.Id,
                    OrderNumber = ro.OrderNumber,
                    EquipmentId = ro.EquipmentId,
                    EquipmentCode = e.Code,
                    EquipmentName = e.Name,
                    EquipmentDepartmentName = ed.Name,
                    EquipmentModelName = em.Name,
                    EquipmentLocationName = el.Name,
                    FaultPartId = ro.FaultPartId,
                    FaultPartCode = ep.Code,
                    FaultPartName = ep.Name,
                    FaultPartFullName = SqlFunc.IIF(pep.Name != null, SqlFunc.MergeString(pep.Name, " - ", ep.Name), ep.Name),
                    ReporterId = ro.ReporterId,
                    ReporterName = u.DisplayName,
                    FaultDescription = ro.FaultDescription,
                    UrgencyLevel = ro.UrgencyLevel,
                    UrgencyLevelName = SqlFunc.IIF(ro.UrgencyLevel == 1, "紧急",
                                     SqlFunc.IIF(ro.UrgencyLevel == 2, "高",
                                     SqlFunc.IIF(ro.UrgencyLevel == 3, "中",
                                     SqlFunc.IIF(ro.UrgencyLevel == 4, "低", "未知")))),
                    MaintenanceDepartmentId = ro.MaintenanceDepartmentId,
                    MaintenanceDepartmentName = md.Name,
                    Status = ro.Status,
                    StatusName = SqlFunc.IIF(ro.Status == RepairOrderStatusHelper.Pending, "待处理",
                                SqlFunc.IIF(ro.Status == RepairOrderStatusHelper.InProgress, "处理中",
                                SqlFunc.IIF(ro.Status == RepairOrderStatusHelper.Completed, "已完成",
                                SqlFunc.IIF(ro.Status == RepairOrderStatusHelper.Cancelled, "已作废",
                                SqlFunc.IIF(ro.Status == RepairOrderStatusHelper.Closed, "已关闭",
                                SqlFunc.IIF(ro.Status == RepairOrderStatusHelper.PendingConfirmation, "待确认", "未知")))))),
                    AssignedTo = ro.AssignedTo,
                    AssignedToName = au.DisplayName,
                    ReportedAt = ro.ReportedAt,
                    AssignedAt = ro.AssignedAt,
                    StartedAt = ro.StartedAt,
                    CompletedAt = ro.CompletedAt,
                    CancelledAt = ro.CancelledAt,
                    CancelReason = ro.CancelReason,
                    RepairDescription = ro.RepairDescription,
                    RepairCost = ro.RepairCost,
                    PartsUsed = ro.PartsUsed,
                    TestResult = ro.TestResult,
                    ReporterRating = ro.ReporterRating,
                    ReporterComment = ro.ReporterComment,
                    CreatedAt = ro.CreatedAt
                }).ToListAsync();

                Console.WriteLine($"[RepairOrderService] 用户 {userId} 查询到 {result.Count} 个报修单");
                foreach (var item in result)
                {
                    Console.WriteLine($"[RepairOrderService] - 报修单: {item.OrderNumber}, 状态: {item.Status}, 分配给: {item.AssignedTo}, 分配给姓名: {item.AssignedToName}");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取维修人员可见报修单失败: {userId}", userId);
                return new List<RepairOrderDetailDto>();
            }
        }

        public async Task<List<RepairOrderDetailDto>> GetRepairOrdersByMaintenanceDepartmentAsync(int departmentId)
        {
            try
            {
                return await _dbContext.Db.Queryable<RepairOrder>()
                    .LeftJoin<Equipment>((ro, e) => ro.EquipmentId == e.Id)
                    .LeftJoin<Department>((ro, e, ed) => e.DepartmentId == ed.Id)
                    .LeftJoin<EquipmentModel>((ro, e, ed, em) => e.ModelId == em.Id)
                    .LeftJoin<Location>((ro, e, ed, em, el) => e.LocationId == el.Id)
                    .LeftJoin<Department>((ro, e, ed, em, el, md) => ro.MaintenanceDepartmentId == md.Id)
                    .LeftJoin<User>((ro, e, ed, em, el, md, u) => ro.ReporterId == u.Id)
                    .LeftJoin<User>((ro, e, ed, em, el, md, u, au) => ro.AssignedTo == au.Id)
                    .Where((ro, e, ed, em, el, md, u, au) => ro.MaintenanceDepartmentId == departmentId)
                    .Select((ro, e, ed, em, el, md, u, au) => new RepairOrderDetailDto
                    {
                        Id = ro.Id,
                        OrderNumber = ro.OrderNumber,
                        EquipmentId = ro.EquipmentId,
                        EquipmentCode = e.Code,
                        EquipmentName = e.Name,
                        EquipmentDepartmentName = ed.Name,
                        EquipmentModelName = em.Name,
                        EquipmentLocationName = el.Name,
                        ReporterId = ro.ReporterId,
                        ReporterName = u.DisplayName,
                        FaultDescription = ro.FaultDescription,
                        UrgencyLevel = ro.UrgencyLevel,
                        UrgencyLevelName = SqlFunc.IIF(ro.UrgencyLevel == 1, "紧急",
                                         SqlFunc.IIF(ro.UrgencyLevel == 2, "高",
                                         SqlFunc.IIF(ro.UrgencyLevel == 3, "中",
                                         SqlFunc.IIF(ro.UrgencyLevel == 4, "低", "未知")))),
                        MaintenanceDepartmentId = ro.MaintenanceDepartmentId,
                        MaintenanceDepartmentName = md.Name,
                        Status = ro.Status,
                        StatusName = SqlFunc.IIF(ro.Status == RepairOrderStatusHelper.Pending, "待处理",
                                    SqlFunc.IIF(ro.Status == RepairOrderStatusHelper.InProgress, "处理中",
                                    SqlFunc.IIF(ro.Status == RepairOrderStatusHelper.Completed, "已完成",
                                    SqlFunc.IIF(ro.Status == RepairOrderStatusHelper.Cancelled, "已作废",
                                    SqlFunc.IIF(ro.Status == RepairOrderStatusHelper.Closed, "已关闭",
                                    SqlFunc.IIF(ro.Status == RepairOrderStatusHelper.PendingConfirmation, "待确认", "未知")))))),
                        AssignedTo = ro.AssignedTo,
                        AssignedToName = au.DisplayName,
                        ReportedAt = ro.ReportedAt,
                        AssignedAt = ro.AssignedAt,
                        StartedAt = ro.StartedAt,
                        CompletedAt = ro.CompletedAt,
                        CancelledAt = ro.CancelledAt,
                        CancelReason = ro.CancelReason,
                        RepairDescription = ro.RepairDescription,
                        RepairCost = ro.RepairCost,
                        PartsUsed = ro.PartsUsed,
                        TestResult = ro.TestResult,
                        ReporterRating = ro.ReporterRating,
                        ReporterComment = ro.ReporterComment,
                        CreatedAt = ro.CreatedAt
                    }).ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取维修部门报修单失败: {departmentId}", departmentId);
                return new List<RepairOrderDetailDto>();
            }
        }

        public async Task<List<RepairOrderDetailDto>> GetRepairOrdersByAssignedTechnicianAsync(int technicianId)
        {
            try
            {
                return await _dbContext.Db.Queryable<RepairOrder>()
                    .LeftJoin<Equipment>((ro, e) => ro.EquipmentId == e.Id)
                    .LeftJoin<Department>((ro, e, ed) => e.DepartmentId == ed.Id)
                    .LeftJoin<EquipmentModel>((ro, e, ed, em) => e.ModelId == em.Id)
                    .LeftJoin<Location>((ro, e, ed, em, el) => e.LocationId == el.Id)
                    .LeftJoin<Department>((ro, e, ed, em, el, md) => ro.MaintenanceDepartmentId == md.Id)
                    .LeftJoin<User>((ro, e, ed, em, el, md, u) => ro.ReporterId == u.Id)
                    .LeftJoin<User>((ro, e, ed, em, el, md, u, au) => ro.AssignedTo == au.Id)
                    .Where((ro, e, ed, em, el, md, u, au) => ro.AssignedTo == technicianId)
                    .Select((ro, e, ed, em, el, md, u, au) => new RepairOrderDetailDto
                    {
                        Id = ro.Id,
                        OrderNumber = ro.OrderNumber,
                        EquipmentId = ro.EquipmentId,
                        EquipmentCode = e.Code,
                        EquipmentName = e.Name,
                        EquipmentDepartmentName = ed.Name,
                        EquipmentModelName = em.Name,
                        EquipmentLocationName = el.Name,
                        ReporterId = ro.ReporterId,
                        ReporterName = u.DisplayName,
                        FaultDescription = ro.FaultDescription,
                        UrgencyLevel = ro.UrgencyLevel,
                        UrgencyLevelName = SqlFunc.IIF(ro.UrgencyLevel == 1, "紧急",
                                         SqlFunc.IIF(ro.UrgencyLevel == 2, "高",
                                         SqlFunc.IIF(ro.UrgencyLevel == 3, "中",
                                         SqlFunc.IIF(ro.UrgencyLevel == 4, "低", "未知")))),
                        MaintenanceDepartmentId = ro.MaintenanceDepartmentId,
                        MaintenanceDepartmentName = md.Name,
                        Status = ro.Status,
                        StatusName = SqlFunc.IIF(ro.Status == 1, "待处理",
                                    SqlFunc.IIF(ro.Status == RepairOrderStatusHelper.InProgress, "处理中",
                                    SqlFunc.IIF(ro.Status == RepairOrderStatusHelper.Completed, "已完成",
                                    SqlFunc.IIF(ro.Status == RepairOrderStatusHelper.Cancelled, "已作废",
                                    SqlFunc.IIF(ro.Status == RepairOrderStatusHelper.Closed, "已关闭",
                                    SqlFunc.IIF(ro.Status == RepairOrderStatusHelper.PendingConfirmation, "待确认", "未知")))))),
                        AssignedTo = ro.AssignedTo,
                        AssignedToName = au.DisplayName,
                        ReportedAt = ro.ReportedAt,
                        AssignedAt = ro.AssignedAt,
                        StartedAt = ro.StartedAt,
                        CompletedAt = ro.CompletedAt,
                        CancelledAt = ro.CancelledAt,
                        CancelReason = ro.CancelReason,
                        RepairDescription = ro.RepairDescription,
                        RepairCost = ro.RepairCost,
                        PartsUsed = ro.PartsUsed,
                        TestResult = ro.TestResult,
                        ReporterRating = ro.ReporterRating,
                        ReporterComment = ro.ReporterComment,
                        CreatedAt = ro.CreatedAt
                    }).ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取技术员分配的报修单失败: {technicianId}", technicianId);
                return new List<RepairOrderDetailDto>();
            }
        }

        public async Task<List<RepairOrderDetailDto>> GetPendingAssignmentRepairOrdersAsync(int maintenanceDepartmentId)
        {
            try
            {
                return await _dbContext.Db.Queryable<RepairOrder>()
                    .LeftJoin<Equipment>((ro, e) => ro.EquipmentId == e.Id)
                    .LeftJoin<Department>((ro, e, ed) => e.DepartmentId == ed.Id)
                    .LeftJoin<EquipmentModel>((ro, e, ed, em) => e.ModelId == em.Id)
                    .LeftJoin<Location>((ro, e, ed, em, el) => e.LocationId == el.Id)
                    .LeftJoin<Department>((ro, e, ed, em, el, md) => ro.MaintenanceDepartmentId == md.Id)
                    .LeftJoin<User>((ro, e, ed, em, el, md, u) => ro.ReporterId == u.Id)
                    .Where((ro, e, ed, em, el, md, u) =>
                        ro.MaintenanceDepartmentId == maintenanceDepartmentId &&
                        ro.AssignedTo == null &&
                        ro.Status == RepairOrderStatusHelper.Pending) // 待处理状态
                    .Select((ro, e, ed, em, el, md, u) => new RepairOrderDetailDto
                    {
                        Id = ro.Id,
                        OrderNumber = ro.OrderNumber,
                        EquipmentId = ro.EquipmentId,
                        EquipmentCode = e.Code,
                        EquipmentName = e.Name,
                        EquipmentDepartmentName = ed.Name,
                        EquipmentModelName = em.Name,
                        EquipmentLocationName = el.Name,
                        ReporterId = ro.ReporterId,
                        ReporterName = u.DisplayName,
                        FaultDescription = ro.FaultDescription,
                        UrgencyLevel = ro.UrgencyLevel,
                        MaintenanceDepartmentId = ro.MaintenanceDepartmentId,
                        MaintenanceDepartmentName = md.Name,
                        Status = ro.Status,
                        AssignedTo = ro.AssignedTo,
                        AssignedToName = null,
                        ReportedAt = ro.ReportedAt,
                        AssignedAt = ro.AssignedAt,
                        StartedAt = ro.StartedAt,
                        CompletedAt = ro.CompletedAt,
                        CancelledAt = ro.CancelledAt,
                        CancelReason = ro.CancelReason,
                        RepairDescription = ro.RepairDescription,
                        RepairCost = ro.RepairCost,
                        PartsUsed = ro.PartsUsed,
                        TestResult = ro.TestResult,
                        ReporterRating = ro.ReporterRating,
                        ReporterComment = ro.ReporterComment,
                        CreatedAt = ro.CreatedAt
                    }).ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取待分配报修单失败: {maintenanceDepartmentId}", maintenanceDepartmentId);
                return new List<RepairOrderDetailDto>();
            }
        }

        public async Task<bool> CanUserManageRepairOrderAsync(int userId, int repairOrderId)
        {
            try
            {
                var repairOrder = await GetRepairOrderByIdAsync(repairOrderId);
                if (repairOrder == null) return false;

                // 检查是否为系统管理员
                var hasSystemAdminPermission = await HasSystemAdminPermission(userId);
                if (hasSystemAdminPermission) return true;

                // 检查用户是否有管理工种
                var userJobTypes = await _jobTypeService.GetUserJobTypesWithDetailsAsync(userId);
                var hasManagerJobType = userJobTypes.Any(ujt =>
                    ujt.JobType.Category == JobCategories.Management && ujt.UserJobType.IsEnabled);

                if (!hasManagerJobType) return false;

                // 获取用户可管理的部门（包括子部门）
                var accessibleDepartments = await _roleDepartmentAssignmentService.GetUserAccessibleDepartmentsAsync(userId);
                var accessibleDepartmentIds = accessibleDepartments.Select(d => d.Id).ToList();

                if (!accessibleDepartmentIds.Any()) return false;

                // 获取用户管理部门的所有子部门（支持上级部门管理下级部门）
                var managedDepartmentIds = await GetDepartmentAndChildrenIds(accessibleDepartmentIds);

                // 检查报修单的维修部门或设备所属部门是否在管理范围内
                var equipment = await _dbContext.Db.Queryable<Equipment>()
                    .Where(e => e.Id == repairOrder.EquipmentId)
                    .FirstAsync();

                var canManageByMaintenanceDept = managedDepartmentIds.Contains(repairOrder.MaintenanceDepartmentId);
                var canManageByEquipmentDept = equipment != null && managedDepartmentIds.Contains(equipment.DepartmentId);

                return canManageByMaintenanceDept || canManageByEquipmentDept;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查用户管理权限失败: {userId}, {repairOrderId}", userId, repairOrderId);
                return false;
            }
        }

        public async Task<bool> CanUserAssignRepairOrderAsync(int userId, int repairOrderId)
        {
            try
            {
                // 管理权限包含分配权限
                return await CanUserManageRepairOrderAsync(userId, repairOrderId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查用户分配权限失败: {userId}, {repairOrderId}", userId, repairOrderId);
                return false;
            }
        }

        public async Task<bool> CanUserProcessRepairOrderAsync(int userId, int repairOrderId)
        {
            try
            {
                var repairOrder = await GetRepairOrderByIdAsync(repairOrderId);
                if (repairOrder == null) return false;

                // 检查是否分配给当前用户
                if (repairOrder.AssignedTo == userId) return true;

                // 检查是否有管理权限
                return await CanUserManageRepairOrderAsync(userId, repairOrderId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查用户处理权限失败: {userId}, {repairOrderId}", userId, repairOrderId);
                return false;
            }
        }

        public async Task<List<User>> GetAvailableTechniciansForRepairOrderAsync(int repairOrderId)
        {
            try
            {
                var repairOrder = await GetRepairOrderByIdAsync(repairOrderId);
                if (repairOrder == null) return new List<User>();

                // 直接从维修部门下面具有维修工种的用户中筛选
                return await _jobTypeService.GetMaintenanceUsersByDepartmentAsync(repairOrder.MaintenanceDepartmentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可用技术员失败: {repairOrderId}", repairOrderId);
                return new List<User>();
            }
        }

        #endregion

        #region 设备状态同步

        /// <summary>
        /// 根据报修单状态同步更新设备状态
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        private async Task SyncEquipmentStatusAsync(int equipmentId)
        {
            try
            {
                // 获取该设备的所有活跃报修单
                var activeRepairOrders = await _dbContext.Db.Queryable<RepairOrder>()
                    .Where(ro => ro.EquipmentId == equipmentId &&
                                (ro.Status == RepairOrderStatusHelper.Pending ||
                                 ro.Status == RepairOrderStatusHelper.InProgress ||
                                 ro.Status == RepairOrderStatusHelper.PendingConfirmation)) // 待处理、处理中、待确认
                    .ToListAsync();

                // 获取设备信息
                var equipment = await _dbContext.Db.Queryable<Equipment>()
                    .Where(e => e.Id == equipmentId)
                    .FirstAsync();

                if (equipment == null) return;

                int newEquipmentStatus;

                if (activeRepairOrders.Any())
                {
                    // 有活跃的报修单，设备状态为维修中
                    newEquipmentStatus = 2; // 维修中
                }
                else
                {
                    // 没有活跃的报修单，设备状态为正常
                    newEquipmentStatus = 1; // 正常
                }

                // 只有状态发生变化时才更新
                if (equipment.Status != newEquipmentStatus)
                {
                    equipment.Status = newEquipmentStatus;
                    equipment.UpdatedAt = DateTime.Now;

                    await _dbContext.Db.Updateable(equipment)
                        .UpdateColumns(e => new { e.Status, e.UpdatedAt })
                        .ExecuteCommandAsync();

                    _logger.LogInformation("同步设备状态成功: 设备 {equipmentId} 状态更新为 {status}",
                        equipmentId, newEquipmentStatus == 1 ? "正常" : "维修中");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "同步设备状态失败: {equipmentId}", equipmentId);
            }
        }

        #endregion

        #region 零件申请相关

        public async Task<(bool IsSuccess, string ErrorMessage, int? RepairOrderId, string? OrderNumber)> CreateRepairOrderWithPartRequestsAsync(
            RepairOrder repairOrder, List<PartReplacementRequestDto> partRequests)
        {
            try
            {
                // 开始事务
                _dbContext.Db.Ado.BeginTran();

                // 1. 创建维修单
                var createResult = await CreateRepairOrderAsync(repairOrder);
                if (!createResult.IsSuccess)
                {
                    _dbContext.Db.Ado.RollbackTran();
                    return (false, createResult.ErrorMessage, null, null);
                }

                // 获取创建的维修单ID
                var createdRepairOrder = await GetRepairOrderByNumberAsync(createResult.OrderNumber!);
                if (createdRepairOrder == null)
                {
                    _dbContext.Db.Ado.RollbackTran();
                    return (false, "创建的维修单未找到", null, null);
                }

                // 2. 如果有零件申请，批量创建
                if (partRequests.Any())
                {
                    var partRequestResult = await _partRequestService.CreatePartRequestsBatchAsync(
                        createdRepairOrder.Id, partRequests);

                    if (!partRequestResult.IsSuccess)
                    {
                        _dbContext.Db.Ado.RollbackTran();
                        return (false, $"创建零件申请失败: {partRequestResult.ErrorMessage}", null, null);
                    }

                    _logger.LogInformation("创建维修单和零件申请成功: RepairOrderId={RepairOrderId}, PartRequestCount={PartRequestCount}",
                        createdRepairOrder.Id, partRequests.Count);
                }

                // 提交事务
                _dbContext.Db.Ado.CommitTran();

                return (true, string.Empty, createdRepairOrder.Id, createdRepairOrder.OrderNumber);
            }
            catch (Exception ex)
            {
                _dbContext.Db.Ado.RollbackTran();
                _logger.LogError(ex, "创建维修单和零件申请失败");
                return (false, $"创建维修单和零件申请失败: {ex.Message}", null, null);
            }
        }

        #endregion
    }
}
