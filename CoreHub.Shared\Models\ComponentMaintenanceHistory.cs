namespace CoreHub.Shared.Models
{
    /// <summary>
    /// 部件保养历史记录模型
    /// </summary>
    public class ComponentMaintenanceHistory
    {
        /// <summary>
        /// 部件ID
        /// </summary>
        public int ComponentId { get; set; }

        /// <summary>
        /// 部件名称
        /// </summary>
        public string ComponentName { get; set; } = string.Empty;

        /// <summary>
        /// 部件编码
        /// </summary>
        public string ComponentCode { get; set; } = string.Empty;

        /// <summary>
        /// 部件型号
        /// </summary>
        public string ComponentModel { get; set; } = string.Empty;

        /// <summary>
        /// 部件规格
        /// </summary>
        public string ComponentSpecifications { get; set; } = string.Empty;

        /// <summary>
        /// 部件类型
        /// </summary>
        public int ComponentType { get; set; }

        /// <summary>
        /// 部件类型名称
        /// </summary>
        public string ComponentTypeName { get; set; } = string.Empty;

        /// <summary>
        /// 是否关键部件
        /// </summary>
        public bool IsCritical { get; set; }

        /// <summary>
        /// 统计时间段开始日期
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 统计时间段结束日期
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 总保养次数
        /// </summary>
        public int TotalMaintenanceCount { get; set; }

        /// <summary>
        /// 预防性保养次数
        /// </summary>
        public int PreventiveMaintenanceCount { get; set; }

        /// <summary>
        /// 纠正性保养次数
        /// </summary>
        public int CorrectiveMaintenanceCount { get; set; }

        /// <summary>
        /// 紧急保养次数
        /// </summary>
        public int EmergencyMaintenanceCount { get; set; }

        /// <summary>
        /// 总保养耗时（分钟）
        /// </summary>
        public int TotalMaintenanceDuration { get; set; }

        /// <summary>
        /// 平均保养耗时（分钟）
        /// </summary>
        public double AverageMaintenanceDuration => TotalMaintenanceCount > 0 ? (double)TotalMaintenanceDuration / TotalMaintenanceCount : 0;

        /// <summary>
        /// 总保养成本
        /// </summary>
        public decimal TotalMaintenanceCost { get; set; }

        /// <summary>
        /// 平均保养成本
        /// </summary>
        public decimal AverageMaintenanceCost => TotalMaintenanceCount > 0 ? TotalMaintenanceCost / TotalMaintenanceCount : 0;

        /// <summary>
        /// 保养成功次数
        /// </summary>
        public int SuccessfulMaintenanceCount { get; set; }

        /// <summary>
        /// 保养成功率
        /// </summary>
        public double MaintenanceSuccessRate => TotalMaintenanceCount > 0 ? (double)SuccessfulMaintenanceCount / TotalMaintenanceCount * 100 : 0;

        /// <summary>
        /// 按时完成次数
        /// </summary>
        public int OnTimeCompletionCount { get; set; }

        /// <summary>
        /// 按时完成率
        /// </summary>
        public double OnTimeCompletionRate => TotalMaintenanceCount > 0 ? (double)OnTimeCompletionCount / TotalMaintenanceCount * 100 : 0;

        /// <summary>
        /// 首次保养日期
        /// </summary>
        public DateTime? FirstMaintenanceDate { get; set; }

        /// <summary>
        /// 最后保养日期
        /// </summary>
        public DateTime? LastMaintenanceDate { get; set; }

        /// <summary>
        /// 下次计划保养日期
        /// </summary>
        public DateTime? NextScheduledMaintenanceDate { get; set; }

        /// <summary>
        /// 平均保养间隔（天）
        /// </summary>
        public double AverageMaintenanceInterval { get; set; }

        /// <summary>
        /// 最短保养间隔（天）
        /// </summary>
        public int MinMaintenanceInterval { get; set; }

        /// <summary>
        /// 最长保养间隔（天）
        /// </summary>
        public int MaxMaintenanceInterval { get; set; }

        /// <summary>
        /// 主要保养人员
        /// </summary>
        public List<MaintenancePersonnelSummary> MaintenancePersonnel { get; set; } = new();

        /// <summary>
        /// 保养记录详情
        /// </summary>
        public List<MaintenanceRecordSummary> MaintenanceRecords { get; set; } = new();

        /// <summary>
        /// 保养类型统计
        /// </summary>
        public List<MaintenanceTypeStatistics> MaintenanceTypeStats { get; set; } = new();

        /// <summary>
        /// 月度保养统计
        /// </summary>
        public List<MonthlyMaintenanceStatistics> MonthlyStats { get; set; } = new();

        /// <summary>
        /// 保养趋势分析
        /// </summary>
        public string TrendAnalysis { get; set; } = string.Empty;

        /// <summary>
        /// 保养建议
        /// </summary>
        public List<string> MaintenanceRecommendations { get; set; } = new();
    }

    /// <summary>
    /// 保养人员汇总
    /// </summary>
    public class MaintenancePersonnelSummary
    {
        /// <summary>
        /// 保养人员姓名
        /// </summary>
        public string PersonName { get; set; } = string.Empty;

        /// <summary>
        /// 保养次数
        /// </summary>
        public int MaintenanceCount { get; set; }

        /// <summary>
        /// 平均耗时（分钟）
        /// </summary>
        public double AverageDuration { get; set; }

        /// <summary>
        /// 成功率
        /// </summary>
        public double SuccessRate { get; set; }
    }

    /// <summary>
    /// 保养记录汇总
    /// </summary>
    public class MaintenanceRecordSummary
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public int RecordId { get; set; }

        /// <summary>
        /// 保养日期
        /// </summary>
        public DateTime MaintenanceDate { get; set; }

        /// <summary>
        /// 保养类型
        /// </summary>
        public int MaintenanceType { get; set; }

        /// <summary>
        /// 保养类型名称
        /// </summary>
        public string MaintenanceTypeName { get; set; } = string.Empty;

        /// <summary>
        /// 保养人员
        /// </summary>
        public string MaintenancePersonName { get; set; } = string.Empty;

        /// <summary>
        /// 保养耗时（分钟）
        /// </summary>
        public int ActualDuration { get; set; }

        /// <summary>
        /// 保养成本
        /// </summary>
        public decimal ActualCost { get; set; }

        /// <summary>
        /// 保养结果
        /// </summary>
        public int MaintenanceResult { get; set; }

        /// <summary>
        /// 保养结果名称
        /// </summary>
        public string MaintenanceResultName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 保养类型统计
    /// </summary>
    public class MaintenanceTypeStatistics
    {
        /// <summary>
        /// 保养类型
        /// </summary>
        public int MaintenanceType { get; set; }

        /// <summary>
        /// 保养类型名称
        /// </summary>
        public string MaintenanceTypeName { get; set; } = string.Empty;

        /// <summary>
        /// 保养次数
        /// </summary>
        public int Count { get; set; }

        /// <summary>
        /// 占比
        /// </summary>
        public double Percentage { get; set; }

        /// <summary>
        /// 平均耗时（分钟）
        /// </summary>
        public double AverageDuration { get; set; }

        /// <summary>
        /// 平均成本
        /// </summary>
        public decimal AverageCost { get; set; }
    }

    /// <summary>
    /// 月度保养统计
    /// </summary>
    public class MonthlyMaintenanceStatistics
    {
        /// <summary>
        /// 年份
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 月份
        /// </summary>
        public int Month { get; set; }

        /// <summary>
        /// 保养次数
        /// </summary>
        public int MaintenanceCount { get; set; }

        /// <summary>
        /// 总耗时（分钟）
        /// </summary>
        public int TotalDuration { get; set; }

        /// <summary>
        /// 总成本
        /// </summary>
        public decimal TotalCost { get; set; }

        /// <summary>
        /// 成功次数
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 成功率
        /// </summary>
        public double SuccessRate => MaintenanceCount > 0 ? (double)SuccessCount / MaintenanceCount * 100 : 0;
    }
}
