@page "/debug-database"
@using CoreHub.Shared.Models
@using CoreHub.Shared.Services
@inject DatabaseContext DbContext

<PageTitle>数据库调试</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large">
    <MudPaper Elevation="2" Class="pa-4">
        <MudText Typo="Typo.h4" Color="Color.Primary" Class="mb-4">数据库调试信息</MudText>
        
        @if (isLoading)
        {
            <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
        }
        else
        {
            <MudStack Spacing="3">
                <MudPaper Elevation="1" Class="pa-3">
                    <MudText Typo="Typo.h6" Class="mb-2">所有角色:</MudText>
                    <MudTable Items="@roles" Dense="true" Hover="true" Striped="true">
                        <HeaderContent>
                            <MudTh>ID</MudTh>
                            <MudTh>代码</MudTh>
                            <MudTh>名称</MudTh>
                            <MudTh>排序</MudTh>
                            <MudTh>启用</MudTh>
                        </HeaderContent>
                        <RowTemplate>
                            <MudTd DataLabel="ID">@context.Id</MudTd>
                            <MudTd DataLabel="代码">@context.Code</MudTd>
                            <MudTd DataLabel="名称">@context.Name</MudTd>
                            <MudTd DataLabel="排序">@context.SortOrder</MudTd>
                            <MudTd DataLabel="启用">@context.IsEnabled</MudTd>
                        </RowTemplate>
                    </MudTable>
                </MudPaper>

                <MudPaper Elevation="1" Class="pa-3">
                    <MudText Typo="Typo.h6" Class="mb-2">admin 用户角色分配:</MudText>
                    <MudTable Items="@userRoles" Dense="true" Hover="true" Striped="true">
                        <HeaderContent>
                            <MudTh>用户名</MudTh>
                            <MudTh>角色代码</MudTh>
                            <MudTh>角色名称</MudTh>
                            <MudTh>启用</MudTh>
                            <MudTh>过期时间</MudTh>
                        </HeaderContent>
                        <RowTemplate>
                            <MudTd DataLabel="用户名">@context.Username</MudTd>
                            <MudTd DataLabel="角色代码">@context.RoleCode</MudTd>
                            <MudTd DataLabel="角色名称">@context.RoleName</MudTd>
                            <MudTd DataLabel="启用">@context.IsEnabled</MudTd>
                            <MudTd DataLabel="过期时间">@context.ExpiresAt?.ToString("yyyy-MM-dd HH:mm:ss")</MudTd>
                        </RowTemplate>
                    </MudTable>
                </MudPaper>

                <MudPaper Elevation="1" Class="pa-3">
                    <MudText Typo="Typo.h6" Class="mb-2">调试信息:</MudText>
                    <MudText>角色数量: @roles.Count</MudText>
                    <MudText>用户角色分配数量: @userRoles.Count</MudText>
                    @if (errorMessage != null)
                    {
                        <MudAlert Severity="Severity.Error">@errorMessage</MudAlert>
                    }
                </MudPaper>
            </MudStack>
        }
    </MudPaper>
</MudContainer>

@code {
    private bool isLoading = true;
    private List<Role> roles = new();
    private List<UserRoleInfo> userRoles = new();
    private string? errorMessage;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            // 获取所有角色
            roles = await DbContext.Db.Queryable<Role>()
                .OrderBy(r => r.SortOrder)
                .ToListAsync();

            // 获取 admin 用户的角色分配
            userRoles = await DbContext.Db.Queryable<User>()
                .LeftJoin<UserRole>((u, ur) => u.Id == ur.UserId)
                .LeftJoin<Role>((u, ur, r) => ur.RoleId == r.Id)
                .Where((u, ur, r) => u.Username == "admin")
                .Select((u, ur, r) => new UserRoleInfo
                {
                    Username = u.Username,
                    RoleCode = r.Code,
                    RoleName = r.Name,
                    IsEnabled = ur.IsEnabled,
                    ExpiresAt = ur.ExpiresAt
                })
                .ToListAsync();
        }
        catch (Exception ex)
        {
            errorMessage = $"查询失败: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }

    private class UserRoleInfo
    {
        public string Username { get; set; } = "";
        public string RoleCode { get; set; } = "";
        public string RoleName { get; set; } = "";
        public bool IsEnabled { get; set; }
        public DateTime? ExpiresAt { get; set; }
    }
}
