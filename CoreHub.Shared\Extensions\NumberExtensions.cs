using System;

namespace CoreHub.Shared.Extensions
{
    /// <summary>
    /// 数字扩展方法
    /// </summary>
    public static class NumberExtensions
    {
        /// <summary>
        /// 格式化为货币字符串
        /// </summary>
        public static string ToCurrencyString(this decimal value)
        {
            return $"¥{value:N2}";
        }

        /// <summary>
        /// 格式化为货币字符串（可空类型）
        /// </summary>
        public static string ToCurrencyString(this decimal? value)
        {
            return value?.ToCurrencyString() ?? "¥0.00";
        }

        /// <summary>
        /// 格式化为百分比字符串
        /// </summary>
        public static string ToPercentageString(this double value, int decimals = 1)
        {
            return $"{value.ToString($"F{decimals}")}%";
        }

        /// <summary>
        /// 格式化为百分比字符串（可空类型）
        /// </summary>
        public static string ToPercentageString(this double? value, int decimals = 1)
        {
            return value?.ToPercentageString(decimals) ?? "0.0%";
        }

        /// <summary>
        /// 格式化为百分比字符串
        /// </summary>
        public static string ToPercentageString(this decimal value, int decimals = 1)
        {
            return $"{value.ToString($"F{decimals}")}%";
        }

        /// <summary>
        /// 格式化为百分比字符串（可空类型）
        /// </summary>
        public static string ToPercentageString(this decimal? value, int decimals = 1)
        {
            return value?.ToPercentageString(decimals) ?? "0.0%";
        }

        /// <summary>
        /// 格式化为友好的数字显示（带千分位分隔符）
        /// </summary>
        public static string ToFriendlyString(this int value)
        {
            return value.ToString("N0");
        }

        /// <summary>
        /// 格式化为友好的数字显示（可空类型）
        /// </summary>
        public static string ToFriendlyString(this int? value)
        {
            return value?.ToFriendlyString() ?? "0";
        }

        /// <summary>
        /// 格式化为友好的数字显示（带千分位分隔符）
        /// </summary>
        public static string ToFriendlyString(this decimal value, int decimals = 2)
        {
            return value.ToString($"N{decimals}");
        }

        /// <summary>
        /// 格式化为友好的数字显示（可空类型）
        /// </summary>
        public static string ToFriendlyString(this decimal? value, int decimals = 2)
        {
            return value?.ToFriendlyString(decimals) ?? "0.00";
        }

        /// <summary>
        /// 格式化为时长字符串（分钟转换为小时分钟）
        /// </summary>
        public static string ToDurationString(this int minutes)
        {
            if (minutes < 60)
                return $"{minutes}分钟";

            var hours = minutes / 60;
            var remainingMinutes = minutes % 60;

            if (remainingMinutes == 0)
                return $"{hours}小时";

            return $"{hours}小时{remainingMinutes}分钟";
        }

        /// <summary>
        /// 格式化为时长字符串（可空类型）
        /// </summary>
        public static string ToDurationString(this int? minutes)
        {
            return minutes?.ToDurationString() ?? "未设置";
        }

        /// <summary>
        /// 格式化为文件大小字符串
        /// </summary>
        public static string ToFileSizeString(this long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        /// <summary>
        /// 安全除法（避免除零错误）
        /// </summary>
        public static decimal SafeDivide(this decimal dividend, decimal divisor, decimal defaultValue = 0)
        {
            return divisor == 0 ? defaultValue : dividend / divisor;
        }

        /// <summary>
        /// 安全除法（避免除零错误）
        /// </summary>
        public static double SafeDivide(this double dividend, double divisor, double defaultValue = 0)
        {
            return Math.Abs(divisor) < double.Epsilon ? defaultValue : dividend / divisor;
        }
    }
}
