@using CoreHub.Shared.Models
@using CoreHub.Shared.Services
@using CoreHub.Shared.Extensions
@inject IMaintenanceRecordService MaintenanceRecordService

<MudDialog>
    <DialogContent>
        <MudContainer MaxWidth="MaxWidth.ExtraLarge">
            @if (Statistics != null)
            {
                <MudGrid>
                    <MudItem xs="12">
                        <MudText Typo="Typo.h5" Class="mb-4">保养统计报告</MudText>
                        <MudDivider />
                    </MudItem>
                    
                    <MudItem xs="12" md="3">
                        <MudCard>
                            <MudCardContent>
                                <MudText Typo="Typo.h6">总记录数</MudText>
                                <MudText Typo="Typo.h4" Color="Color.Primary">@Statistics.TotalRecords</MudText>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    
                    <MudItem xs="12" md="3">
                        <MudCard>
                            <MudCardContent>
                                <MudText Typo="Typo.h6">已完成</MudText>
                                <MudText Typo="Typo.h4" Color="Color.Success">@Statistics.CompletedRecords</MudText>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    
                    <MudItem xs="12" md="3">
                        <MudCard>
                            <MudCardContent>
                                <MudText Typo="Typo.h6">进行中</MudText>
                                <MudText Typo="Typo.h4" Color="Color.Warning">@Statistics.InProgressRecords</MudText>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    
                    <MudItem xs="12" md="3">
                        <MudCard>
                            <MudCardContent>
                                <MudText Typo="Typo.h6">逾期</MudText>
                                <MudText Typo="Typo.h4" Color="Color.Error">@Statistics.OverdueRecords</MudText>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudCard>
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">成本统计</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                <MudGrid>
                                    <MudItem xs="12">
                                        <MudText><strong>总成本：</strong>@Statistics.TotalCost.ToString("F2")</MudText>
                                    </MudItem>
                                    <MudItem xs="12">
                                        <MudText><strong>平均成本：</strong>@Statistics.AverageCost.ToString("F2")</MudText>
                                    </MudItem>
                                    <MudItem xs="12">
                                        <MudText><strong>最高成本：</strong>@Statistics.MaxCost.ToString("F2")</MudText>
                                    </MudItem>
                                    <MudItem xs="12">
                                        <MudText><strong>最低成本：</strong>@Statistics.MinCost.ToString("F2")</MudText>
                                    </MudItem>
                                </MudGrid>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudCard>
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">时间统计</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                <MudGrid>
                                    <MudItem xs="12">
                                        <MudText><strong>总耗时：</strong>@Statistics.TotalDuration 分钟</MudText>
                                    </MudItem>
                                    <MudItem xs="12">
                                        <MudText><strong>平均耗时：</strong>@Statistics.AverageDuration 分钟</MudText>
                                    </MudItem>
                                    <MudItem xs="12">
                                        <MudText><strong>最长耗时：</strong>@Statistics.MaxDuration 分钟</MudText>
                                    </MudItem>
                                    <MudItem xs="12">
                                        <MudText><strong>最短耗时：</strong>@Statistics.MinDuration 分钟</MudText>
                                    </MudItem>
                                </MudGrid>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    
                    <MudItem xs="12">
                        <MudCard>
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">效率指标</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                <MudGrid>
                                    <MudItem xs="12" md="4">
                                        <MudText><strong>完成率：</strong>@Statistics.CompletionRate.ToString("F1")%</MudText>
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudText><strong>按时完成率：</strong>@Statistics.OnTimeRate.ToString("F1")%</MudText>
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudText><strong>成功率：</strong>@Statistics.SuccessRate.ToString("F1")%</MudText>
                                    </MudItem>
                                </MudGrid>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                </MudGrid>
            }
            else
            {
                <MudText>暂无统计数据</MudText>
            }
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">关闭</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public MaintenanceRecordStatistics? Statistics { get; set; }

    void Cancel() => MudDialog.Cancel();
}
