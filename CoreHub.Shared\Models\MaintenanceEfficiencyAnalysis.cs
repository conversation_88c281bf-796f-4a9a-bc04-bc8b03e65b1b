namespace CoreHub.Shared.Models
{
    /// <summary>
    /// 保养效率分析模型
    /// </summary>
    public class MaintenanceEfficiencyAnalysis
    {
        /// <summary>
        /// 分析时间段开始日期
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 分析时间段结束日期
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 总保养记录数
        /// </summary>
        public int TotalRecords { get; set; }

        /// <summary>
        /// 按时完成的记录数
        /// </summary>
        public int OnTimeRecords { get; set; }

        /// <summary>
        /// 延期完成的记录数
        /// </summary>
        public int DelayedRecords { get; set; }

        /// <summary>
        /// 平均保养耗时（分钟）
        /// </summary>
        public double AverageDuration { get; set; }

        /// <summary>
        /// 平均保养成本
        /// </summary>
        public decimal AverageCost { get; set; }

        /// <summary>
        /// 总保养成本
        /// </summary>
        public decimal TotalCost { get; set; }

        /// <summary>
        /// 按时完成率
        /// </summary>
        public double OnTimeRate => TotalRecords > 0 ? (double)OnTimeRecords / TotalRecords * 100 : 0;

        /// <summary>
        /// 延期率
        /// </summary>
        public double DelayRate => TotalRecords > 0 ? (double)DelayedRecords / TotalRecords * 100 : 0;

        /// <summary>
        /// 保养成功率
        /// </summary>
        public double SuccessRate { get; set; }

        /// <summary>
        /// 保养失败率
        /// </summary>
        public double FailureRate { get; set; }

        /// <summary>
        /// 最高效的保养人员
        /// </summary>
        public string? MostEfficientPerson { get; set; }

        /// <summary>
        /// 最高效人员的平均耗时（分钟）
        /// </summary>
        public double MostEfficientPersonAvgDuration { get; set; }

        /// <summary>
        /// 效率最低的保养人员
        /// </summary>
        public string? LeastEfficientPerson { get; set; }

        /// <summary>
        /// 效率最低人员的平均耗时（分钟）
        /// </summary>
        public double LeastEfficientPersonAvgDuration { get; set; }

        /// <summary>
        /// 保养类型效率统计
        /// </summary>
        public List<MaintenanceTypeEfficiency> TypeEfficiencies { get; set; } = new();

        /// <summary>
        /// 部件保养效率统计
        /// </summary>
        public List<ComponentMaintenanceEfficiency> ComponentEfficiencies { get; set; } = new();
    }

    /// <summary>
    /// 保养类型效率统计
    /// </summary>
    public class MaintenanceTypeEfficiency
    {
        /// <summary>
        /// 保养类型
        /// </summary>
        public int MaintenanceType { get; set; }

        /// <summary>
        /// 保养类型名称
        /// </summary>
        public string TypeName { get; set; } = string.Empty;

        /// <summary>
        /// 记录数量
        /// </summary>
        public int RecordCount { get; set; }

        /// <summary>
        /// 平均耗时（分钟）
        /// </summary>
        public double AverageDuration { get; set; }

        /// <summary>
        /// 平均成本
        /// </summary>
        public decimal AverageCost { get; set; }

        /// <summary>
        /// 成功率
        /// </summary>
        public double SuccessRate { get; set; }
    }

    /// <summary>
    /// 部件保养效率统计
    /// </summary>
    public class ComponentMaintenanceEfficiency
    {
        /// <summary>
        /// 部件ID
        /// </summary>
        public int ComponentId { get; set; }

        /// <summary>
        /// 部件名称
        /// </summary>
        public string ComponentName { get; set; } = string.Empty;

        /// <summary>
        /// 部件编码
        /// </summary>
        public string ComponentCode { get; set; } = string.Empty;

        /// <summary>
        /// 保养记录数量
        /// </summary>
        public int RecordCount { get; set; }

        /// <summary>
        /// 平均保养耗时（分钟）
        /// </summary>
        public double AverageDuration { get; set; }

        /// <summary>
        /// 平均保养成本
        /// </summary>
        public decimal AverageCost { get; set; }

        /// <summary>
        /// 保养成功率
        /// </summary>
        public double SuccessRate { get; set; }

        /// <summary>
        /// 最后保养日期
        /// </summary>
        public DateTime? LastMaintenanceDate { get; set; }

        /// <summary>
        /// 下次计划保养日期
        /// </summary>
        public DateTime? NextMaintenanceDate { get; set; }
    }
}
