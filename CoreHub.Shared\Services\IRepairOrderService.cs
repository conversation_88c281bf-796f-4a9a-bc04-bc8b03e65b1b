using CoreHub.Shared.Models.Database;
using CoreHub.Shared.Models.Dto;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 报修单服务接口
    /// </summary>
    public interface IRepairOrderService
    {
        /// <summary>
        /// 获取所有报修单
        /// </summary>
        Task<List<RepairOrder>> GetAllRepairOrdersAsync();

        /// <summary>
        /// 根据ID获取报修单
        /// </summary>
        Task<RepairOrder?> GetRepairOrderByIdAsync(int id);

        /// <summary>
        /// 根据报修单号获取报修单
        /// </summary>
        Task<RepairOrder?> GetRepairOrderByNumberAsync(string orderNumber);

        /// <summary>
        /// 创建报修单
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage, string? OrderNumber)> CreateRepairOrderAsync(RepairOrder repairOrder);

        /// <summary>
        /// 更新报修单
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateRepairOrderAsync(RepairOrder repairOrder);

        /// <summary>
        /// 删除报修单
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> DeleteRepairOrderAsync(int id);

        /// <summary>
        /// 作废报修单
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> CancelRepairOrderAsync(int id, string cancelReason);

        /// <summary>
        /// 更新报修单状态
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateRepairOrderStatusAsync(int id, int status, int? assignedTo = null);

        /// <summary>
        /// 指派报修单
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> AssignRepairOrderAsync(int id, int assignedTo);

        /// <summary>
        /// 开始维修
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> StartRepairAsync(int id);

        /// <summary>
        /// 完成维修
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> CompleteRepairAsync(int id, string repairDescription, decimal? repairCost = null, string? partsUsed = null, string? testResult = null);

        /// <summary>
        /// 获取用户的报修单
        /// </summary>
        Task<List<RepairOrder>> GetUserRepairOrdersAsync(int userId);

        /// <summary>
        /// 获取部门的报修单
        /// </summary>
        Task<List<RepairOrder>> GetDepartmentRepairOrdersAsync(int departmentId);

        /// <summary>
        /// 获取设备的报修单
        /// </summary>
        Task<List<RepairOrder>> GetEquipmentRepairOrdersAsync(int equipmentId);

        /// <summary>
        /// 获取报修单详细信息（包含关联数据）
        /// </summary>
        Task<List<RepairOrderDetailDto>> GetRepairOrderDetailsAsync();

        #region 维修工作流方法

        /// <summary>
        /// 获取维修人员可见的报修单（基于部门权限和工种）
        /// </summary>
        Task<List<RepairOrderDetailDto>> GetMaintenanceVisibleRepairOrdersAsync(int userId);

        /// <summary>
        /// 获取指定维修部门的报修单
        /// </summary>
        Task<List<RepairOrderDetailDto>> GetRepairOrdersByMaintenanceDepartmentAsync(int departmentId);

        /// <summary>
        /// 获取分配给指定技术员的报修单
        /// </summary>
        Task<List<RepairOrderDetailDto>> GetRepairOrdersByAssignedTechnicianAsync(int technicianId);

        /// <summary>
        /// 获取待分配的报修单（指定维修部门）
        /// </summary>
        Task<List<RepairOrderDetailDto>> GetPendingAssignmentRepairOrdersAsync(int maintenanceDepartmentId);

        /// <summary>
        /// 检查用户是否可以管理指定报修单
        /// </summary>
        Task<bool> CanUserManageRepairOrderAsync(int userId, int repairOrderId);

        /// <summary>
        /// 检查用户是否可以分配报修单
        /// </summary>
        Task<bool> CanUserAssignRepairOrderAsync(int userId, int repairOrderId);

        /// <summary>
        /// 检查用户是否可以处理报修单
        /// </summary>
        Task<bool> CanUserProcessRepairOrderAsync(int userId, int repairOrderId);

        /// <summary>
        /// 获取可分配给报修单的技术员列表
        /// </summary>
        Task<List<User>> GetAvailableTechniciansForRepairOrderAsync(int repairOrderId);

        #endregion

        /// <summary>
        /// 搜索报修单
        /// </summary>
        Task<List<RepairOrderDetailDto>> SearchRepairOrdersAsync(RepairOrderSearchDto searchDto);

        /// <summary>
        /// 获取报修单统计信息
        /// </summary>
        Task<RepairOrderStatisticsDto> GetRepairOrderStatisticsAsync();

        /// <summary>
        /// 生成报修单号
        /// </summary>
        Task<string> GenerateOrderNumberAsync();

        /// <summary>
        /// 创建维修单并保存零件申请
        /// </summary>
        /// <param name="repairOrder">维修单</param>
        /// <param name="partRequests">零件申请列表</param>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage, int? RepairOrderId, string? OrderNumber)> CreateRepairOrderWithPartRequestsAsync(
            RepairOrder repairOrder, List<PartReplacementRequestDto> partRequests);
    }

    /// <summary>
    /// 报修单详细信息DTO
    /// </summary>
    public class RepairOrderDetailDto
    {
        public int Id { get; set; }
        public string OrderNumber { get; set; } = string.Empty;
        public int EquipmentId { get; set; }
        public string EquipmentCode { get; set; } = string.Empty;
        public string EquipmentName { get; set; } = string.Empty;
        public string EquipmentDepartmentName { get; set; } = string.Empty;
        public string EquipmentModelName { get; set; } = string.Empty;
        public string EquipmentLocationName { get; set; } = string.Empty;
        public int? FaultPartId { get; set; }
        public string? FaultPartCode { get; set; }
        public string? FaultPartName { get; set; }
        public string? FaultPartFullName { get; set; }
        public int ReporterId { get; set; }
        public string ReporterName { get; set; } = string.Empty;
        public string FaultDescription { get; set; } = string.Empty;
        public int UrgencyLevel { get; set; }
        public string UrgencyLevelName { get; set; } = string.Empty;
        public int MaintenanceDepartmentId { get; set; }
        public string MaintenanceDepartmentName { get; set; } = string.Empty;
        public int Status { get; set; }
        public string StatusName { get; set; } = string.Empty;
        public int? AssignedTo { get; set; }
        public string? AssignedToName { get; set; }
        public DateTime ReportedAt { get; set; }
        public DateTime? AssignedAt { get; set; }
        public DateTime? StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public DateTime? CancelledAt { get; set; }
        public string? CancelReason { get; set; }
        public string? RepairDescription { get; set; }
        public decimal? RepairCost { get; set; }
        public string? PartsUsed { get; set; }
        public string? TestResult { get; set; }
        public int? ReporterRating { get; set; }
        public string? ReporterComment { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string? Remark { get; set; }
        public int AttachmentCount { get; set; }
    }

    /// <summary>
    /// 报修单搜索DTO
    /// </summary>
    public class RepairOrderSearchDto
    {
        public string? SearchText { get; set; }
        public int? EquipmentId { get; set; }
        public int? FaultPartId { get; set; }
        public int? ReporterId { get; set; }
        public int? MaintenanceDepartmentId { get; set; }
        public int? Status { get; set; }
        public int? UrgencyLevel { get; set; }
        public int? AssignedTo { get; set; }
        public DateTime? CreatedDateFrom { get; set; }
        public DateTime? CreatedDateTo { get; set; }
        public DateTime? CompletedDateFrom { get; set; }
        public DateTime? CompletedDateTo { get; set; }
    }

    /// <summary>
    /// 报修单统计信息DTO
    /// </summary>
    public class RepairOrderStatisticsDto
    {
        public int TotalCount { get; set; }
        public int PendingCount { get; set; }
        public int InProgressCount { get; set; }
        public int CompletedCount { get; set; }
        public int CancelledCount { get; set; }
        public int ClosedCount { get; set; }
        public int PendingConfirmationCount { get; set; }
        public int UrgentCount { get; set; }
        public int OverdueCount { get; set; }
        public decimal AverageRepairTime { get; set; }
        public Dictionary<string, int> DepartmentCounts { get; set; } = new();
        public Dictionary<string, int> UrgencyLevelCounts { get; set; } = new();
        public Dictionary<string, int> FaultPartCounts { get; set; } = new();
        public Dictionary<string, int> MonthlyTrends { get; set; } = new();
    }
}
