@using System.Security.Claims

<AuthorizeView>
    <Authorized>
        @if (HasRequiredPermissions(context.User))
        {
            @ChildContent
        }
        else if (NotAuthorized != null)
        {
            @NotAuthorized
        }
    </Authorized>
    <NotAuthorized>
        @if (NotAuthorized != null)
        {
            @NotAuthorized
        }
    </NotAuthorized>
</AuthorizeView>

@code {
    [Parameter] public RenderFragment? ChildContent { get; set; }
    [Parameter] public RenderFragment? NotAuthorized { get; set; }
    [Parameter] public string? RequiredPermission { get; set; }
    [Parameter] public List<string>? RequiredPermissions { get; set; }
    [Parameter] public bool RequireAllPermissions { get; set; } = false;

    private bool HasRequiredPermissions(ClaimsPrincipal user)
    {
        // 如果没有指定权限要求，则通过
        if (string.IsNullOrEmpty(RequiredPermission) &&
            (RequiredPermissions == null || !RequiredPermissions.Any()))
        {
            return true;
        }

        // 检查是否为管理员角色，管理员拥有所有权限
        var userRoles = user.Claims
            .Where(c => c.Type == "Role")
            .Select(c => c.Value)
            .ToList();

        // 调试：输出所有角色信息
        System.Diagnostics.Debug.WriteLine($"=== PermissionView 调试信息 ===");
        System.Diagnostics.Debug.WriteLine($"用户所有 Role Claims: [{string.Join(", ", userRoles)}]");
        System.Diagnostics.Debug.WriteLine($"检查权限: {RequiredPermission}");

        if (userRoles.Any(role =>
            role.Equals("Administrator", StringComparison.OrdinalIgnoreCase)))
        {
            System.Diagnostics.Debug.WriteLine("✅ 用户有 Administrator 角色，允许访问");
            return true;
        }
        else
        {
            System.Diagnostics.Debug.WriteLine("❌ 用户没有 Administrator 角色");
        }

        // 获取用户权限
        var userPermissions = user.Claims
            .Where(c => c.Type == "Permission")
            .Select(c => c.Value)
            .ToList();

        // 检查单个权限
        if (!string.IsNullOrEmpty(RequiredPermission))
        {
            return userPermissions.Contains(RequiredPermission);
        }

        // 检查多个权限
        if (RequiredPermissions != null && RequiredPermissions.Any())
        {
            if (RequireAllPermissions)
            {
                // 需要所有权限
                return RequiredPermissions.All(p => userPermissions.Contains(p));
            }
            else
            {
                // 需要任一权限
                return RequiredPermissions.Any(p => userPermissions.Contains(p));
            }
        }

        System.Diagnostics.Debug.WriteLine("❌ 权限检查失败，拒绝访问");
        System.Diagnostics.Debug.WriteLine($"=== PermissionView 调试结束 ===");
        return false;
    }
} 