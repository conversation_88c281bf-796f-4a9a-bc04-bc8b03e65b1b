namespace CoreHub.Shared.Models
{
    /// <summary>
    /// 部件使用统计模型
    /// </summary>
    public class ComponentUsageStatistics
    {
        /// <summary>
        /// 部件ID
        /// </summary>
        public int ComponentId { get; set; }

        /// <summary>
        /// 部件名称
        /// </summary>
        public string ComponentName { get; set; } = string.Empty;

        /// <summary>
        /// 部件编码
        /// </summary>
        public string ComponentCode { get; set; } = string.Empty;

        /// <summary>
        /// 总保养次数
        /// </summary>
        public int TotalMaintenanceCount { get; set; }

        /// <summary>
        /// 预防性保养次数
        /// </summary>
        public int PreventiveMaintenanceCount { get; set; }

        /// <summary>
        /// 纠正性保养次数
        /// </summary>
        public int CorrectiveMaintenanceCount { get; set; }

        /// <summary>
        /// 紧急维修次数
        /// </summary>
        public int EmergencyMaintenanceCount { get; set; }

        /// <summary>
        /// 总保养费用
        /// </summary>
        public decimal TotalMaintenanceCost { get; set; }

        /// <summary>
        /// 平均保养费用
        /// </summary>
        public decimal AverageMaintenanceCost { get; set; }

        /// <summary>
        /// 总保养耗时（分钟）
        /// </summary>
        public int TotalMaintenanceDuration { get; set; }

        /// <summary>
        /// 平均保养耗时（分钟）
        /// </summary>
        public double AverageMaintenanceDuration { get; set; }

        /// <summary>
        /// 保养成功率
        /// </summary>
        public double SuccessRate { get; set; }

        /// <summary>
        /// 按时完成率
        /// </summary>
        public double OnTimeCompletionRate { get; set; }

        /// <summary>
        /// 首次保养日期
        /// </summary>
        public DateTime? FirstMaintenanceDate { get; set; }

        /// <summary>
        /// 最近保养日期
        /// </summary>
        public DateTime? LastMaintenanceDate { get; set; }

        /// <summary>
        /// 下次建议保养日期
        /// </summary>
        public DateTime? NextSuggestedMaintenanceDate { get; set; }

        /// <summary>
        /// 当前库存数量
        /// </summary>
        public int CurrentStockQuantity { get; set; }

        /// <summary>
        /// 最低库存量
        /// </summary>
        public int MinStockLevel { get; set; }

        /// <summary>
        /// 库存状态（1=正常,2=不足,3=缺货）
        /// </summary>
        public int StockStatus { get; set; }

        /// <summary>
        /// 库存价值
        /// </summary>
        public decimal StockValue { get; set; }

        /// <summary>
        /// 是否为关键部件
        /// </summary>
        public bool IsCritical { get; set; }

        /// <summary>
        /// 部件状态（1=正常,2=停用,3=淘汰）
        /// </summary>
        public int ComponentStatus { get; set; }

        /// <summary>
        /// 统计时间段开始日期
        /// </summary>
        public DateTime StatisticsPeriodStart { get; set; }

        /// <summary>
        /// 统计时间段结束日期
        /// </summary>
        public DateTime StatisticsPeriodEnd { get; set; }

        /// <summary>
        /// 保养频率（次/月）
        /// </summary>
        public double MaintenanceFrequency { get; set; }

        /// <summary>
        /// 故障率
        /// </summary>
        public double FailureRate { get; set; }

        /// <summary>
        /// 可靠性评分（0-100）
        /// </summary>
        public double ReliabilityScore { get; set; }
    }
}
