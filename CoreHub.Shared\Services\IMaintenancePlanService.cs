using CoreHub.Shared.Models.Database;
using CoreHub.Shared.Models.Common;
using CoreHub.Shared.Models;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 保养计划管理服务接口
    /// </summary>
    public interface IMaintenancePlanService
    {
        #region 基础CRUD操作

        /// <summary>
        /// 获取所有保养计划
        /// </summary>
        /// <returns>保养计划列表</returns>
        Task<List<MaintenancePlan>> GetAllPlansAsync();

        /// <summary>
        /// 根据ID获取保养计划
        /// </summary>
        /// <param name="id">计划ID</param>
        /// <returns>保养计划</returns>
        Task<MaintenancePlan?> GetPlanByIdAsync(int id);

        /// <summary>
        /// 根据编码获取保养计划
        /// </summary>
        /// <param name="code">计划编码</param>
        /// <returns>保养计划</returns>
        Task<MaintenancePlan?> GetPlanByCodeAsync(string code);

        /// <summary>
        /// 创建保养计划
        /// </summary>
        /// <param name="plan">保养计划</param>
        /// <returns>创建结果</returns>
        Task<(bool IsSuccess, string ErrorMessage, int? Id)> CreatePlanAsync(MaintenancePlan plan);

        /// <summary>
        /// 更新保养计划
        /// </summary>
        /// <param name="plan">保养计划</param>
        /// <returns>更新结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> UpdatePlanAsync(MaintenancePlan plan);

        /// <summary>
        /// 删除保养计划
        /// </summary>
        /// <param name="id">计划ID</param>
        /// <returns>删除结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> DeletePlanAsync(int id);

        /// <summary>
        /// 切换计划启用状态
        /// </summary>
        /// <param name="id">计划ID</param>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> ToggleStatusAsync(int id);

        #endregion

        #region 搜索和筛选

        /// <summary>
        /// 搜索保养计划（分页版本）
        /// </summary>
        /// <param name="filter">搜索过滤条件</param>
        /// <returns>分页搜索结果</returns>
        Task<PagedResult<MaintenancePlan>> SearchPlansAsync(MaintenancePlanFilter filter);

        /// <summary>
        /// 搜索保养计划（简单版本）
        /// </summary>
        /// <param name="keyword">关键词</param>
        /// <param name="componentId">部件ID</param>
        /// <param name="maintenanceType">保养类型</param>
        /// <param name="status">状态</param>
        /// <param name="responsibleDepartmentId">负责部门ID</param>
        /// <returns>搜索结果</returns>
        Task<List<MaintenancePlan>> SearchPlansAsync(string? keyword = null, int? componentId = null, int? maintenanceType = null, int? status = null, int? responsibleDepartmentId = null);

        /// <summary>
        /// 获取部件的保养计划
        /// </summary>
        /// <param name="componentId">部件ID</param>
        /// <returns>保养计划列表</returns>
        Task<List<MaintenancePlan>> GetPlansByComponentIdAsync(int componentId);

        /// <summary>
        /// 获取设备的所有保养计划
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns>保养计划列表</returns>
        Task<List<MaintenancePlan>> GetPlansByEquipmentIdAsync(int equipmentId);

        #endregion

        #region 保养提醒和调度

        /// <summary>
        /// 获取需要提醒的保养计划
        /// </summary>
        /// <param name="days">提前天数（可选，默认使用计划中的提醒天数）</param>
        /// <returns>需要提醒的保养计划列表</returns>
        Task<List<MaintenancePlan>> GetPlansNeedingReminderAsync(int? days = null);

        /// <summary>
        /// 获取已过期的保养计划
        /// </summary>
        /// <returns>已过期的保养计划列表</returns>
        Task<List<MaintenancePlan>> GetOverduePlansAsync();

        /// <summary>
        /// 获取今日需要保养的计划
        /// </summary>
        /// <returns>今日保养计划列表</returns>
        Task<List<MaintenancePlan>> GetTodayMaintenancePlansAsync();

        /// <summary>
        /// 获取本周需要保养的计划
        /// </summary>
        /// <returns>本周保养计划列表</returns>
        Task<List<MaintenancePlan>> GetWeeklyMaintenancePlansAsync();

        /// <summary>
        /// 获取本月需要保养的计划
        /// </summary>
        /// <returns>本月保养计划列表</returns>
        Task<List<MaintenancePlan>> GetMonthlyMaintenancePlansAsync();

        #endregion

        #region 周期计算和日期管理

        /// <summary>
        /// 计算下次保养日期
        /// </summary>
        /// <param name="plan">保养计划</param>
        /// <param name="lastMaintenanceDate">最后保养日期（可选，默认使用计划中的日期）</param>
        /// <returns>下次保养日期</returns>
        Task<DateTime?> CalculateNextMaintenanceDateAsync(MaintenancePlan plan, DateTime? lastMaintenanceDate = null);

        /// <summary>
        /// 更新保养计划的下次保养日期
        /// </summary>
        /// <param name="planId">计划ID</param>
        /// <param name="lastMaintenanceDate">最后保养日期</param>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateNextMaintenanceDateAsync(int planId, DateTime lastMaintenanceDate);

        /// <summary>
        /// 批量更新保养计划的下次保养日期
        /// </summary>
        /// <param name="updates">更新列表</param>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> BatchUpdateNextMaintenanceDateAsync(List<(int PlanId, DateTime LastMaintenanceDate)> updates);

        /// <summary>
        /// 重新计算所有计划的下次保养日期
        /// </summary>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage, int UpdatedCount)> RecalculateAllNextMaintenanceDatesAsync();

        #endregion

        #region 保养计划执行

        /// <summary>
        /// 标记保养计划为已执行
        /// </summary>
        /// <param name="planId">计划ID</param>
        /// <param name="executionDate">执行日期</param>
        /// <param name="maintenanceRecordId">保养记录ID（可选）</param>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> MarkPlanAsExecutedAsync(int planId, DateTime executionDate, int? maintenanceRecordId = null);

        /// <summary>
        /// 暂停保养计划
        /// </summary>
        /// <param name="planId">计划ID</param>
        /// <param name="reason">暂停原因</param>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> PausePlanAsync(int planId, string reason);

        /// <summary>
        /// 恢复保养计划
        /// </summary>
        /// <param name="planId">计划ID</param>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> ResumePlanAsync(int planId);

        #endregion

        #region 统计分析

        /// <summary>
        /// 获取保养计划统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        Task<MaintenancePlanStatistics> GetPlanStatisticsAsync();

        /// <summary>
        /// 获取保养计划执行率统计
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>执行率统计</returns>
        Task<MaintenancePlanExecutionStatistics> GetPlanExecutionStatisticsAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// 获取部门保养工作量统计
        /// </summary>
        /// <param name="departmentId">部门ID（可选）</param>
        /// <param name="days">统计天数</param>
        /// <returns>工作量统计</returns>
        Task<List<DepartmentMaintenanceWorkload>> GetDepartmentWorkloadStatisticsAsync(int? departmentId = null, int days = 30);

        #endregion

        #region 模板和批量操作

        /// <summary>
        /// 根据模板创建保养计划
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <param name="componentId">部件ID</param>
        /// <param name="customizations">自定义参数</param>
        /// <returns>创建结果</returns>
        Task<(bool IsSuccess, string ErrorMessage, int? Id)> CreatePlanFromTemplateAsync(int templateId, int componentId, Dictionary<string, object>? customizations = null);

        /// <summary>
        /// 批量创建保养计划
        /// </summary>
        /// <param name="plans">保养计划列表</param>
        /// <returns>创建结果</returns>
        Task<(bool IsSuccess, string ErrorMessage, int SuccessCount, int FailCount)> BatchCreatePlansAsync(List<MaintenancePlan> plans);

        /// <summary>
        /// 复制保养计划
        /// </summary>
        /// <param name="sourcePlanId">源计划ID</param>
        /// <param name="targetComponentId">目标部件ID</param>
        /// <param name="newCode">新计划编码</param>
        /// <param name="newName">新计划名称</param>
        /// <returns>复制结果</returns>
        Task<(bool IsSuccess, string ErrorMessage, int? Id)> CopyPlanAsync(int sourcePlanId, int targetComponentId, string newCode, string newName);

        #endregion
    }
}
