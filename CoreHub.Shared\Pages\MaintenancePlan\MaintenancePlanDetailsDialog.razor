@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@using CoreHub.Shared.Extensions
@inject IMaintenancePlanService MaintenancePlanService
@inject IDialogService DialogService

<MudDialog>
    <DialogContent>
        <MudContainer MaxWidth="MaxWidth.Large">
            @if (Plan != null)
            {
                <MudGrid>
                    <MudItem xs="12" md="6">
                        <MudCard>
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">基本信息</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                <MudGrid>
                                    <MudItem xs="12">
                                        <MudText><strong>计划编码：</strong>@Plan.Code</MudText>
                                    </MudItem>
                                    <MudItem xs="12">
                                        <MudText><strong>计划名称：</strong>@Plan.Name</MudText>
                                    </MudItem>
                                    <MudItem xs="12">
                                        <MudText><strong>部件名称：</strong>@Plan.ComponentName</MudText>
                                    </MudItem>
                                    <MudItem xs="12">
                                        <MudText><strong>保养类型：</strong>@Plan.MaintenanceTypeName</MudText>
                                    </MudItem>
                                    <MudItem xs="12">
                                        <MudText><strong>状态：</strong>@Plan.StatusName</MudText>
                                    </MudItem>
                                </MudGrid>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudCard>
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">保养周期</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                <MudGrid>
                                    <MudItem xs="12">
                                        <MudText><strong>周期类型：</strong>@Plan.CycleTypeName</MudText>
                                    </MudItem>
                                    <MudItem xs="12">
                                        <MudText><strong>周期值：</strong>@Plan.CycleValue</MudText>
                                    </MudItem>
                                    <MudItem xs="12">
                                        <MudText><strong>下次保养日期：</strong>@Plan.NextMaintenanceDate?.ToString("yyyy-MM-dd")</MudText>
                                    </MudItem>
                                    <MudItem xs="12">
                                        <MudText><strong>最后保养日期：</strong>@Plan.LastMaintenanceDate?.ToString("yyyy-MM-dd")</MudText>
                                    </MudItem>
                                </MudGrid>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    
                    <MudItem xs="12">
                        <MudCard>
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">保养内容</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                <MudText>@Plan.MaintenanceContent</MudText>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    
                    @if (!string.IsNullOrEmpty(Plan.Notes))
                    {
                        <MudItem xs="12">
                            <MudCard>
                                <MudCardHeader>
                                    <CardHeaderContent>
                                        <MudText Typo="Typo.h6">备注</MudText>
                                    </CardHeaderContent>
                                </MudCardHeader>
                                <MudCardContent>
                                    <MudText>@Plan.Notes</MudText>
                                </MudCardContent>
                            </MudCard>
                        </MudItem>
                    }
                </MudGrid>
            }
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">关闭</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public MaintenancePlan? Plan { get; set; }

    void Cancel() => MudDialog.Cancel();
}
