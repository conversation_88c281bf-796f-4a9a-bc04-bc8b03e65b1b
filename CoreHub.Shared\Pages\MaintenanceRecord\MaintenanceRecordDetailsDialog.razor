@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@using CoreHub.Shared.Extensions
@inject IMaintenanceRecordService MaintenanceRecordService

<MudDialog>
    <DialogContent>
        <MudContainer MaxWidth="MaxWidth.Large">
            @if (Record != null)
            {
                <MudGrid>
                    <MudItem xs="12" md="6">
                        <MudCard>
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">基本信息</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                <MudGrid>
                                    <MudItem xs="12">
                                        <MudText><strong>记录编号：</strong>@Record.Id</MudText>
                                    </MudItem>
                                    <MudItem xs="12">
                                        <MudText><strong>部件名称：</strong>@Record.ComponentName</MudText>
                                    </MudItem>
                                    <MudItem xs="12">
                                        <MudText><strong>保养日期：</strong>@Record.MaintenanceDate.ToString("yyyy-MM-dd HH:mm")</MudText>
                                    </MudItem>
                                    <MudItem xs="12">
                                        <MudText><strong>保养人员：</strong>@Record.MaintenancePersonName</MudText>
                                    </MudItem>
                                    <MudItem xs="12">
                                        <MudText><strong>状态：</strong>@Record.StatusName</MudText>
                                    </MudItem>
                                </MudGrid>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudCard>
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">执行情况</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                <MudGrid>
                                    <MudItem xs="12">
                                        <MudText><strong>预计耗时：</strong>@(Record.EstimatedDuration?.ToString() ?? "未设置") 分钟</MudText>
                                    </MudItem>
                                    <MudItem xs="12">
                                        <MudText><strong>实际耗时：</strong>@(Record.ActualDuration?.ToString() ?? "未记录") 分钟</MudText>
                                    </MudItem>
                                    <MudItem xs="12">
                                        <MudText><strong>预计成本：</strong>@(Record.EstimatedCost?.ToString("F2") ?? "未设置")</MudText>
                                    </MudItem>
                                    <MudItem xs="12">
                                        <MudText><strong>实际成本：</strong>@(Record.ActualCost?.ToString("F2") ?? "未记录")</MudText>
                                    </MudItem>
                                </MudGrid>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    
                    <MudItem xs="12">
                        <MudCard>
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">保养内容</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                <MudText>@Record.MaintenanceContent</MudText>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    
                    @if (Record.MaintenanceResult > 0)
                    {
                        <MudItem xs="12">
                            <MudCard>
                                <MudCardHeader>
                                    <CardHeaderContent>
                                        <MudText Typo="Typo.h6">保养结果</MudText>
                                    </CardHeaderContent>
                                </MudCardHeader>
                                <MudCardContent>
                                    <MudText>@Record.MaintenanceResult</MudText>
                                </MudCardContent>
                            </MudCard>
                        </MudItem>
                    }
                    
                    @if (!string.IsNullOrEmpty(Record.Notes))
                    {
                        <MudItem xs="12">
                            <MudCard>
                                <MudCardHeader>
                                    <CardHeaderContent>
                                        <MudText Typo="Typo.h6">备注</MudText>
                                    </CardHeaderContent>
                                </MudCardHeader>
                                <MudCardContent>
                                    <MudText>@Record.Notes</MudText>
                                </MudCardContent>
                            </MudCard>
                        </MudItem>
                    }
                    
                    @if (Record.ReviewerId.HasValue)
                    {
                        <MudItem xs="12">
                            <MudCard>
                                <MudCardHeader>
                                    <CardHeaderContent>
                                        <MudText Typo="Typo.h6">审核信息</MudText>
                                    </CardHeaderContent>
                                </MudCardHeader>
                                <MudCardContent>
                                    <MudGrid>
                                        <MudItem xs="12" md="6">
                                            <MudText><strong>审核人：</strong>@Record.ReviewerName</MudText>
                                        </MudItem>
                                        <MudItem xs="12" md="6">
                                            <MudText><strong>审核日期：</strong>@Record.ReviewDate?.ToString("yyyy-MM-dd HH:mm")</MudText>
                                        </MudItem>
                                        @if (!string.IsNullOrEmpty(Record.ReviewNotes))
                                        {
                                            <MudItem xs="12">
                                                <MudText><strong>审核意见：</strong>@Record.ReviewNotes</MudText>
                                            </MudItem>
                                        }
                                    </MudGrid>
                                </MudCardContent>
                            </MudCard>
                        </MudItem>
                    }
                </MudGrid>
            }
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">关闭</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public MaintenanceRecord? Record { get; set; }

    void Cancel() => MudDialog.Cancel();
}
