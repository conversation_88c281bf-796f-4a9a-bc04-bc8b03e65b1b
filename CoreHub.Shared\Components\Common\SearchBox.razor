@using MudBlazor

<MudPaper Class="pa-4 mb-4" Elevation="2">
    <MudGrid AlignItems="Center">
        <MudItem xs="12" sm="6" md="4">
            <MudTextField @bind-Value="SearchKeyword"
                         Label="搜索关键词"
                         Placeholder="@Placeholder"
                         Variant="Variant.Outlined"
                         Adornment="Adornment.End"
                         AdornmentIcon="Icons.Material.Filled.Search"
                         OnAdornmentClick="OnSearch"
                         OnKeyPress="OnKeyPress"
                         Clearable="true"
                         Immediate="false" />
        </MudItem>
        
        @if (ShowDateRange)
        {
            <MudItem xs="12" sm="3" md="2">
                <MudDatePicker @bind-Date="StartDate"
                              Label="开始日期"
                              Variant="Variant.Outlined"
                              DateFormat="yyyy-MM-dd"
                              Clearable="true" />
            </MudItem>
            <MudItem xs="12" sm="3" md="2">
                <MudDatePicker @bind-Date="EndDate"
                              Label="结束日期"
                              Variant="Variant.Outlined"
                              DateFormat="yyyy-MM-dd"
                              Clearable="true" />
            </MudItem>
        }
        
        @if (ShowStatusFilter)
        {
            <MudItem xs="12" sm="6" md="2">
                <MudSelect @bind-Value="SelectedStatus"
                          Label="状态"
                          Variant="Variant.Outlined"
                          Clearable="true"
                          AnchorOrigin="Origin.BottomCenter">
                    @foreach (var status in StatusOptions)
                    {
                        <MudSelectItem Value="@status.Value">@status.Text</MudSelectItem>
                    }
                </MudSelect>
            </MudItem>
        }
        
        <MudItem xs="12" sm="6" md="2">
            <MudButtonGroup Variant="Variant.Outlined" Color="Color.Primary">
                <MudButton StartIcon="Icons.Material.Filled.Search"
                          OnClick="OnSearch"
                          Color="Color.Primary">
                    搜索
                </MudButton>
                <MudButton StartIcon="Icons.Material.Filled.Clear"
                          OnClick="OnClear"
                          Color="Color.Secondary">
                    清除
                </MudButton>
            </MudButtonGroup>
        </MudItem>
        
        @if (ShowAdvancedFilter)
        {
            <MudItem xs="12">
                <MudCollapse Expanded="ShowAdvanced">
                    <MudPaper Class="pa-4 mt-2" Elevation="1">
                        <MudText Typo="Typo.subtitle2" Class="mb-3">高级筛选</MudText>
                        @AdvancedFilterContent
                    </MudPaper>
                </MudCollapse>
                <MudButton StartIcon="@(ShowAdvanced ? Icons.Material.Filled.ExpandLess : Icons.Material.Filled.ExpandMore)"
                          OnClick="ToggleAdvanced"
                          Variant="Variant.Text"
                          Size="Size.Small"
                          Class="mt-2">
                    @(ShowAdvanced ? "收起高级筛选" : "展开高级筛选")
                </MudButton>
            </MudItem>
        }
    </MudGrid>
</MudPaper>

@code {
    [Parameter] public string Placeholder { get; set; } = "请输入搜索关键词";
    [Parameter] public bool ShowDateRange { get; set; } = true;
    [Parameter] public bool ShowStatusFilter { get; set; } = true;
    [Parameter] public bool ShowAdvancedFilter { get; set; } = false;
    [Parameter] public RenderFragment? AdvancedFilterContent { get; set; }
    [Parameter] public List<SelectOption> StatusOptions { get; set; } = new();
    
    [Parameter] public string? SearchKeyword { get; set; }
    [Parameter] public EventCallback<string?> SearchKeywordChanged { get; set; }
    
    [Parameter] public DateTime? StartDate { get; set; }
    [Parameter] public EventCallback<DateTime?> StartDateChanged { get; set; }
    
    [Parameter] public DateTime? EndDate { get; set; }
    [Parameter] public EventCallback<DateTime?> EndDateChanged { get; set; }
    
    [Parameter] public int? SelectedStatus { get; set; }
    [Parameter] public EventCallback<int?> SelectedStatusChanged { get; set; }
    
    [Parameter] public EventCallback OnSearchClicked { get; set; }
    [Parameter] public EventCallback OnClearClicked { get; set; }

    private bool ShowAdvanced = false;

    protected override void OnInitialized()
    {
        if (!StatusOptions.Any())
        {
            StatusOptions = new List<SelectOption>
            {
                new(1, "启用"),
                new(0, "禁用")
            };
        }
    }

    private async Task OnSearch()
    {
        await OnSearchClicked.InvokeAsync();
    }

    private async Task OnClear()
    {
        SearchKeyword = null;
        await SearchKeywordChanged.InvokeAsync(SearchKeyword);
        
        StartDate = null;
        await StartDateChanged.InvokeAsync(StartDate);
        
        EndDate = null;
        await EndDateChanged.InvokeAsync(EndDate);
        
        SelectedStatus = null;
        await SelectedStatusChanged.InvokeAsync(SelectedStatus);
        
        await OnClearClicked.InvokeAsync();
    }

    private async Task OnKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await OnSearch();
        }
    }

    private void ToggleAdvanced()
    {
        ShowAdvanced = !ShowAdvanced;
    }

    public class SelectOption
    {
        public int Value { get; set; }
        public string Text { get; set; } = string.Empty;

        public SelectOption() { }

        public SelectOption(int value, string text)
        {
            Value = value;
            Text = text;
        }
    }
}
