using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace CoreHub.Shared.Models.Database
{
    /// <summary>
    /// 保养记录实体
    /// </summary>
    [SugarTable("MaintenanceRecords")]
    public class MaintenanceRecord
    {
        /// <summary>
        /// 保养记录ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 记录编号（唯一）
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        [Required(ErrorMessage = "记录编号不能为空")]
        [StringLength(50, ErrorMessage = "记录编号长度不能超过50个字符")]
        public string RecordNumber { get; set; } = string.Empty;

        /// <summary>
        /// 关联的保养计划ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? MaintenancePlanId { get; set; }

        /// <summary>
        /// 计划ID（别名，用于兼容）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int? PlanId { get => MaintenancePlanId; set => MaintenancePlanId = value; }

        /// <summary>
        /// 关联的设备部件ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "设备部件不能为空")]
        public int ComponentId { get; set; }

        /// <summary>
        /// 保养类型（1=预防性保养,2=定期检查,3=深度保养,4=应急保养,5=故障维修）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int MaintenanceType { get; set; } = 1;

        /// <summary>
        /// 保养日期
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "保养日期不能为空")]
        public DateTime MaintenanceDate { get; set; }

        /// <summary>
        /// 保养开始时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 保养结束时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 计划保养时长（分钟）（仅用于显示，不映射到数据库）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int? PlannedDuration { get; set; }

        /// <summary>
        /// 实际保养时长（分钟）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? ActualDuration { get; set; }

        /// <summary>
        /// 保养人员ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "保养人员不能为空")]
        public int MaintenancePersonId { get; set; }

        /// <summary>
        /// 协助人员ID列表（JSON格式存储）
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "协助人员ID列表长度不能超过500个字符")]
        public string? AssistantPersonIds { get; set; }

        /// <summary>
        /// 保养前状态描述
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "保养前状态描述长度不能超过1000个字符")]
        public string? BeforeMaintenanceStatus { get; set; }

        /// <summary>
        /// 保养内容
        /// </summary>
        [SugarColumn(Length = 2000, IsNullable = false)]
        [Required(ErrorMessage = "保养内容不能为空")]
        [StringLength(2000, ErrorMessage = "保养内容长度不能超过2000个字符")]
        public string MaintenanceContent { get; set; } = string.Empty;

        /// <summary>
        /// 使用的工具
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "使用的工具长度不能超过500个字符")]
        public string? UsedTools { get; set; }

        /// <summary>
        /// 使用的材料
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "使用的材料长度不能超过500个字符")]
        public string? UsedMaterials { get; set; }

        /// <summary>
        /// 更换的部件清单
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "更换的部件清单长度不能超过1000个字符")]
        public string? ReplacedParts { get; set; }

        /// <summary>
        /// 保养后状态描述
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "保养后状态描述长度不能超过1000个字符")]
        public string? AfterMaintenanceStatus { get; set; }

        /// <summary>
        /// 预计成本（仅用于显示，不映射到数据库）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public decimal? EstimatedCost { get; set; }

        /// <summary>
        /// 保养结果（1=正常,2=发现问题,3=需要进一步维修,4=保养失败）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int MaintenanceResult { get; set; } = 1;

        /// <summary>
        /// 发现的问题
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "发现的问题长度不能超过1000个字符")]
        public string? FoundIssues { get; set; }

        /// <summary>
        /// 建议措施
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "建议措施长度不能超过1000个字符")]
        public string? Recommendations { get; set; }

        /// <summary>
        /// 解决方案描述（仅用于显示，不映射到数据库）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string? SolutionDescription { get; set; }

        /// <summary>
        /// 下次保养建议日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? NextMaintenanceSuggestion { get; set; }

        /// <summary>
        /// 保养费用
        /// </summary>
        [SugarColumn(DecimalDigits = 2, IsNullable = true)]
        public decimal? MaintenanceCost { get; set; }

        /// <summary>
        /// 实际成本（别名，用于兼容）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public decimal? ActualCost { get => MaintenanceCost; set => MaintenanceCost = value; }

        /// <summary>
        /// 预计时长（分钟）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int? EstimatedDuration { get => PlannedDuration; set => PlannedDuration = value; }

        /// <summary>
        /// 计划日期（别名，用于兼容）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public DateTime? ScheduledDate { get => MaintenanceDate; set => MaintenanceDate = value ?? DateTime.Now; }

        /// <summary>
        /// 发现的问题（别名，用于兼容）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string? Problems { get => FoundIssues; set => FoundIssues = value; }

        /// <summary>
        /// 解决方案（别名，用于兼容）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string? Solutions { get => SolutionDescription; set => SolutionDescription = value; }

        /// <summary>
        /// 备注（别名，用于兼容）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string? Notes { get => ReviewComments; set => ReviewComments = value; }

        /// <summary>
        /// 审核人员ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? ReviewedBy { get; set; }

        /// <summary>
        /// 审核时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? ReviewedAt { get; set; }

        /// <summary>
        /// 记录状态（1=待执行,2=执行中,3=已完成,4=已取消）（仅用于显示，不映射到数据库）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int Status { get; set; } = 1;

        /// <summary>
        /// 审核状态（1=待审核,2=已通过,3=需要重做）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int ReviewStatus { get; set; } = 1;

        /// <summary>
        /// 审核意见
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "审核意见长度不能超过500个字符")]
        public string? ReviewComments { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
        public string? Remark { get; set; }

        // 导航属性
        /// <summary>
        /// 关联的保养计划
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public MaintenancePlan? MaintenancePlan { get; set; }

        /// <summary>
        /// 关联的设备部件
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public EquipmentComponent? Component { get; set; }

        /// <summary>
        /// 保养人员
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public User? MaintenancePerson { get; set; }

        /// <summary>
        /// 审核人员
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public User? Reviewer { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public User? Creator { get; set; }

        /// <summary>
        /// 更新人
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public User? Updater { get; set; }

        /// <summary>
        /// 部件名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ComponentName => Component?.Name ?? "";

        /// <summary>
        /// 部件编码
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ComponentCode => Component?.Code ?? "";

        /// <summary>
        /// 保养人员姓名
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string MaintenancePersonName { get => MaintenancePerson?.DisplayName ?? ""; set { } }

        /// <summary>
        /// 审核人员姓名
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ReviewerName { get => Reviewer?.DisplayName ?? ""; set { } }

        /// <summary>
        /// 审核日期
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public DateTime? ReviewDate { get => ReviewedAt; set => ReviewedAt = value; }

        /// <summary>
        /// 保养类型名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string MaintenanceTypeName => MaintenanceType switch
        {
            1 => "预防性保养",
            2 => "定期检查",
            3 => "深度保养",
            4 => "应急保养",
            5 => "故障维修",
            _ => "未知"
        };

        /// <summary>
        /// 保养结果名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string MaintenanceResultName => MaintenanceResult switch
        {
            1 => "正常",
            2 => "发现问题",
            3 => "需要进一步维修",
            4 => "保养失败",
            _ => "未知"
        };

        /// <summary>
        /// 审核状态名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ReviewStatusName => ReviewStatus switch
        {
            1 => "待审核",
            2 => "已通过",
            3 => "需要重做",
            _ => "未知"
        };

        /// <summary>
        /// 状态名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string StatusName => Status switch
        {
            1 => "待执行",
            2 => "执行中",
            3 => "已完成",
            4 => "已取消",
            _ => "未知"
        };

        /// <summary>
        /// 审核人员ID（别名）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int? ReviewerId { get => ReviewedBy; set => ReviewedBy = value; }

        /// <summary>
        /// 审核意见（别名）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string? ReviewNotes { get => ReviewComments; set => ReviewComments = value; }

        /// <summary>
        /// 是否已完成
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool IsCompleted => EndTime.HasValue;

        /// <summary>
        /// 保养时长显示文本
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string DurationText
        {
            get
            {
                if (!ActualDuration.HasValue)
                    return "未记录";
                
                var hours = ActualDuration.Value / 60;
                var minutes = ActualDuration.Value % 60;
                
                if (hours > 0)
                    return $"{hours}小时{minutes}分钟";
                else
                    return $"{minutes}分钟";
            }
        }
    }
}
