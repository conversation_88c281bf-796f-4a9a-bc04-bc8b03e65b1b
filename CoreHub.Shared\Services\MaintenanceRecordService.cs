using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using CoreHub.Shared.Models.Common;
using CoreHub.Shared.Models;
using SqlSugar;
using System.Linq;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 保养记录管理服务实现
    /// </summary>
    public class MaintenanceRecordService : IMaintenanceRecordService
    {
        private readonly DatabaseContext _context;
        private readonly IMaintenancePlanService _planService;

        public MaintenanceRecordService(DatabaseContext context, IMaintenancePlanService planService)
        {
            _context = context;
            _planService = planService;
        }

        #region 基础CRUD操作

        /// <summary>
        /// 获取所有保养记录
        /// </summary>
        public async Task<List<MaintenanceRecord>> GetAllRecordsAsync()
        {
            return await _context.MaintenanceRecords
                .OrderByDescending(r => r.MaintenanceDate)
                .ToListAsync();
        }

        /// <summary>
        /// 根据ID获取保养记录
        /// </summary>
        public async Task<MaintenanceRecord?> GetRecordByIdAsync(int id)
        {
            return await _context.MaintenanceRecords
                .Where(r => r.Id == id)
                .FirstAsync();
        }

        /// <summary>
        /// 根据记录编号获取保养记录
        /// </summary>
        public async Task<MaintenanceRecord?> GetRecordByNumberAsync(string recordNumber)
        {
            return await _context.MaintenanceRecords
                .Where(r => r.RecordNumber == recordNumber)
                .FirstAsync();
        }

        /// <summary>
        /// 创建保养记录
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage, int? Id)> CreateAsync(MaintenanceRecord record)
        {
            return await CreateRecordAsync(record);
        }

        /// <summary>
        /// 创建保养记录
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage, int? Id)> CreateRecordAsync(MaintenanceRecord record)
        {
            try
            {
                // 检查记录编号是否已存在
                var existingRecord = await _context.MaintenanceRecords
                    .Where(r => r.RecordNumber == record.RecordNumber)
                    .FirstAsync();

                if (existingRecord != null)
                {
                    return (false, "保养记录编号已存在", null);
                }

                // 检查部件是否存在
                var component = await _context.EquipmentComponents
                    .Where(c => c.Id == record.ComponentId && c.IsEnabled)
                    .FirstAsync();

                if (component == null)
                {
                    return (false, "指定的部件不存在或已禁用", null);
                }

                record.CreatedAt = DateTime.Now;
                var result = await _context.Db.Insertable(record).ExecuteReturnIdentityAsync();
                return (true, string.Empty, result);
            }
            catch (Exception ex)
            {
                return (false, $"创建保养记录失败：{ex.Message}", null);
            }
        }

        /// <summary>
        /// 更新保养记录
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateRecordAsync(MaintenanceRecord record)
        {
            try
            {
                // 检查记录编号是否与其他记录冲突
                var existingRecord = await _context.MaintenanceRecords
                    .Where(r => r.RecordNumber == record.RecordNumber && r.Id != record.Id)
                    .FirstAsync();

                if (existingRecord != null)
                {
                    return (false, "保养记录编号已存在");
                }

                record.UpdatedAt = DateTime.Now;
                await _context.Db.Updateable(record).ExecuteCommandAsync();
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, $"更新保养记录失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 删除保养记录
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> DeleteRecordAsync(int id)
        {
            try
            {
                var record = await GetRecordByIdAsync(id);
                if (record == null)
                {
                    return (false, "保养记录不存在");
                }

                if (record.ReviewStatus == 2)
                {
                    return (false, "已审核通过的记录无法删除");
                }

                await _context.Db.Deleteable<MaintenanceRecord>().Where(r => r.Id == id).ExecuteCommandAsync();
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, $"删除保养记录失败：{ex.Message}");
            }
        }

        #endregion

        #region 搜索和筛选

        /// <summary>
        /// 搜索保养记录（分页版本）
        /// </summary>
        public async Task<PagedResult<MaintenanceRecord>> SearchRecordsAsync(MaintenanceRecordFilter filter)
        {
            try
            {
                var query = _context.MaintenanceRecords;

                // 应用搜索条件
                if (!string.IsNullOrEmpty(filter.Keyword))
                {
                    query = query.Where(r => r.RecordNumber.Contains(filter.Keyword) ||
                                           r.MaintenancePersonName.Contains(filter.Keyword) ||
                                           r.MaintenanceContent.Contains(filter.Keyword));
                }

                if (filter.ComponentId.HasValue)
                {
                    query = query.Where(r => r.ComponentId == filter.ComponentId.Value);
                }

                if (filter.MaintenanceType.HasValue)
                {
                    query = query.Where(r => r.MaintenanceType == filter.MaintenanceType.Value);
                }

                if (filter.MaintenanceResult.HasValue)
                {
                    query = query.Where(r => r.MaintenanceResult == filter.MaintenanceResult.Value);
                }

                if (filter.ReviewStatus.HasValue)
                {
                    query = query.Where(r => r.ReviewStatus == filter.ReviewStatus.Value);
                }

                if (filter.MaintenanceStartDate.HasValue)
                {
                    query = query.Where(r => r.MaintenanceDate >= filter.MaintenanceStartDate.Value);
                }

                if (filter.MaintenanceEndDate.HasValue)
                {
                    query = query.Where(r => r.MaintenanceDate <= filter.MaintenanceEndDate.Value);
                }

                if (filter.MinCost.HasValue)
                {
                    query = query.Where(r => r.ActualCost >= filter.MinCost.Value);
                }

                if (filter.MaxCost.HasValue)
                {
                    query = query.Where(r => r.ActualCost <= filter.MaxCost.Value);
                }

                // 获取总数
                var totalCount = await query.CountAsync();

                // 应用分页
                var items = await query
                    .OrderByDescending(r => r.MaintenanceDate)
                    .Skip((filter.PageIndex - 1) * filter.PageSize)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return PagedResult<MaintenanceRecord>.Create(items, totalCount, filter.PageIndex, filter.PageSize);
            }
            catch (Exception ex)
            {
                throw new Exception($"搜索保养记录失败：{ex.Message}", ex);
            }
        }

        /// <summary>
        /// 搜索保养记录（简单版本）
        /// </summary>
        public async Task<List<MaintenanceRecord>> SearchRecordsAsync(string? keyword = null, int? componentId = null, int? maintenanceType = null, int? maintenancePersonId = null, DateTime? startDate = null, DateTime? endDate = null, int? reviewStatus = null)
        {
            var query = _context.MaintenanceRecords;

            if (!string.IsNullOrEmpty(keyword))
            {
                query = query.Where(r => r.RecordNumber.Contains(keyword) || 
                                       r.MaintenanceContent.Contains(keyword) ||
                                       r.FoundIssues.Contains(keyword));
            }

            if (componentId.HasValue)
            {
                query = query.Where(r => r.ComponentId == componentId.Value);
            }

            if (maintenanceType.HasValue)
            {
                query = query.Where(r => r.MaintenanceType == maintenanceType.Value);
            }

            if (maintenancePersonId.HasValue)
            {
                query = query.Where(r => r.MaintenancePersonId == maintenancePersonId.Value);
            }

            if (startDate.HasValue)
            {
                query = query.Where(r => r.MaintenanceDate >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(r => r.MaintenanceDate <= endDate.Value);
            }

            if (reviewStatus.HasValue)
            {
                query = query.Where(r => r.ReviewStatus == reviewStatus.Value);
            }

            return await query.OrderByDescending(r => r.MaintenanceDate).ToListAsync();
        }

        /// <summary>
        /// 获取部件的保养记录
        /// </summary>
        public async Task<List<MaintenanceRecord>> GetRecordsByComponentIdAsync(int componentId, int? limit = null)
        {
            var query = _context.MaintenanceRecords
                .Where(r => r.ComponentId == componentId)
                .OrderByDescending(r => r.MaintenanceDate);

            if (limit.HasValue)
            {
                query = query.Take(limit.Value);
            }

            return await query.ToListAsync();
        }

        /// <summary>
        /// 获取设备的所有保养记录
        /// </summary>
        public async Task<List<MaintenanceRecord>> GetRecordsByEquipmentIdAsync(int equipmentId, int? limit = null)
        {
            // 通过设备部件关联获取保养记录
            var componentIds = await _context.EquipmentComponentMappings
                .Where(m => m.EquipmentId == equipmentId && m.IsEnabled)
                .Select(m => m.ComponentId)
                .ToListAsync();

            var query = _context.MaintenanceRecords
                .Where(r => componentIds.Contains(r.ComponentId))
                .OrderByDescending(r => r.MaintenanceDate);

            if (limit.HasValue)
            {
                query = query.Take(limit.Value);
            }

            return await query.ToListAsync();
        }

        /// <summary>
        /// 获取人员的保养记录
        /// </summary>
        public async Task<List<MaintenanceRecord>> GetRecordsByPersonAsync(int personId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = _context.MaintenanceRecords
                .Where(r => r.MaintenancePersonId == personId);

            if (startDate.HasValue)
            {
                query = query.Where(r => r.MaintenanceDate >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(r => r.MaintenanceDate <= endDate.Value);
            }

            return await query.OrderByDescending(r => r.MaintenanceDate).ToListAsync();
        }

        /// <summary>
        /// 获取保养计划的执行记录
        /// </summary>
        public async Task<List<MaintenanceRecord>> GetRecordsByPlanIdAsync(int planId)
        {
            return await _context.MaintenanceRecords
                .Where(r => r.MaintenancePlanId == planId)
                .OrderByDescending(r => r.MaintenanceDate)
                .ToListAsync();
        }

        #endregion

        #region 审核管理

        /// <summary>
        /// 获取待审核的保养记录
        /// </summary>
        public async Task<List<MaintenanceRecord>> GetPendingReviewRecordsAsync()
        {
            return await _context.MaintenanceRecords
                .Where(r => r.ReviewStatus == 1)
                .OrderBy(r => r.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// 审核保养记录
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> ReviewRecordAsync(int recordId, int reviewerId, int reviewStatus, string? reviewComments = null)
        {
            try
            {
                var record = await GetRecordByIdAsync(recordId);
                if (record == null)
                {
                    return (false, "保养记录不存在");
                }

                if (record.ReviewStatus != 1)
                {
                    return (false, "该记录已审核，无法重复审核");
                }

                record.ReviewedBy = reviewerId;
                record.ReviewedAt = DateTime.Now;
                record.ReviewStatus = reviewStatus;
                record.ReviewComments = reviewComments;
                record.UpdatedAt = DateTime.Now;

                await _context.Db.Updateable(record).ExecuteCommandAsync();

                // 如果审核通过且有关联的保养计划，更新计划的下次保养日期
                if (reviewStatus == 2 && record.MaintenancePlanId.HasValue)
                {
                    await _planService.UpdateNextMaintenanceDateAsync(record.MaintenancePlanId.Value, record.MaintenanceDate);
                }

                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, $"审核记录失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 批量审核保养记录
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage, int SuccessCount, int FailCount)> BatchReviewRecordsAsync(List<(int RecordId, int ReviewerId, int ReviewStatus, string? ReviewComments)> reviews)
        {
            var successCount = 0;
            var failCount = 0;
            var errors = new List<string>();

            try
            {
                await _context.Db.Ado.BeginTranAsync();

                foreach (var review in reviews)
                {
                    try
                    {
                        var result = await ReviewRecordAsync(review.RecordId, review.ReviewerId, review.ReviewStatus, review.ReviewComments);
                        if (result.IsSuccess)
                        {
                            successCount++;
                        }
                        else
                        {
                            failCount++;
                            errors.Add($"记录 {review.RecordId}: {result.ErrorMessage}");
                        }
                    }
                    catch (Exception ex)
                    {
                        failCount++;
                        errors.Add($"记录 {review.RecordId}: {ex.Message}");
                    }
                }

                await _context.Db.Ado.CommitTranAsync();

                var errorMessage = errors.Any() ? string.Join("; ", errors) : string.Empty;
                return (true, errorMessage, successCount, failCount);
            }
            catch (Exception ex)
            {
                await _context.Db.Ado.RollbackTranAsync();
                return (false, $"批量审核失败：{ex.Message}", successCount, failCount);
            }
        }

        #endregion

        #region 保养计划关联

        /// <summary>
        /// 从保养计划创建保养记录
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage, int? Id)> CreateRecordFromPlanAsync(int planId, int maintenancePersonId, DateTime scheduledDate)
        {
            try
            {
                var plan = await _planService.GetPlanByIdAsync(planId);
                if (plan == null)
                {
                    return (false, "保养计划不存在", null);
                }

                var recordNumber = await GenerateRecordNumberAsync(plan.MaintenanceType, scheduledDate);

                var record = new MaintenanceRecord
                {
                    RecordNumber = recordNumber,
                    MaintenancePlanId = planId,
                    ComponentId = plan.ComponentId,
                    MaintenanceType = plan.MaintenanceType,
                    MaintenanceDate = scheduledDate,
                    MaintenancePersonId = maintenancePersonId,
                    MaintenanceContent = plan.MaintenanceContent ?? string.Empty,
                    UsedTools = plan.RequiredTools,
                    UsedMaterials = plan.RequiredMaterials,
                    ReviewStatus = 1, // 待审核
                    CreatedAt = DateTime.Now
                };

                return await CreateRecordAsync(record);
            }
            catch (Exception ex)
            {
                return (false, $"从计划创建记录失败：{ex.Message}", null);
            }
        }

        /// <summary>
        /// 关联保养记录到保养计划
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> LinkRecordToPlanAsync(int recordId, int planId)
        {
            try
            {
                var record = await GetRecordByIdAsync(recordId);
                if (record == null)
                {
                    return (false, "保养记录不存在");
                }

                var plan = await _planService.GetPlanByIdAsync(planId);
                if (plan == null)
                {
                    return (false, "保养计划不存在");
                }

                record.MaintenancePlanId = planId;
                record.UpdatedAt = DateTime.Now;

                await _context.Db.Updateable(record).ExecuteCommandAsync();
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, $"关联记录到计划失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 完成保养记录并更新计划
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> CompleteRecordAndUpdatePlanAsync(int recordId, MaintenanceCompletionData completionData)
        {
            try
            {
                var record = await GetRecordByIdAsync(recordId);
                if (record == null)
                {
                    return (false, "保养记录不存在");
                }

                // 更新记录完成信息
                record.MaintenanceResult = completionData.MaintenanceResult;
                record.ActualDuration = completionData.ActualDuration;
                record.MaintenanceCost = completionData.MaintenanceCost;
                record.FoundIssues = completionData.FoundIssues;
                record.SolutionDescription = completionData.SolutionDescription;
                record.UsedMaterials = completionData.UsedMaterials;
                record.UsedTools = completionData.UsedTools;
                record.AfterMaintenanceStatus = completionData.AfterMaintenanceStatus;
                record.UpdatedAt = DateTime.Now;

                await _context.Db.Updateable(record).ExecuteCommandAsync();

                // 如果有关联的保养计划，更新计划的下次保养日期
                if (record.MaintenancePlanId.HasValue)
                {
                    await _planService.UpdateNextMaintenanceDateAsync(record.MaintenancePlanId.Value, record.MaintenanceDate);
                }

                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, $"完成记录失败：{ex.Message}");
            }
        }

        #endregion

        #region 统计分析

        /// <summary>
        /// 获取保养记录统计信息
        /// </summary>
        public async Task<MaintenanceRecordStatistics> GetRecordStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = _context.MaintenanceRecords;

            if (startDate.HasValue)
            {
                query = query.Where(r => r.MaintenanceDate >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(r => r.MaintenanceDate <= endDate.Value);
            }

            var records = await query.ToListAsync();

            var durations = records.Where(r => r.ActualDuration.HasValue).Select(r => r.ActualDuration.Value).ToList();
            var costs = records.Where(r => r.MaintenanceCost.HasValue).Select(r => r.MaintenanceCost.Value).ToList();

            return new MaintenanceRecordStatistics
            {
                TotalRecords = records.Count,
                CompletedRecords = records.Count(r => r.ReviewStatus == 2),
                InProgressRecords = records.Count(r => r.Status == 2),
                OverdueRecords = records.Count(r => r.MaintenanceDate < DateTime.Now && r.Status != 3),
                PendingReviewRecords = records.Count(r => r.ReviewStatus == 1),
                AverageMaintenanceDuration = durations.Any() ? (int)durations.Average() : 0,
                TotalDuration = durations.Any() ? (int)durations.Sum() : 0,
                MaxDuration = durations.Any() ? (int)durations.Max() : 0,
                MinDuration = durations.Any() ? (int)durations.Min() : 0,
                TotalMaintenanceCost = records.Sum(r => r.MaintenanceCost ?? 0),
                AverageCost = costs.Any() ? costs.Average() : 0,
                MaxCost = costs.Any() ? costs.Max() : 0,
                MinCost = costs.Any() ? costs.Min() : 0,
                CompletionRate = records.Count > 0 ? (decimal)records.Count(r => r.Status == 3) / records.Count * 100 : 0,
                OnTimeRate = records.Count > 0 ? (decimal)records.Count(r => r.MaintenanceDate <= DateTime.Now && r.Status == 3) / records.Count * 100 : 0,
                SuccessRate = records.Count > 0 ? (decimal)records.Count(r => r.MaintenanceResult == 1) / records.Count * 100 : 0,
                MaintenanceTypeStatistics = records.GroupBy(r => r.MaintenanceType)
                    .ToDictionary(g => g.Key, g => g.Count()),
                MaintenanceResultStatistics = records.GroupBy(r => r.MaintenanceResult)
                    .ToDictionary(g => g.Key, g => g.Count())
            };
        }

        /// <summary>
        /// 获取成本统计
        /// </summary>
        public async Task<MaintenanceCostStatistics> GetCostStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null, int? departmentId = null)
        {
            var query = _context.MaintenanceRecords
                .Where(r => r.ReviewStatus == 2);

            if (startDate.HasValue)
            {
                query = query.Where(r => r.MaintenanceDate >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(r => r.MaintenanceDate <= endDate.Value);
            }

            // TODO: 添加部门筛选逻辑
            // if (departmentId.HasValue)
            // {
            //     query = query.Where(r => r.DepartmentId == departmentId.Value);
            // }

            var records = await query.ToListAsync();
            var costs = records.Where(r => r.MaintenanceCost.HasValue).Select(r => r.MaintenanceCost.Value).ToList();

            return new MaintenanceCostStatistics
            {
                TotalCost = records.Sum(r => r.MaintenanceCost ?? 0),
                AverageCost = costs.Any() ? costs.Average() : 0,
                MaxCost = costs.Any() ? costs.Max() : 0,
                MinCost = costs.Any() ? costs.Min() : 0,
                PreventiveCost = records.Where(r => r.MaintenanceType == 1).Sum(r => r.MaintenanceCost ?? 0),
                CorrectiveCost = records.Where(r => r.MaintenanceType == 2).Sum(r => r.MaintenanceCost ?? 0),
                PredictiveCost = records.Where(r => r.MaintenanceType == 3).Sum(r => r.MaintenanceCost ?? 0),
                RecordCount = records.Count,
                CostByMaintenanceType = records.GroupBy(r => r.MaintenanceType)
                    .ToDictionary(g => g.Key, g => g.Sum(r => r.MaintenanceCost ?? 0)),
                MonthlyCostTrend = records.GroupBy(r => r.MaintenanceDate.ToString("yyyy-MM"))
                    .ToDictionary(g => g.Key, g => g.Sum(r => r.MaintenanceCost ?? 0))
            };
        }

        /// <summary>
        /// 获取效率统计
        /// </summary>
        public async Task<MaintenanceEfficiencyStatistics> GetEfficiencyStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null, int? personId = null)
        {
            var query = _context.MaintenanceRecords
                .Where(r => r.ReviewStatus == 2);

            if (startDate.HasValue)
            {
                query = query.Where(r => r.MaintenanceDate >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(r => r.MaintenanceDate <= endDate.Value);
            }

            if (personId.HasValue)
            {
                query = query.Where(r => r.MaintenancePersonId == personId.Value);
            }

            var records = await query.ToListAsync();

            if (!records.Any())
            {
                return new MaintenanceEfficiencyStatistics();
            }

            var avgPlanned = records.Where(r => r.EstimatedDuration.HasValue).Any() ?
                (int)records.Where(r => r.EstimatedDuration.HasValue).Average(r => r.EstimatedDuration.Value) : 0;
            var avgActual = records.Where(r => r.ActualDuration.HasValue).Any() ?
                (int)records.Where(r => r.ActualDuration.HasValue).Average(r => r.ActualDuration.Value) : 0;

            return new MaintenanceEfficiencyStatistics
            {
                AveragePlannedDuration = avgPlanned,
                AverageActualDuration = avgActual,
                DurationEfficiency = avgPlanned > 0 ? (decimal)avgActual / avgPlanned : 0,
                OnTimeCompletionRate = records.Count > 0 ?
                    (decimal)records.Count(r => r.MaintenancePlanId.HasValue) / records.Count * 100 : 0,
                FirstTimePassRate = records.Count > 0 ?
                    (decimal)records.Count(r => r.MaintenanceResult == 1) / records.Count * 100 : 0
            };
        }

        /// <summary>
        /// 获取保养效率分析
        /// </summary>
        public async Task<MaintenanceEfficiencyAnalysis> GetMaintenanceEfficiencyAnalysisAsync(DateTime startDate, DateTime endDate)
        {
            var records = await _context.MaintenanceRecords
                .Where(r => r.MaintenanceDate >= startDate && r.MaintenanceDate <= endDate && r.ReviewStatus == 2)
                .ToListAsync();

            var analysis = new MaintenanceEfficiencyAnalysis
            {
                StartDate = startDate,
                EndDate = endDate,
                TotalRecords = records.Count,
                OnTimeRecords = records.Count(r => r.MaintenanceDate <= r.ScheduledDate),
                DelayedRecords = records.Count(r => r.MaintenanceDate > r.ScheduledDate),
                AverageDuration = records.Where(r => r.ActualDuration.HasValue).Any() ? records.Where(r => r.ActualDuration.HasValue).Average(r => r.ActualDuration.Value) : 0,
                AverageCost = records.Where(r => r.MaintenanceCost.HasValue).Any() ? records.Where(r => r.MaintenanceCost.HasValue).Average(r => r.MaintenanceCost.Value) : 0,
                TotalCost = records.Sum(r => r.MaintenanceCost ?? 0),
                SuccessRate = records.Count > 0 ? (double)records.Count(r => r.MaintenanceResult == 1) / records.Count * 100 : 0,
                FailureRate = records.Count > 0 ? (double)records.Count(r => r.MaintenanceResult != 1) / records.Count * 100 : 0
            };

            // 计算最高效和最低效的保养人员
            var personnelStats = records
                .Where(r => r.ActualDuration.HasValue)
                .GroupBy(r => r.MaintenancePersonName)
                .Select(g => new
                {
                    PersonName = g.Key ?? "未知",
                    AvgDuration = g.Average(r => r.ActualDuration.Value),
                    Count = g.Count()
                })
                .Where(p => p.Count >= 3) // 至少3次记录才参与统计
                .ToList();

            if (personnelStats.Any())
            {
                var mostEfficient = personnelStats.OrderBy(p => p.AvgDuration).First();
                analysis.MostEfficientPerson = mostEfficient.PersonName;
                analysis.MostEfficientPersonAvgDuration = mostEfficient.AvgDuration;

                var leastEfficient = personnelStats.OrderByDescending(p => p.AvgDuration).First();
                analysis.LeastEfficientPerson = leastEfficient.PersonName;
                analysis.LeastEfficientPersonAvgDuration = leastEfficient.AvgDuration;
            }

            return analysis;
        }

        /// <summary>
        /// 获取部件保养历史分析
        /// </summary>
        public async Task<ComponentMaintenanceHistory> GetComponentMaintenanceHistoryAsync(int componentId, int months = 12)
        {
            var startDate = DateTime.Now.AddMonths(-months);
            var endDate = DateTime.Now;

            var records = await _context.MaintenanceRecords
                .Where(r => r.ComponentId == componentId && r.ReviewStatus == 2 && r.MaintenanceDate >= startDate)
                .OrderByDescending(r => r.MaintenanceDate)
                .ToListAsync();

            var component = await _context.EquipmentComponents
                .Where(c => c.Id == componentId)
                .FirstAsync();

            if (component == null)
            {
                throw new ArgumentException($"部件ID {componentId} 不存在");
            }

            var history = new ComponentMaintenanceHistory
            {
                ComponentId = componentId,
                ComponentName = component.Name,
                ComponentCode = component.Code,
                ComponentModel = component.Model ?? string.Empty,
                ComponentSpecifications = component.Specifications ?? string.Empty,
                ComponentType = component.ComponentType,
                IsCritical = component.IsCritical,
                StartDate = startDate,
                EndDate = endDate,
                TotalMaintenanceCount = records.Count,
                PreventiveMaintenanceCount = records.Count(r => r.MaintenanceType == 1),
                CorrectiveMaintenanceCount = records.Count(r => r.MaintenanceType == 2),
                EmergencyMaintenanceCount = records.Count(r => r.MaintenanceType == 4),
                TotalMaintenanceDuration = records.Where(r => r.ActualDuration.HasValue).Sum(r => r.ActualDuration.Value),
                TotalMaintenanceCost = records.Sum(r => r.MaintenanceCost ?? 0),
                SuccessfulMaintenanceCount = records.Count(r => r.MaintenanceResult == 1),
                OnTimeCompletionCount = records.Count(r => r.MaintenanceDate <= DateTime.Now),
                FirstMaintenanceDate = records.LastOrDefault()?.MaintenanceDate,
                LastMaintenanceDate = records.FirstOrDefault()?.MaintenanceDate,
                MaintenanceRecords = records.Take(50).Select(r => new MaintenanceRecordSummary
                {
                    RecordId = r.Id,
                    MaintenanceDate = r.MaintenanceDate,
                    MaintenanceType = r.MaintenanceType,
                    MaintenanceTypeName = GetMaintenanceTypeName(r.MaintenanceType),
                    MaintenancePersonName = r.MaintenancePersonName ?? string.Empty,
                    ActualDuration = r.ActualDuration ?? 0,
                    ActualCost = r.MaintenanceCost ?? 0,
                    MaintenanceResult = r.MaintenanceResult,
                    MaintenanceResultName = GetMaintenanceResultName(r.MaintenanceResult)
                }).ToList()
            };

            return history;
        }

        #endregion

        #region 报表和导出

        /// <summary>
        /// 获取保养趋势分析
        /// </summary>
        public async Task<List<MaintenanceTrendData>> GetMaintenanceTrendAnalysisAsync(int months = 12, int? maintenanceType = null)
        {
            var startDate = DateTime.Now.AddMonths(-months);
            var endDate = DateTime.Now;

            var query = _context.MaintenanceRecords
                .Where(r => r.MaintenanceDate >= startDate && r.MaintenanceDate <= endDate && r.ReviewStatus == 2);

            if (maintenanceType.HasValue)
            {
                query = query.Where(r => r.MaintenanceType == maintenanceType.Value);
            }

            var records = await query.ToListAsync();

            return records.GroupBy(r => new { r.MaintenanceDate.Year, r.MaintenanceDate.Month })
                .Select(g => new MaintenanceTrendData
                {
                    Date = new DateTime(g.Key.Year, g.Key.Month, 1),
                    RecordCount = g.Count(),
                    TotalCost = g.Sum(r => r.MaintenanceCost ?? 0),
                    AverageDuration = (int)(g.Where(r => r.ActualDuration.HasValue).Any() ? g.Where(r => r.ActualDuration.HasValue).Average(r => r.ActualDuration.Value) : 0),
                    SuccessRate = g.Count() > 0 ? (decimal)g.Count(r => r.MaintenanceResult == 1) / g.Count() * 100 : 0,
                    OnTimeRate = g.Count() > 0 ? (decimal)g.Count(r => r.MaintenanceDate <= r.ScheduledDate) / g.Count() * 100 : 0,
                    ActivePersonnelCount = g.Select(r => r.MaintenancePersonId).Distinct().Count()
                }).OrderBy(t => t.Date).ToList();
        }

        /// <summary>
        /// 生成保养报告
        /// </summary>
        public async Task<MaintenanceReport> GenerateMaintenanceReportAsync(DateTime startDate, DateTime endDate, MaintenanceReportType reportType, Dictionary<string, object>? parameters = null)
        {
            var query = _context.MaintenanceRecords
                .Where(r => r.MaintenanceDate >= startDate && r.MaintenanceDate <= endDate && r.ReviewStatus == 2);

            // 根据参数进行筛选
            if (parameters != null)
            {
                if (parameters.ContainsKey("ComponentId") && parameters["ComponentId"] is int componentId)
                {
                    query = query.Where(r => r.ComponentId == componentId);
                }

                if (parameters.ContainsKey("DepartmentId") && parameters["DepartmentId"] is int departmentId)
                {
                    // TODO: 添加部门筛选逻辑
                    // query = query.Where(r => r.DepartmentId == departmentId);
                }
            }

            var records = await query.ToListAsync();
            var days = (endDate - startDate).Days;

            return new MaintenanceReport
            {
                ReportType = reportType,
                Title = $"保养报告 ({startDate:yyyy-MM-dd} 至 {endDate:yyyy-MM-dd})",
                ReportData = new
                {
                    ReportPeriod = $"{startDate:yyyy-MM-dd} 至 {endDate:yyyy-MM-dd}",
                    TotalRecords = records.Count,
                    Statistics = await GetRecordStatisticsAsync(startDate, endDate),
                    TrendAnalysis = await GetMaintenanceTrendAnalysisAsync(months: (endDate - startDate).Days / 30),
                    EfficiencyAnalysis = await GetMaintenanceEfficiencyAnalysisAsync(startDate, endDate)
                },
                GeneratedAt = DateTime.Now
            };
        }

        /// <summary>
        /// 导出保养记录
        /// </summary>
        public async Task<List<MaintenanceRecord>> ExportRecordsAsync(DateTime? startDate = null, DateTime? endDate = null, int? componentId = null, int? maintenanceType = null)
        {
            return await SearchRecordsAsync(startDate: startDate, endDate: endDate, componentId: componentId, maintenanceType: maintenanceType);
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 生成保养记录编号
        /// </summary>
        public async Task<string> GenerateRecordNumberAsync(int maintenanceType, DateTime maintenanceDate)
        {
            var typePrefix = maintenanceType switch
            {
                1 => "PM", // 预防性保养
                2 => "CM", // 纠正性保养
                3 => "PD", // 预测性保养
                _ => "MR"  // 一般保养记录
            };

            var datePrefix = maintenanceDate.ToString("yyyyMMdd");
            var prefix = $"{typePrefix}{datePrefix}";

            var lastRecord = await _context.MaintenanceRecords
                .Where(r => r.RecordNumber.StartsWith(prefix))
                .OrderByDescending(r => r.RecordNumber)
                .FirstAsync();

            if (lastRecord != null && lastRecord.RecordNumber.Length > prefix.Length)
            {
                var numberPart = lastRecord.RecordNumber.Substring(prefix.Length);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    return $"{prefix}{(lastNumber + 1):D3}";
                }
            }

            return $"{prefix}001";
        }

        /// <summary>
        /// 获取保养类型名称
        /// </summary>
        private string GetMaintenanceTypeName(int maintenanceType)
        {
            return maintenanceType switch
            {
                1 => "预防性保养",
                2 => "纠正性保养",
                3 => "预测性保养",
                4 => "紧急保养",
                _ => "未知类型"
            };
        }

        /// <summary>
        /// 获取保养结果名称
        /// </summary>
        private string GetMaintenanceResultName(int maintenanceResult)
        {
            return maintenanceResult switch
            {
                1 => "成功",
                2 => "部分成功",
                3 => "失败",
                _ => "未知结果"
            };
        }

        #endregion
    }
}
