namespace CoreHub.Shared.Models
{
    /// <summary>
    /// 保养成本统计数据
    /// </summary>
    public class MaintenanceCostStatistics
    {
        /// <summary>
        /// 总成本
        /// </summary>
        public decimal TotalCost { get; set; }

        /// <summary>
        /// 预防性保养成本
        /// </summary>
        public decimal PreventiveCost { get; set; }

        /// <summary>
        /// 纠正性保养成本
        /// </summary>
        public decimal CorrectiveCost { get; set; }

        /// <summary>
        /// 预测性保养成本
        /// </summary>
        public decimal PredictiveCost { get; set; }

        /// <summary>
        /// 应急保养成本
        /// </summary>
        public decimal EmergencyCost { get; set; }

        /// <summary>
        /// 平均单次保养成本
        /// </summary>
        public decimal AverageCostPerRecord { get; set; }

        /// <summary>
        /// 记录数量
        /// </summary>
        public int RecordCount { get; set; }

        /// <summary>
        /// 按月份分组的成本统计
        /// </summary>
        public Dictionary<string, decimal> MonthlyCosts { get; set; } = new Dictionary<string, decimal>();

        /// <summary>
        /// 按部门分组的成本统计
        /// </summary>
        public Dictionary<string, decimal> DepartmentCosts { get; set; } = new Dictionary<string, decimal>();

        /// <summary>
        /// 按设备部件分组的成本统计
        /// </summary>
        public Dictionary<string, decimal> ComponentCosts { get; set; } = new Dictionary<string, decimal>();
    }
}
