namespace CoreHub.Shared.Models.Common
{
    /// <summary>
    /// 设备部件筛选条件
    /// </summary>
    public class EquipmentComponentFilter : SearchFilter
    {
        /// <summary>
        /// 部件分类
        /// </summary>
        public string? Category { get; set; }

        /// <summary>
        /// 部件类型
        /// </summary>
        public int? ComponentType { get; set; }

        /// <summary>
        /// 部件状态
        /// </summary>
        public new int? Status { get; set; }

        /// <summary>
        /// 是否库存不足
        /// </summary>
        public bool? IsLowStock { get; set; }

        /// <summary>
        /// 是否为关键部件
        /// </summary>
        public bool? IsCritical { get; set; }

        /// <summary>
        /// 库存状态
        /// </summary>
        public int? StockStatus { get; set; }

        /// <summary>
        /// 最小价格
        /// </summary>
        public decimal? MinPrice { get; set; }

        /// <summary>
        /// 最大价格
        /// </summary>
        public decimal? MaxPrice { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public new bool? IsEnabled { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        public string? Supplier { get; set; }

        /// <summary>
        /// 最小库存量范围 - 最小值
        /// </summary>
        public int? MinStockLevelMin { get; set; }

        /// <summary>
        /// 最小库存量范围 - 最大值
        /// </summary>
        public int? MinStockLevelMax { get; set; }

        /// <summary>
        /// 单价范围 - 最小值
        /// </summary>
        public decimal? UnitPriceMin { get; set; }

        /// <summary>
        /// 单价范围 - 最大值
        /// </summary>
        public decimal? UnitPriceMax { get; set; }

        /// <summary>
        /// 创建时间范围 - 开始时间
        /// </summary>
        public DateTime? CreatedAtStart { get; set; }

        /// <summary>
        /// 创建时间范围 - 结束时间
        /// </summary>
        public DateTime? CreatedAtEnd { get; set; }
    }
}
