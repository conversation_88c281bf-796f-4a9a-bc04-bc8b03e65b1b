namespace CoreHub.Shared.Models
{
    /// <summary>
    /// 保养趋势数据
    /// </summary>
    public class MaintenanceTrendData
    {
        /// <summary>
        /// 日期
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// 记录数量
        /// </summary>
        public int RecordCount { get; set; }

        /// <summary>
        /// 总成本
        /// </summary>
        public decimal TotalCost { get; set; }

        /// <summary>
        /// 平均时长（分钟）
        /// </summary>
        public int AverageDuration { get; set; }

        /// <summary>
        /// 成功率（百分比）
        /// </summary>
        public decimal SuccessRate { get; set; }

        /// <summary>
        /// 按时完成率（百分比）
        /// </summary>
        public decimal OnTimeRate { get; set; }

        /// <summary>
        /// 活跃人员数量
        /// </summary>
        public int ActivePersonnelCount { get; set; }

        /// <summary>
        /// 预防性保养数量
        /// </summary>
        public int PreventiveMaintenanceCount { get; set; }

        /// <summary>
        /// 纠正性保养数量
        /// </summary>
        public int CorrectiveMaintenanceCount { get; set; }

        /// <summary>
        /// 预测性保养数量
        /// </summary>
        public int PredictiveMaintenanceCount { get; set; }

        /// <summary>
        /// 应急保养数量
        /// </summary>
        public int EmergencyMaintenanceCount { get; set; }

        /// <summary>
        /// 平均每次保养成本
        /// </summary>
        public decimal AverageCostPerRecord { get; set; }

        /// <summary>
        /// 设备可用性（百分比）
        /// </summary>
        public decimal EquipmentAvailability { get; set; }

        /// <summary>
        /// 故障率（百分比）
        /// </summary>
        public decimal FailureRate { get; set; }
    }
}
