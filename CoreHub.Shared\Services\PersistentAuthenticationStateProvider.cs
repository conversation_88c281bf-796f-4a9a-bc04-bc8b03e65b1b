using Microsoft.AspNetCore.Components.Authorization;
using System.Security.Claims;
using System.Text.Json;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 持久化认证状态提供器
    /// 继承自 .NET 原生的 AuthenticationStateProvider，支持跨平台状态持久化
    /// </summary>
    public class PersistentAuthenticationStateProvider : AuthenticationStateProvider
    {
        private readonly IAuthenticationStateStorage _storage;
        private ClaimsPrincipal _currentUser = new ClaimsPrincipal(new ClaimsIdentity());
        private readonly string _storageKey = "authState";

        public PersistentAuthenticationStateProvider(IAuthenticationStateStorage storage)
        {
            _storage = storage;
        }

        public override async Task<AuthenticationState> GetAuthenticationStateAsync()
        {
            try
            {
                // 尝试从存储中恢复认证状态
                if (_currentUser.Identity?.IsAuthenticated != true)
                {
                    await TryRestoreAuthenticationStateAsync();
                }

                return new AuthenticationState(_currentUser);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取认证状态失败: {ex.Message}");
                return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
            }
        }

        /// <summary>
        /// 标记用户为已认证状态
        /// </summary>
        public async Task MarkUserAsAuthenticatedAsync(ClaimsPrincipal user)
        {
            _currentUser = user;
            await SaveAuthenticationStateAsync();
            NotifyAuthenticationStateChanged(Task.FromResult(new AuthenticationState(user)));
        }

        /// <summary>
        /// 标记用户为未认证状态
        /// </summary>
        public async Task MarkUserAsLoggedOutAsync()
        {
            _currentUser = new ClaimsPrincipal(new ClaimsIdentity());
            await ClearAuthenticationStateAsync();
            NotifyAuthenticationStateChanged(Task.FromResult(new AuthenticationState(_currentUser)));
        }

        /// <summary>
        /// 保存认证状态到本地存储
        /// </summary>
        private async Task SaveAuthenticationStateAsync()
        {
            try
            {
                if (_currentUser.Identity?.IsAuthenticated == true && _storage.IsAvailable)
                {
                    var authData = new StoredAuthData
                    {
                        UserId = _currentUser.FindFirst("UserId")?.Value ?? "",
                        Username = _currentUser.FindFirst(ClaimTypes.Name)?.Value ?? "",
                        Email = _currentUser.FindFirst(ClaimTypes.Email)?.Value ?? "",
                        DisplayName = _currentUser.FindFirst("DisplayName")?.Value ?? "",
                        Role = _currentUser.FindFirst("Role")?.Value ?? "",
                        AllRoles = _currentUser.Claims
                            .Where(c => c.Type == "Role")
                            .Select(c => c.Value)
                            .ToList(),
                        Permissions = _currentUser.Claims
                            .Where(c => c.Type == "Permission")
                            .Select(c => c.Value)
                            .ToList(),
                        SavedAt = DateTime.UtcNow
                    };

                    var json = JsonSerializer.Serialize(authData);
                    await _storage.SaveAuthStateAsync(_storageKey, json);
                    
                    System.Diagnostics.Debug.WriteLine($"认证状态保存成功: {authData.Username}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存认证状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从本地存储恢复认证状态
        /// </summary>
        private async Task TryRestoreAuthenticationStateAsync()
        {
            try
            {
                if (_storage.IsAvailable)
                {
                    var json = await _storage.GetAuthStateAsync(_storageKey);
                    
                    if (!string.IsNullOrEmpty(json))
                    {
                        var authData = JsonSerializer.Deserialize<StoredAuthData>(json);
                        
                        if (authData != null && IsValidAuthData(authData))
                        {
                            var claims = new List<Claim>
                            {
                                new Claim(ClaimTypes.Name, authData.Username),
                                new Claim(ClaimTypes.Email, authData.Email),
                                new Claim("UserId", authData.UserId),
                                new Claim("DisplayName", authData.DisplayName),
                                new Claim("Role", authData.Role)
                            };

                            // 添加所有角色声明
                            foreach (var role in authData.AllRoles)
                            {
                                claims.Add(new Claim("Role", role));
                            }

                            foreach (var permission in authData.Permissions)
                            {
                                claims.Add(new Claim("Permission", permission));
                            }

                            var identity = new ClaimsIdentity(claims, "persistent");
                            _currentUser = new ClaimsPrincipal(identity);
                            
                            System.Diagnostics.Debug.WriteLine($"认证状态恢复成功: {authData.Username}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("认证状态已过期或无效，清除存储");
                            await _storage.ClearAuthStateAsync(_storageKey);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"恢复认证状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清除本地存储的认证状态
        /// </summary>
        private async Task ClearAuthenticationStateAsync()
        {
            try
            {
                if (_storage.IsAvailable)
                {
                    await _storage.ClearAuthStateAsync(_storageKey);
                    System.Diagnostics.Debug.WriteLine("认证状态清除成功");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清除认证状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证存储的认证数据是否有效
        /// </summary>
        private bool IsValidAuthData(StoredAuthData authData)
        {
            // 检查数据是否过期（例如：7天）
            var expirationTime = authData.SavedAt.AddDays(7);
            if (DateTime.UtcNow > expirationTime)
            {
                return false;
            }

            // 检查必要字段
            return !string.IsNullOrEmpty(authData.Username) && 
                   !string.IsNullOrEmpty(authData.DisplayName);
        }

        /// <summary>
        /// 存储的认证数据结构
        /// </summary>
        private class StoredAuthData
        {
            public string UserId { get; set; } = "";
            public string Username { get; set; } = "";
            public string Email { get; set; } = "";
            public string DisplayName { get; set; } = "";
            public string Role { get; set; } = "";
            public List<string> AllRoles { get; set; } = [];
            public List<string> Permissions { get; set; } = [];
            public DateTime SavedAt { get; set; }
        }
    }
} 