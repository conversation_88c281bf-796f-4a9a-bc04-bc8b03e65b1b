@page "/maintenance-plan-management"
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Models.Common
@using CoreHub.Shared.Services
@using CoreHub.Shared.Components.Common
@using CoreHub.Shared.Components
@using CoreHub.Shared.Extensions
@using MudBlazor
@inject IMaintenancePlanService PlanService
@inject IEquipmentComponentService ComponentService
@inject IDialogService DialogService
@inject ISnackbar Snackbar

<PageTitle>保养计划管理</PageTitle>

<PermissionView RequiredPermission="MaintenancePlanManagement.View">
    <ChildContent>
        <MudContainer MaxWidth="MaxWidth.False" Class="pa-4">
            <MudText Typo="Typo.h4" Class="mb-4">保养计划管理</MudText>
    
    <!-- 视图切换 -->
    <MudPaper Class="pa-4 mb-4" Elevation="1">
        <MudGrid AlignItems="Center">
            <MudItem xs="12" sm="6">
                <MudToggleGroup @bind-Value="_currentView" Color="Color.Primary" CheckMark="true">
                    <MudToggleItem Value="ViewType.List" Icon="Icons.Material.Filled.List" Text="列表视图" />
                    <MudToggleItem Value="ViewType.Calendar" Icon="Icons.Material.Filled.CalendarMonth" Text="日历视图" />
                </MudToggleGroup>
            </MudItem>
            <MudItem xs="12" sm="6" Class="d-flex justify-end">
                <MudButtonGroup Variant="Variant.Filled">
                    <MudButton StartIcon="Icons.Material.Filled.Add"
                              Color="Color.Primary"
                              OnClick="OpenCreateDialog">
                        新增计划
                    </MudButton>
                    <MudButton StartIcon="Icons.Material.Filled.Schedule"
                              Color="Color.Secondary"
                              OnClick="ShowTodayPlans">
                        今日计划
                    </MudButton>
                    <MudButton StartIcon="Icons.Material.Filled.Warning"
                              Color="Color.Warning"
                              OnClick="ShowOverduePlans">
                        逾期计划
                    </MudButton>
                </MudButtonGroup>
            </MudItem>
        </MudGrid>
    </MudPaper>

    @if (_currentView == ViewType.List)
    {
        <!-- 列表视图 -->
        <!-- 简化的搜索区域 -->
        <MudPaper Class="pa-4 mb-4" Elevation="2">
            <MudGrid AlignItems="Center">
                <MudItem xs="12" sm="6" md="4">
                    <MudTextField @bind-Value="_filter.Keyword"
                                 Label="搜索关键词"
                                 Placeholder="搜索计划名称、部件名称..."
                                 Variant="Variant.Outlined"
                                 Adornment="Adornment.End"
                                 AdornmentIcon="Icons.Material.Filled.Search"
                                 OnAdornmentClick="SearchPlans"
                                 Clearable="true" />
                </MudItem>
                <MudItem xs="12" sm="6" md="2">
                    <MudButton StartIcon="Icons.Material.Filled.Search"
                              OnClick="SearchPlans"
                              Color="Color.Primary"
                              Variant="Variant.Filled">
                        搜索
                    </MudButton>
                </MudItem>
                <MudItem xs="12" sm="6" md="2">
                    <MudButton StartIcon="Icons.Material.Filled.Clear"
                              OnClick="ClearFilters"
                              Color="Color.Secondary"
                              Variant="Variant.Outlined">
                        清除
                    </MudButton>
                </MudItem>
            </MudGrid>
        </MudPaper>

        <!-- 数据表格 -->
        @if (_isLoading)
        {
            <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
            <MudText>正在加载...</MudText>
        }
        else if (_hasError)
        {
            <MudAlert Severity="Severity.Error">
                @_errorMessage
                <MudButton OnClick="LoadPlans" Color="Color.Primary" Variant="Variant.Text">重试</MudButton>
            </MudAlert>
        }
        else if (_pagedResult?.Items?.Any() != true)
        {
            <MudPaper Class="pa-8 text-center">
                <MudText Typo="Typo.h6">暂无保养计划</MudText>
                <MudText>点击上方"新增计划"按钮创建第一个保养计划</MudText>
                <MudButton OnClick="OpenCreateDialog" Color="Color.Primary" Variant="Variant.Filled" Class="mt-4">
                    新增计划
                </MudButton>
            </MudPaper>
        }
        else
        {
            <MudTable Items="@_pagedResult?.Items"
                      Hover="true"
                      Striped="true"
                      Dense="true"
                      FixedHeader="true"
                      Height="600px">
                <HeaderContent>
                    <MudTh>计划名称</MudTh>
                    <MudTh>部件信息</MudTh>
                    <MudTh>保养类型</MudTh>
                    <MudTh>周期设置</MudTh>
                    <MudTh>下次保养</MudTh>
                    <MudTh>优先级</MudTh>
                    <MudTh>负责人</MudTh>
                    <MudTh>状态</MudTh>
                    <MudTh>操作</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd DataLabel="计划名称">
                        <MudText Typo="Typo.body2">@context.PlanName</MudText>
                        @if (!string.IsNullOrEmpty(context.Description))
                        {
                            <MudText Typo="Typo.caption" Color="Color.Secondary">@context.Description.Truncate(50)</MudText>
                        }
                    </MudTd>
                    <MudTd DataLabel="部件信息">
                        <MudText Typo="Typo.body2">@context.ComponentName</MudText>
                        <MudText Typo="Typo.caption" Color="Color.Secondary">@context.ComponentCode</MudText>
                    </MudTd>
                    <MudTd DataLabel="保养类型">
                        <MudChip T="string" Size="Size.Small" Color="@GetMaintenanceTypeColor(context.MaintenanceType)">
                            @GetMaintenanceTypeName(context.MaintenanceType)
                        </MudChip>
                    </MudTd>
                    <MudTd DataLabel="周期设置">
                        <MudText Typo="Typo.body2">@context.CycleValue @GetCycleTypeName(context.CycleType)</MudText>
                    </MudTd>
                    <MudTd DataLabel="下次保养">
                        @if (context.NextMaintenanceDate.HasValue)
                        {
                            var isOverdue = context.NextMaintenanceDate.Value < DateTime.Today;
                            <MudText Typo="Typo.body2" Color="@(isOverdue ? Color.Error : Color.Default)">
                                @context.NextMaintenanceDate.Value.ToDateString()
                            </MudText>
                            @if (isOverdue)
                            {
                                <MudChip T="string" Size="Size.Small" Color="Color.Error" Icon="Icons.Material.Filled.Warning">逾期</MudChip>
                            }
                        }
                        else
                        {
                            <MudText Typo="Typo.body2" Color="Color.Secondary">未设置</MudText>
                        }
                    </MudTd>
                    <MudTd DataLabel="优先级">
                        <MudChip T="string" Size="Size.Small" Color="@GetPriorityColor(context.Priority)">
                            @GetPriorityName(context.Priority)
                        </MudChip>
                    </MudTd>
                    <MudTd DataLabel="负责人">@context.ResponsiblePersonName</MudTd>
                    <MudTd DataLabel="状态">
                        <MudChip T="string" Size="Size.Small" Color="@(context.IsActive ? Color.Success : Color.Default)">
                            @(context.IsActive ? "启用" : "禁用")
                        </MudChip>
                    </MudTd>
                    <MudTd DataLabel="操作">
                        <MudButtonGroup Size="Size.Small" Variant="Variant.Text">
                            <MudIconButton Icon="Icons.Material.Filled.PlayArrow"
                                          Color="Color.Success"
                                          Size="Size.Small"
                                          OnClick="() => ExecutePlan(context)"
                                          Title="执行保养" />
                            <MudIconButton Icon="Icons.Material.Filled.Edit"
                                          Color="Color.Primary"
                                          Size="Size.Small"
                                          OnClick="() => OpenEditDialog(context)" />
                            <MudIconButton Icon="Icons.Material.Filled.Visibility"
                                          Color="Color.Info"
                                          Size="Size.Small"
                                          OnClick="() => ViewPlanDetails(context)" />
                            <MudIconButton Icon="Icons.Material.Filled.Delete"
                                          Color="Color.Error"
                                          Size="Size.Small"
                                          OnClick="() => DeletePlan(context)" />
                        </MudButtonGroup>
                    </MudTd>
                </RowTemplate>
            </MudTable>
        }

        <!-- 分页组件 -->
        @if (_pagedResult != null && _pagedResult.Items?.Any() == true)
        {
            <MudPaper Class="pa-4 mt-4">
                <MudGrid AlignItems="Center" Justify="Justify.SpaceBetween">
                    <MudItem>
                        <MudText>共 @_pagedResult.TotalCount 条记录，第 @_pagedResult.PageIndex 页，共 @_pagedResult.TotalPages 页</MudText>
                    </MudItem>
                    <MudItem>
                        <MudButtonGroup>
                            <MudButton Disabled="@(_pagedResult.PageIndex <= 1)" OnClick="() => OnPageChanged(_pagedResult.PageIndex - 1)">上一页</MudButton>
                            <MudButton Disabled="@(_pagedResult.PageIndex >= _pagedResult.TotalPages)" OnClick="() => OnPageChanged(_pagedResult.PageIndex + 1)">下一页</MudButton>
                        </MudButtonGroup>
                    </MudItem>
                </MudGrid>
            </MudPaper>
        }
    }
    else
    {
        <!-- 日历视图 -->
        <MudPaper Class="pa-4" Elevation="2">
            <MudText Typo="Typo.h6" Class="mb-4">保养计划日历</MudText>
            <!-- TODO: 实现日历组件 -->
            <div class="text-center pa-8">
                <MudIcon Icon="Icons.Material.Filled.CalendarMonth" Size="Size.Large" Color="Color.Secondary" />
                <MudText Typo="Typo.h6" Color="Color.Secondary" Class="mt-2">日历视图开发中...</MudText>
                <MudText Typo="Typo.body2" Color="Color.Secondary">即将支持日历形式查看和管理保养计划</MudText>
            </div>
        </MudPaper>
    }
</MudContainer>

@code {
    private enum ViewType { List, Calendar }

    private ViewType _currentView = ViewType.List;
    private MaintenancePlanFilter _filter = new();
    private PagedResult<MaintenancePlan>? _pagedResult;
    private bool _isLoading = true;
    private bool _hasError = false;
    private string? _errorMessage;

    protected override async Task OnInitializedAsync()
    {
        await LoadPlans();
    }

    private async Task LoadPlans()
    {
        try
        {
            _isLoading = true;
            _hasError = false;
            StateHasChanged();

            _pagedResult = await PlanService.SearchPlansAsync(_filter);
        }
        catch (Exception ex)
        {
            _hasError = true;
            _errorMessage = ex.Message;
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private async Task SearchPlans()
    {
        _filter.PageIndex = 1;
        await LoadPlans();
    }

    private async Task ClearFilters()
    {
        _filter.Keyword = null;
        _filter.PageIndex = 1;
        await LoadPlans();
    }

    private async Task OnPageChanged(int pageIndex)
    {
        _filter.PageIndex = pageIndex;
        await LoadPlans();
    }

    private async Task OnPageSizeChanged(int pageSize)
    {
        _filter.PageSize = pageSize;
        _filter.PageIndex = 1;
        await LoadPlans();
    }

    private async Task ShowTodayPlans()
    {
        _filter.Keyword = null;
        _filter.PageIndex = 1;
        _filter.NextMaintenanceStartDate = DateTime.Today;
        _filter.NextMaintenanceEndDate = DateTime.Today;
        await LoadPlans();
    }

    private async Task ShowOverduePlans()
    {
        _filter.Keyword = null;
        _filter.PageIndex = 1;
        _filter.IsOverdue = true;
        await LoadPlans();
    }

    private async Task OpenCreateDialog()
    {
        var parameters = new DialogParameters { ["IsEdit"] = false };
        var options = new DialogOptions { CloseButton = true, MaxWidth = MaxWidth.Medium, FullWidth = true };
        
        var dialog = await DialogService.ShowAsync<MaintenancePlanDialog>("新增保养计划", parameters, options);
        var result = await dialog.Result;
        
        if (!result.Canceled)
        {
            await LoadPlans();
            Snackbar.Add("保养计划创建成功", Severity.Success);
        }
    }

    private async Task OpenEditDialog(MaintenancePlan plan)
    {
        var parameters = new DialogParameters 
        { 
            ["IsEdit"] = true,
            ["PlanId"] = plan.Id
        };
        var options = new DialogOptions { CloseButton = true, MaxWidth = MaxWidth.Medium, FullWidth = true };
        
        var dialog = await DialogService.ShowAsync<MaintenancePlanDialog>("编辑保养计划", parameters, options);
        var result = await dialog.Result;
        
        if (!result.Canceled)
        {
            await LoadPlans();
            Snackbar.Add("保养计划更新成功", Severity.Success);
        }
    }

    private async Task ViewPlanDetails(MaintenancePlan plan)
    {
        var parameters = new DialogParameters { ["PlanId"] = plan.Id };
        var options = new DialogOptions { CloseButton = true, MaxWidth = MaxWidth.Large, FullWidth = true };
        
        await DialogService.ShowAsync<MaintenancePlanDetailsDialog>("保养计划详情", parameters, options);
    }

    private async Task ExecutePlan(MaintenancePlan plan)
    {
        var parameters = new DialogParameters { ["PlanId"] = plan.Id };
        var options = new DialogOptions { CloseButton = true, MaxWidth = MaxWidth.Medium, FullWidth = true };
        
        var dialog = await DialogService.ShowAsync<ExecuteMaintenanceDialog>("执行保养", parameters, options);
        var result = await dialog.Result;
        
        if (!result.Canceled)
        {
            await LoadPlans();
            Snackbar.Add("保养记录创建成功", Severity.Success);
        }
    }

    private async Task DeletePlan(MaintenancePlan plan)
    {
        var confirmed = await DialogService.ShowDeleteConfirmAsync(
            plan.PlanName, 
            "删除后将无法恢复，相关的保养记录将保留但不再关联到此计划。");
        
        if (confirmed)
        {
            try
            {
                await PlanService.DeletePlanAsync(plan.Id);
                await LoadPlans();
                Snackbar.Add("保养计划删除成功", Severity.Success);
            }
            catch (Exception ex)
            {
                Snackbar.Add($"删除失败: {ex.Message}", Severity.Error);
            }
        }
    }

    private string GetMaintenanceTypeName(int type)
    {
        return type switch
        {
            1 => "预防性保养",
            2 => "纠正性保养",
            3 => "预测性保养",
            4 => "紧急维修",
            _ => "其他"
        };
    }

    private Color GetMaintenanceTypeColor(int type)
    {
        return type switch
        {
            1 => Color.Primary,
            2 => Color.Warning,
            3 => Color.Info,
            4 => Color.Error,
            _ => Color.Default
        };
    }

    private string GetCycleTypeName(int type)
    {
        return type switch
        {
            1 => "天",
            2 => "周",
            3 => "月",
            4 => "年",
            5 => "次",
            6 => "小时",
            7 => "件",
            _ => ""
        };
    }

    private string GetPriorityName(int priority)
    {
        return priority switch
        {
            1 => "低",
            2 => "中",
            3 => "高",
            4 => "紧急",
            _ => "未知"
        };
    }

    private Color GetPriorityColor(int priority)
    {
        return priority switch
        {
            1 => Color.Default,
            2 => Color.Info,
            3 => Color.Warning,
            4 => Color.Error,
            _ => Color.Default
        };
    }
}
    </ChildContent>
</PermissionView>
