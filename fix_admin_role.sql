-- 修复管理员角色代码
-- 将角色代码从"系统管理员"改为"Administrator"

-- 首先查看当前的角色数据
SELECT 'Current Roles:' AS Info;
SELECT Id, Code, Name, SortOrder, IsEnabled FROM Roles ORDER BY SortOrder;

-- 查看admin用户当前的角色分配
SELECT 'Admin User Current Roles:' AS Info;
SELECT 
    u.Username,
    r.Code AS RoleCode,
    r.Name AS RoleName,
    ur.IsEnabled,
    ur.ExpiresAt
FROM Users u
INNER JOIN UserRoles ur ON u.Id = ur.UserId
INNER JOIN Roles r ON ur.RoleId = r.Id
WHERE u.Username = 'admin';

-- 更新角色代码：将"系统管理员"改为"Administrator"
UPDATE Roles 
SET Code = 'Administrator' 
WHERE Code = '系统管理员' OR Name = '系统管理员';

-- 确保有一个标准的Administrator角色
IF NOT EXISTS (SELECT 1 FROM Roles WHERE Code = 'Administrator')
BEGIN
    INSERT INTO Roles (Code, Name, Description, SortOrder, IsEnabled, IsSystem, CreatedAt, UpdatedAt)
    VALUES ('Administrator', '系统管理员', '拥有系统全部权限的管理员角色', 1, 1, 1, GETDATE(), GETDATE());
END

-- 查看更新后的角色数据
SELECT 'Updated Roles:' AS Info;
SELECT Id, Code, Name, SortOrder, IsEnabled FROM Roles ORDER BY SortOrder;

-- 查看admin用户更新后的角色分配
SELECT 'Admin User Updated Roles:' AS Info;
SELECT 
    u.Username,
    r.Code AS RoleCode,
    r.Name AS RoleName,
    ur.IsEnabled,
    ur.ExpiresAt
FROM Users u
INNER JOIN UserRoles ur ON u.Id = ur.UserId
INNER JOIN Roles r ON ur.RoleId = r.Id
WHERE u.Username = 'admin';
